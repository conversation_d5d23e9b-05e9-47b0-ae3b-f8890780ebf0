import React from "react";

import { act, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { expect, test } from "vitest";

import { NumberFieldProps, NumberType } from "../src";
import {
  ControlledRender,
  DebounceRender,
  OnlyTriggerChangeWhenBlurRender
} from "../src/components/NumberField/storyHelpers/numberFieldComponents";
import { formatDecimalPlaces } from "../src/components/NumberField/storyHelpers/numberFieldHelpers";

const placeholderText = "Placeholder7876";
const defaultArgs: NumberFieldProps = {
  label: "Label",
  name: "number-field",
  placeholder: placeholderText,
  width: "100",
  extraDecimalPlacesForPercentage: 2
};

describe("NumberField", () => {
  test("extra decimals should be cleared with format", async () => {
    render(
      <OnlyTriggerChangeWhenBlurRender
        {...defaultArgs}
        type={NumberType.NUMBER}
        format={formatDecimalPlaces(1)}
        allowedDecimalPlaces={1}
      />
    );
    const input = screen.getByPlaceholderText(placeholderText);
    await act(async () => {
      /* fire events that update state */
      await userEvent.type(input, "123.1");
      input.blur();
    });
    /* assert on the output */
    expect(input).toHaveValue("123.1");
    expect(screen.getByRole("value")).toHaveTextContent("123.1");

    await act(async () => {
      await userEvent.clear(input);
      await userEvent.type(input, "123.111");
      input.blur();
    });

    expect(input).toHaveValue("123.1");
    expect(screen.getByRole("value")).toHaveTextContent("123.1");
  });

  test("format should work", async () => {
    render(
      <OnlyTriggerChangeWhenBlurRender
        {...defaultArgs}
        type={NumberType.NUMBER}
        format={formatDecimalPlaces(1)}
        allowedDecimalPlaces={1}
      />
    );
    const input = screen.getByPlaceholderText(placeholderText);
    await act(async () => {
      await userEvent.type(input, "5.111");
      input.blur();
    });

    expect(input).toHaveValue("5.1");
    expect(screen.getByRole("value")).toHaveTextContent("5.1");
  });

  test("percentage should work", async () => {
    render(<ControlledRender {...defaultArgs} type={NumberType.PERCENTAGE} />);
    const input = screen.getByPlaceholderText(placeholderText);
    await act(async () => {
      await userEvent.type(input, "5.111");
    });

    expect(input).toHaveValue("5.111");
    expect(screen.getByRole("value")).toHaveTextContent("0.05111");
  });

  test("percentage should work with debounce", async () => {
    render(<DebounceRender {...defaultArgs} type={NumberType.PERCENTAGE} />);
    const input = screen.getByPlaceholderText(placeholderText);
    await act(async () => {
      await userEvent.type(input, "55.123");
      // debounce delay
      await new Promise(resolve => setTimeout(resolve, 400));
    });

    expect(input).toHaveValue("55.123");
    expect(screen.getByRole("value")).toHaveTextContent("0.55123");
  });

  // not currently used in otai - i.e. combo of on change and format isn't used
  test.skip("percentage should work with debounce and format", async () => {
    render(
      <DebounceRender
        {...defaultArgs}
        type={NumberType.PERCENTAGE}
        format={formatDecimalPlaces(3)}
        allowedDecimalPlaces={3}
      />
    );
    const input = screen.getByPlaceholderText(placeholderText);
    await act(async () => {
      await userEvent.type(input, "55.123");
      // debounce delay
      await new Promise(resolve => setTimeout(resolve, 400));
      // blurring input will trigger the format
    });

    expect(input).toHaveValue("55.1");
    expect(screen.getByRole("value")).toHaveTextContent("0.551");
  });

  test("percentage should work with format on blur", async () => {
    render(
      <OnlyTriggerChangeWhenBlurRender
        {...defaultArgs}
        type={NumberType.PERCENTAGE}
        format={formatDecimalPlaces(3)}
        allowedDecimalPlaces={3}
      />
    );
    const input = screen.getByPlaceholderText(placeholderText);
    await act(async () => {
      await userEvent.type(input, "55.123");
      input.blur();
    });

    expect(input).toHaveValue("55.1");
    expect(screen.getByRole("value")).toHaveTextContent("0.551");
  });

  test("percentage should show full incorrect default value", async () => {
    render(
      <ControlledRender
        {...defaultArgs}
        type={NumberType.PERCENTAGE}
        value="0.25101"
        format={formatDecimalPlaces(3)}
        allowedDecimalPlaces={3}
      />
    );
    const input = screen.getByPlaceholderText(placeholderText);
    // this is needed because we want the field to reflect the actual value,
    // and external error validation (otai) should pick it up
    expect(input).toHaveValue("25.101");
    expect(screen.getByRole("value")).toHaveTextContent("0.25101");
  });

  test("percentage should have same value on blur twice", async () => {
    render(
      <ControlledRender
        {...defaultArgs}
        type={NumberType.PERCENTAGE}
        format={formatDecimalPlaces(3)}
        allowedDecimalPlaces={3}
      />
    );
    const input = screen.getByPlaceholderText(placeholderText);
    await act(async () => {
      await userEvent.type(input, "55.1");
      input.blur();
      input.blur(); // blur again to check if value remains the same
    });

    expect(input).toHaveValue("55.1");
    expect(screen.getByRole("value")).toHaveTextContent("0.551");
  });

  describe("step up buttons", () => {
    test("should increment value", async () => {
      render(<ControlledRender {...defaultArgs} type={NumberType.NUMBER} />);
      const input = screen.getByPlaceholderText(placeholderText);
      const stepUpButton = screen.getByRole("button", {
        name: "keyboard_arrow_up"
      });

      await act(async () => {
        await userEvent.type(input, "5");
        stepUpButton.click();
      });

      await waitFor(async () => {
        await userEvent.tab();
      });

      expect(input).toHaveValue("6");
      expect(screen.getByRole("value")).toHaveTextContent("6");
    });

    test("should increment value with percentage type", async () => {
      render(
        <OnlyTriggerChangeWhenBlurRender
          {...defaultArgs}
          type={NumberType.PERCENTAGE}
          format={formatDecimalPlaces(3)}
          allowedDecimalPlaces={3}
          step={Math.pow(10, -1)}
        />
      );
      const input = screen.getByPlaceholderText(placeholderText);
      const stepUpButton = screen.getByRole("button", {
        name: "keyboard_arrow_up"
      });

      await act(async () => {
        await userEvent.type(input, "1.9");
        stepUpButton.click();
        await new Promise(resolve => setTimeout(resolve, 400));
      });

      await waitFor(async () => {
        await userEvent.tab();
      });

      expect(input).toHaveValue("2.0");
      expect(screen.getByRole("value")).toHaveTextContent("0.020");
    });
  });
});
