@import url("https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;500;600;700&display=swap");

// CSS Reset
*,
*::before,
*::after {
  box-sizing: border-box;
}
:root {
  --components-navigation-side-width: 184px;
}
* {
  margin: 0;
}
body {
  font-family:
    var(--font-family), Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.25;
  color: #191716;
  overflow: hidden;
}
img,
picture,
video,
canvas,
input,
button,
textarea,
select {
  font: inherit;
}
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}
h1,
h2,
h3,
h4,
button,
input {
  line-height: 1.25;
}

h1,
h2,
h3,
h4 {
  text-wrap: balance;
}
#root,
#__next {
  isolation: isolate;
}

.whiteboard-page-body-template {
  .floating-content-panel__content {
    height: 80vh;
    max-height: 700px;
    width: 25vw;
    min-width: 300px;
    max-width: 500px;
  }
}

.floating-with-parent {
  z-index: 20 !important;
}
