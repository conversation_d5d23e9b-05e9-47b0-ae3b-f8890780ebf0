import {
  MAX_KEY_LENGTH,
  MAX_NAME_LENGTH,
  MIN_KEY_LENGTH,
  MIN_NAME_LENGTH
} from "@src/constants/errorMessages";
import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { DocError, WorkspaceDocument } from "@src/types/documentTypes.ts";

type ErrorResourceMap = {
  forms: string;
  formQuestions: string;
  foundations: string;
  series: string;
  flows: string;
};

export type ErrorFieldKeyMap = {
  accessor: string;
  message: string;
  field: string;
};

type LengthConstraint = {
  min: number;
  max: number;
};

type ErrorKeyToLengthConstraint = { [key: string]: LengthConstraint };

type GetDictionaryMessagesForErrorsArgs = {
  prefix: string;
  resourceName?: string;
  index?: number; // only used for foundations as explained below
  includeOnlyOneLevelDepth?: boolean;
  labelForErrorMessage?: string;
};

export class DocumentErrorHelper {
  private readonly errors: DocError[];
  private readonly d: Dictionary;
  private readonly document: WorkspaceDocument | undefined;
  private readonly errorResourceMap: ErrorResourceMap = {
    forms: "configurationForm",
    formQuestions: "configurationForm.question",
    foundations: "foundation",
    series: "configurationSeries",
    flows: "configurationFlow"
  };
  private readonly errorKeyToLengthConstraint: ErrorKeyToLengthConstraint = {
    key: {
      min: MIN_KEY_LENGTH,
      max: MAX_KEY_LENGTH
    },
    name: {
      min: MIN_NAME_LENGTH,
      max: MAX_NAME_LENGTH
    }
  };

  constructor(document: WorkspaceDocument | undefined, d: Dictionary) {
    this.d = d;
    this.document = document;
    this.errors = document?.errors ?? [];
  }

  show() {
    return this.errors; // for debugging
  }

  showFiltered() {
    const filteredErrors = this.filterErrors();
    if (!filteredErrors) {
      return [];
    }
    return filteredErrors;
  }

  hasErrors() {
    const filteredErrors = this.filterErrors();
    if (!filteredErrors) {
      return false;
    }
    return filteredErrors?.length > 0;
  }

  private filterErrors(): DocError[] {
    const inactiveFlowIds = this.getInactiveFlowIds();
    return this.errors.filter(error => {
      if (inactiveFlowIds.size === 0) {
        return true;
      }
      const flowId = error.path.split(".").at(3); // $.flows.entities.id
      return flowId ? !inactiveFlowIds.has(flowId) : true;
    });
  }

  private getInactiveFlowIds(): Set<string> {
    const inactiveFlows: Set<string> = new Set();
    if (!this.document) {
      return inactiveFlows;
    }

    const flows = this.document.flows;
    for (const flow in flows.entities) {
      const flowData = flows.entities[flow];
      if (flowData.status === "inactive") {
        inactiveFlows.add(flow);
      }
    }
    return inactiveFlows;
  }

  private getErrorsByPrefix(prefix: string, includeOnlyOneLevelDepth = true) {
    if (!prefix || prefix == "") {
      return [];
    }
    return this.errors?.filter(error => {
      if (!includeOnlyOneLevelDepth) {
        return error.path.startsWith(prefix);
      }
      if (!error.path.startsWith(prefix)) {
        return false;
      }
      const isMoreThanOneLevel = error.path
        .split(prefix)?.[1]
        ?.replace(/^(\.properties\.|\.)/, "")
        ?.includes(".");
      // the replace function is used to remove the first dot or .properties. from the string
      // eg: if includeOnlyOneLevelDepth = true & prefix = $.series.seriesId
      // excluded error paths: $.series.seriesId.intervals.entities...
      // included error paths: $.series.seriesId.name, $.series.seriesId

      return !isMoreThanOneLevel;
    });
  }

  getErrorCountByPrefix(prefix: string, includeOnlyOneLevelDepth = false) {
    return this.getErrorsByPrefix(prefix, includeOnlyOneLevelDepth).length;
  }

  getErrorCountOfErroneousEntitiesByPrefix(prefix: string) {
    const errors = this.getErrorsByPrefix(prefix, false);
    const erroneousEntities = new Set<string>();

    errors?.forEach(error => {
      const pathParts = error.path.split(".");
      // $.foundations.entities.id.errorName...
      // $.flows.entities.id.errorName...
      // $.series.seriesId.errorName...
      // $.forms.id.errorName...
      const entityIndex =
        prefix.includes("foundations") || prefix.includes("flows") ? 3 : 2;
      const entity = pathParts[entityIndex] ?? null;

      if (entity) {
        erroneousEntities.add(entity);
      }
    });

    return erroneousEntities.size;
  }

  private getDictionaryKeyForError(
    error: DocError,
    resourceName?: string,
    labelForErrorMessage?: string
  ): string {
    switch (error.type) {
      case "minLength":
        return this.d("errors.common.length", {
          name: labelForErrorMessage ?? error.key,
          min: error.constraintDetail,
          max: this.errorKeyToLengthConstraint[
            error.key as keyof ErrorKeyToLengthConstraint
          ].max
        });
      case "maxLength":
        return this.d("errors.common.length", {
          name: labelForErrorMessage ?? error.key,
          min: this.errorKeyToLengthConstraint[
            error.key as keyof ErrorKeyToLengthConstraint
          ].min,
          max: error.constraintDetail
        });
      case "required":
        return this.d("errors.common.required", {
          name: labelForErrorMessage ?? error.key
        });
      case "pattern": {
        let constraint;
        if (error.key == "identifier") {
          constraint = this.d(
            "errors.configurationForm.question.identifier.constraint"
          );
        } else {
          constraint = this.d("errors.common.constraint");
        }

        return this.d("errors.common.alphanumeric", {
          name: labelForErrorMessage ?? error.key,
          constraint
        }); // currently only for error.key = "key"
      }
      case "duplicate":
        return this.d("errors.common.duplicate", {
          name: labelForErrorMessage ?? error.key
        });
      case "inactiveFlow":
        return this.d("errors.common.inactiveFlow", {
          name: labelForErrorMessage ?? error.key
        });
    }
    const resource =
      this.errorResourceMap[resourceName as keyof ErrorResourceMap] ?? "common";
    const keyPrefix = `errors.${resource}`;
    return this.d(`${keyPrefix}.${error.key}.${error.type}`);
  }

  getDictionaryMessagesForErrors({
    prefix,
    resourceName,
    index, // only used for foundations as explained below
    includeOnlyOneLevelDepth = true,
    labelForErrorMessage
  }: GetDictionaryMessagesForErrorsArgs): ErrorFieldKeyMap[] | null {
    const errors = this.getErrorsByPrefix(prefix, includeOnlyOneLevelDepth);
    const dictionaryKeys: ErrorFieldKeyMap[] = [];
    errors?.forEach(error => {
      dictionaryKeys.push({
        accessor: index ? `${resourceName}.${index}.${error.key}` : "", // accessor is used by foundations, because foundations uses an overall form with array of objects
        message: this.getDictionaryKeyForError(
          error,
          resourceName,
          labelForErrorMessage
        ),
        field: error.key // field is used by series and forms
      });
    });
    return dictionaryKeys;
  }
}
