import { get } from "lodash";

export const matchesSearchTerm = ({
  searchTerm,
  obj,
  keysToMatch
}: {
  searchTerm: string;
  obj: object;
  keysToMatch: (string | string[])[];
}) => {
  const searchWords = searchTerm.toLowerCase().trim().split(" ");
  const allWords = keysToMatch
    .map(key => {
      const value = get(obj, key) ?? "";
      if (!["string", "number", "boolean"].includes(typeof value)) {
        return "";
      }
      return value.toString();
    })
    .join(" ")
    .toLowerCase();

  return searchWords.every(term => allWords.includes(term));
};
