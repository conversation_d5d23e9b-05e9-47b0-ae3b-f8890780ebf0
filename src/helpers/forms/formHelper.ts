import { FormAPI } from "@helpers/searchHelper";
import { WorkspaceConfigurationHelper } from "@helpers/workspace/WorkspaceConfigurationHelper.ts";

import { CollectionsFoundationsTabs } from "@pages/collection/home/<USER>";
import { HomeHelper } from "@pages/collection/home/<USER>";

import { ExistingForm } from "@src/types/Form";
import { ConfigurationFormType } from "@src/types/FormConfiguration.ts";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  JSONQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import { ListQuestionProperties } from "@src/types/QuestionProperties.ts";
import {
  AlertType,
  FormAnswer,
  JsonAnswer,
  ListAnswer,
  TableAnswer
} from "@src/types/collection/CollectionForm.ts";

// Check if content is a question or a section
export const isQuestion = (
  content: ConfigurationFormType["content"][0]
): boolean => {
  return content && "text" in content;
};

export const isQuestionContent = (content: ConfigurationFormType["content"]) =>
  isQuestion(content?.[0]);

const isValidAnswerValue = (value: unknown) => {
  return (
    value !== undefined &&
    value !== "" &&
    value !== null &&
    !(Array.isArray(value) && value.length == 0)
  );
};

const getRequiredColumns = (question: Question<TableQuestionProperties>) => {
  const questionColumns = question.properties?.columns;

  return questionColumns
    ?.filter(col => col.properties?.required)
    .map((col: { id: string }) => col.id);
};

const hasAllRequiredColumnsFilled = (
  answer: FormAnswer | undefined,
  requiredColIds: string[] | undefined
): boolean => {
  const tableAnswer = answer?.value as TableAnswer;
  if (!tableAnswer || !requiredColIds) {
    return false;
  }

  const { entities, order } = tableAnswer;

  // check if each row has all required columns filled
  const hasAllRequiredColumnsFilled = order.every(rowId => {
    const row = entities[rowId];
    return requiredColIds.every(colId =>
      isValidAnswerValue(row?.columns?.[colId]?.value)
    );
  });

  if (!hasAllRequiredColumnsFilled) {
    return false;
  }

  return true;
};

const hasAtLeastOneColumnFilled = (answer?: FormAnswer): boolean => {
  const tableAnswer = answer?.value as TableAnswer;
  if (!tableAnswer) {
    return false;
  }

  const { entities, order } = tableAnswer;

  // check if each row has at least one columnIds filled
  const hasSomeColumnsFilled = order.every(rowId => {
    const row = entities[rowId];
    return Object.values(row?.columns ?? {}).some(columnIds =>
      isValidAnswerValue(columnIds.value)
    );
  });

  if (!hasSomeColumnsFilled) {
    return false;
  }

  return true;
};

// TODO: Add more checks for different types of questions
export const isQuestionAnswered = (
  question?: Question,
  answer?: FormAnswer
) => {
  if (question?.type === QuestionTypes.TABLE) {
    const requiredColIds = getRequiredColumns(
      question as Question<TableQuestionProperties>
    );
    if (requiredColIds?.length) {
      return hasAllRequiredColumnsFilled(answer, requiredColIds);
    } else {
      return hasAtLeastOneColumnFilled(answer);
    }
  }

  if (question?.type === QuestionTypes.LIST) {
    const listQuestion = question as Question<ListQuestionProperties>;
    const listItemQuestion = listQuestion.properties?.items[0];
    if (!listItemQuestion) {
      return false;
    }

    const listAnswer = answer?.value as ListAnswer;
    const listItemAnswers = Object.values(listAnswer?.entities ?? {});
    const answerLength = listItemAnswers.length;
    if (!answerLength) {
      return false;
    }

    const minLength = listQuestion?.properties?.minLength ?? 0;
    if (minLength > answerLength) {
      return false;
    }

    return listItemAnswers.every(listAnswerItem => {
      const itemAnswer = listAnswerItem.item?.[listItemQuestion.id];
      return isValidAnswerValue(itemAnswer?.value);
    });
  }

  if (question?.type === QuestionTypes.JSON) {
    const jsonQuestion = question as Question<JSONQuestionProperties>;
    const jsonAnswer = answer?.value as JsonAnswer;

    return (jsonQuestion.properties?.items ?? []).every(
      (item: Question): boolean => {
        if (item.properties?.hidden) {
          return true;
        }

        const itemAnswer = jsonAnswer?.[item.id] as FormAnswer;
        if (
          item.type === QuestionTypes.JSON ||
          item.type === QuestionTypes.LIST
        ) {
          // Recursively check nested JSON/LIST
          return isQuestionAnswered(item, itemAnswer);
        }

        return isValidAnswerValue(itemAnswer?.value);
      }
    );
  }

  // all other question types
  return isValidAnswerValue(answer?.value);
};
export type QuestionAlert = {
  message: string;
  type: AlertType;
};

export const getFormUrl = ({
  form,
  workspaceVersion,
  tab,
  homeHelper,
  foundationHierarchyPath
}: {
  form: ExistingForm | FormAPI;
  workspaceVersion: WorkspaceConfigurationHelper;
  tab: string;
  homeHelper?: HomeHelper;
  foundationHierarchyPath?: string;
}) => {
  if (!form || !workspaceVersion) {
    return {
      basePath: "",
      formIdentifier: ""
    };
  }
  const formConfigurationKey =
    workspaceVersion.getFormConfiguration(form.formConfigurationId)?.key ?? "";

  let formIdentifier = `${form.id}-${formConfigurationKey}`;

  if (form.intervalId && form.intervalId !== null) {
    formIdentifier += `-${workspaceVersion.getIntervalById(form.intervalId)?.name}`;
  }

  const basePath = (() => {
    if (foundationHierarchyPath) {
      return foundationHierarchyPath;
    }
    return tab === CollectionsFoundationsTabs.SELECTED_FORMS
      ? homeHelper?.buildPath()
      : homeHelper?.nextPath(form.foundation.key);
  })();

  if (!basePath) {
    return {
      basePath: "",
      formIdentifier: ""
    };
  }

  return { basePath, formIdentifier };
};
