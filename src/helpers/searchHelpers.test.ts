import { matchesSearchTerm } from "./searchHelpers";

describe("SearchHelpers", () => {
  it("should match with search term", () => {
    const searchTerm = "test search";
    const obj = {
      title: "This is a test",
      properties: { description: "Searching for test cases" }
    };
    expect(
      matchesSearchTerm({
        searchTerm,
        obj,
        keysToMatch: ["title", "properties.description"]
      })
    ).toBe(true);
  });

  it("should not match with search term", () => {
    const searchTerm = "hello";
    const obj = {
      title: "This is a test",
      properties: { description: "Searching for test cases" }
    };
    expect(
      matchesSearchTerm({
        searchTerm,
        obj,
        keysToMatch: ["title", "properties.description"]
      })
    ).toBe(false);
  });

  it("should match number with search term", () => {
    const searchTerm = "test 100";
    const obj = {
      title: "This is a test",
      properties: { description: 100 }
    };
    expect(
      matchesSearchTerm({
        searchTerm,
        obj,
        keysToMatch: ["title", "properties.description"]
      })
    ).toBe(true);
  });

  it("should match boolean with search term", () => {
    const searchTerm = "test true";
    const obj = {
      title: "This is a test",
      properties: { description: true }
    };
    expect(
      matchesSearchTerm({
        searchTerm,
        obj,
        keysToMatch: ["title", "properties.description"]
      })
    ).toBe(true);
  });
});

it("should not match object  with search term", () => {
  const searchTerm = "test value";
  const obj = {
    title: "This is a test",
    properties: {
      description: {
        nested: "value"
      }
    }
  };
  expect(
    matchesSearchTerm({
      searchTerm,
      obj,
      // "properties.description" is not the correct path to a primitive value
      keysToMatch: ["title", "properties.description"]
    })
  ).toBe(false);
});
