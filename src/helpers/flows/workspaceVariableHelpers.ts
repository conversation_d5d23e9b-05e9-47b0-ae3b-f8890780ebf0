import { Dictionary } from "@src/hooks/useDictionary";
import { MockFlowContext } from "@src/types/FlowConfiguration/FlowConfiguration";
import { WorkspaceDocument } from "@src/types/documentTypes";

export const WorkspacePrefix = "_workspace_";

const WORKSPACE_VARIABLES = {
  workspaceId: {
    type: "workspaceConfiguration",
    identifier: "WorkspaceId"
  },
  workspaceKey: {
    type: "workspaceConfiguration",
    identifier: "WorkspaceKey"
  },
  workspaceName: {
    type: "workspaceConfiguration",
    identifier: "WorkspaceName"
  },
  variables: {
    type: "workspaceVariables",
    identifier: "Variables"
  }
};

export const getWorkspaceVariablePath = (
  key: keyof typeof WORKSPACE_VARIABLES
) => {
  const config = WORKSPACE_VARIABLES[key];
  const path = `workspace.${key}`;

  // Determine type based on identifier - if it contains "Id", return "number"
  const type = config.identifier.includes("Id") ? "number" : "string";

  return {
    path,
    type
  };
};

export const getWorkspaceVariableDefinitions = ({
  flowContext,
  d
}: {
  flowContext?: MockFlowContext;
  document: WorkspaceDocument;
  d: Dictionary;
}) => {
  if (flowContext) {
    const variables = Object.entries(WORKSPACE_VARIABLES).reduce(
      (acc, [key, config]) => ({
        ...acc,
        [`${WorkspacePrefix}.${key}`]: config
      }),
      {}
    );

    flowContext.variables = {
      ...flowContext.variables,
      ...variables
    };
  }
};
