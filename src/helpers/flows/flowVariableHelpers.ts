import { runBooleanConditionCheck } from "@helpers/conditionHelper";

import { Dictionary } from "@src/hooks/useDictionary";
import {
  FlowConfiguration,
  MockFlowContext,
  MockVariable
} from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  FlowStep,
  FlowStepVariant,
  flowStepVariantsWithTypeConfiguration
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  FlowVariantStepProperties,
  IteratorStepProperties,
  IteratorStepType,
  SetVariablesStepProperties
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import {
  ActionFlowStepTypeConfigurationPropertiesConfiguration,
  FlowStepTypeConfiguration,
  IteratorFlowStepTypeConfigurationPropertiesConfiguration,
  TriggerFlowStepTypeConfigurationPropertiesConfiguration
} from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";
import { TriggerSubscription } from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/TriggerEvent";
import {
  Variable,
  VariableConfiguration,
  VariableIdentifier,
  VariableTypeDefinition
} from "@src/types/FlowConfiguration/Variables";
import { Question } from "@src/types/Question";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import {
  getLocalStepContext,
  populateRealValuesUsingMockFlowContext
} from "./flowHelpers";
import { getVariableTypeDefinition } from "./flowVariableTypeHelpers";

export const getVariableMappingsFromStepTypeConfiguration = ({
  variant,
  flowStepTypeConfig
}: {
  variant?: `${FlowStepVariant}`;
  flowStepTypeConfig: FlowStepTypeConfiguration;
}): Variable[] => {
  if (variant === FlowStepVariant.TRIGGER) {
    return (
      (
        Object.values(
          (
            flowStepTypeConfig.properties
              .configuration as TriggerFlowStepTypeConfigurationPropertiesConfiguration
          ).subscribeTo ?? {}
        ) ?? []
      ).flatMap((triggerSubscription: TriggerSubscription) => {
        return triggerSubscription.variableMappings;
      }) ?? []
    );
  }

  if (variant === FlowStepVariant.ACTION) {
    return (
      (
        flowStepTypeConfig.properties
          .configuration as ActionFlowStepTypeConfigurationPropertiesConfiguration
      ).variableMappings ?? []
    );
  }

  if (variant === FlowStepVariant.ITERATOR) {
    return (
      (
        flowStepTypeConfig.properties
          .configuration as IteratorFlowStepTypeConfigurationPropertiesConfiguration
      ).variableMappings ?? []
    );
  }

  return [];
};

export const getVariablesForStep = ({
  step,
  flowContext,
  workspaceDocument,

  // optional
  flowStepTypeConfig,
  variablesByPath = {}
  // includeNestedStepVariables = false
}: {
  step?: FlowStep;
  flowContext?: MockFlowContext;
  workspaceDocument: WorkspaceDocument;

  flowStepTypeConfig?: FlowStepTypeConfiguration;
  includeNestedStepVariables?: boolean;
  variablesByPath?: {
    [path: string]: VariableTypeDefinition;
  };
  flowConfiguration?: FlowConfiguration;
}): {
  variables: MockVariable[];
  stepOutputVariables?: VariableIdentifier[];
} => {
  if (!step || !flowContext) {
    return {
      variables: [],
      stepOutputVariables: []
    };
  }
  const localStepContext = getLocalStepContext({
    step,
    flowContext
  });
  const variables: MockVariable[] = [];
  const stepOutputVariables = [];
  if (step.variant === FlowStepVariant.SET_VARIABLES) {
    if ((step.properties as SetVariablesStepProperties).variables) {
      variables.push(
        ...(step.properties as SetVariablesStepProperties).variables.map(
          variable => ({
            ...variable,
            sourceStepId: step.id,
            availableFromStepId: step.id,
            sourceStepVariant: step.variant,
            isStartingVariable: false,
            isEndingVariable: false,
            configuration: {
              identifier: variable.identifier,
              type: variable.type,
              properties: variable.properties ?? {}
            },
            stepIds: [step.id]
          })
        )
      );
    }
  } else if (
    flowStepVariantsWithTypeConfiguration.includes(
      step.variant as FlowStepVariant
    )
  ) {
    if (!flowStepTypeConfig) {
      return {
        variables: []
      };
    }

    const variableMappings = getVariableMappingsFromStepTypeConfiguration({
      variant: step.variant,
      flowStepTypeConfig
    })
      .map(variableMapping => {
        const variable = populateRealValuesUsingMockFlowContext(
          variableMapping,
          localStepContext
        ) as Variable;
        return {
          ...variable,
          sourceStepId: step.id,
          availableFromStepId: step.next === "" ? step.id : step.next,
          sourceStepVariant: step.variant,
          isStartingVariable: step.variant === FlowStepVariant.TRIGGER,
          isEndingVariable: false,
          isConstant: true,
          configuration: {
            identifier: variable.identifier,
            type: variable.type,
            properties: variable.properties ?? {}
          },
          value: undefined
          // Not adding stepIds here as variable mappings are an output and not counted as "use" of a variable
        };
      })
      .filter(variable => {
        if (!variable.properties?.hidden) {
          return true;
        }
        const hidden = runBooleanConditionCheck(variable.properties.hidden);
        return !hidden;
      });

    if (
      step.variant !== FlowStepVariant.ITERATOR ||
      (step as FlowStep<IteratorStepProperties>).properties
        .typePrimaryIdentifier !== IteratorStepType.AGGREGATE
    ) {
      variables.push(...variableMappings);
    }
    stepOutputVariables.push(
      ...variableMappings.map(variable => variable.identifier)
    );
  }

  if (step.variant === FlowStepVariant.ITERATOR) {
    const iteratorStartingVariables = getIteratorStartingVariables(
      step as FlowStep<IteratorStepProperties>,
      variablesByPath
    );

    const iteratorStep = step as FlowStep<IteratorStepProperties>;

    variables.push(
      ...(Object.values(iteratorStartingVariables) ?? []).map(variable => ({
        ...variable,
        sourceStepId: iteratorStep.properties.configuration.start,
        availableFromStepId: iteratorStep.properties.configuration.start,
        sourceStepVariant: step.variant,
        isStartingVariable: false,
        isEndingVariable: false,
        stepIds: [step.id],
        configuration: {
          identifier: variable.identifier,
          type: variable.type,
          properties: variable.properties ?? {}
        },
        iteratorParentId: iteratorStep.id
      }))
    );

    // TODO: add all the variables that are used in the iterator if includeNestedStepVariables is true
    // for each nested step - getVariablesForStep
    // Object.entries(iteratorConfiguration.steps).forEach(
    //   ([nestedStepId, nestedStep]) => {
    //     const nestedStepVariables = getVariablesForStep({
    //       step: nestedStep,
    //       // flowStepTypeConfig: nestedStep.properties.typeConfiguration,
    //       flowContext,
    //       includeNestedStepVariables
    //     });
    //     variables.push(...nestedStepVariables.variables);
    //     // stepOutputVariables.push(
    //     //   ...(nestedStepVariables.stepOutputVariables ?? [])
    //     // );
    //   }
    // );
  }
  if (step.variant === FlowStepVariant.FLOW) {
    const childFlowConfigurationId = (
      step.properties as FlowVariantStepProperties
    ).inputs?.flowConfigurationId;

    if (childFlowConfigurationId) {
      const childFlow =
        workspaceDocument?.flows?.entities?.[childFlowConfigurationId ?? ""];

      if (childFlow) {
        variables.push(
          ...(childFlow?.endingVariables ?? []).map(variable => ({
            ...variable,
            sourceStepId: step.id,
            availableFromStepId: step.next,
            sourceStepVariant: step.variant,
            isStartingVariable: false,
            isEndingVariable: false,
            stepIds: [step.id],
            isConstant: true,
            configuration: {
              identifier: variable.identifier,
              type: variable.type,
              properties: variable.properties ?? {}
            },
            value: undefined
          }))
        );

        stepOutputVariables.push(
          ...(childFlow?.endingVariables ?? []).map(
            variable => variable.identifier
          )
        );
      }
    }
  }

  return { variables, stepOutputVariables };
};

export const getIteratorStartingVariables = (
  iteratorStep: FlowStep<IteratorStepProperties>,
  variablesByPath: {
    [path: string]: VariableTypeDefinition;
  },
  variablesByName: {
    [name: string]: VariableTypeDefinition;
  } = {}
) => {
  const selectedList = String(
    iteratorStep.properties.inputs["list"] ?? ""
  ).trim();
  // Get the variable type from the selected list
  const isVariable =
    selectedList.startsWith("{{") && selectedList.endsWith("}}");
  const identifier = selectedList.replaceAll(/\{\{|\}\}/g, "");
  const variableToIterateOver = isVariable
    ? (variablesByPath[identifier] ?? variablesByName[identifier])
    : undefined;

  const itemVariableName = String(
    iteratorStep.properties.inputs["itemVariableName"] ?? ""
  );

  const result: {
    item: VariableConfiguration;
    index: VariableConfiguration;
    other?: VariableConfiguration; // Variable to set (aggregate or boolean filter)
  } = {
    item: {
      type: "unknown",
      identifier: itemVariableName
    },
    index: {
      type: "number",
      identifier: `${itemVariableName}_index`
    }
  };

  if (variableToIterateOver?.__configuration?.type === "list") {
    // get type of list items
    const listOfQuestion = variableToIterateOver.__configuration?.properties
      ?.items?.[0] as Question;

    if (listOfQuestion) {
      result.item = {
        type: listOfQuestion.type,
        identifier: itemVariableName,
        ...(listOfQuestion.properties
          ? {
              properties: (listOfQuestion.properties ??
                {}) as Variable["properties"]
            }
          : {})
      };
    }
  } else if (variableToIterateOver?.__configuration?.type === "table") {
    result.item = {
      type: "json",
      identifier: itemVariableName,
      properties: {
        items: variableToIterateOver.__configuration?.properties?.columns
      }
    };
  }

  if (
    iteratorStep.properties.typePrimaryIdentifier === IteratorStepType.AGGREGATE
  ) {
    result.other = {
      type: `${iteratorStep.properties.inputs["resultVariableType"] ?? "json"}`,
      identifier: String(
        iteratorStep.properties.inputs["resultVariableName"] ?? ""
      )
    };
  } else if (
    iteratorStep.properties.typePrimaryIdentifier === IteratorStepType.FILTER
  ) {
    result.other = {
      type: "boolean",
      identifier: `${itemVariableName}_output`
    };
  }

  return result;
};

export const getVariableDefinitions = ({
  flowContext,
  document,
  d
}: {
  flowContext?: MockFlowContext;
  document: WorkspaceDocument;
  d: Dictionary;
}) => {
  return Object.values(flowContext?.variables ?? {}).reduce((acc, variable) => {
    const definition = getVariableTypeDefinition({
      variable,
      document,
      d
    });
    return {
      ...acc,
      [variable.identifier]: {
        ...definition,
        __identifier: definition.__identifier ?? variable.identifier,
        __path: definition.__path ?? variable.identifier,
        // Just for root level
        __sourceStepId: variable.sourceStepId,
        __availableFromStepId: variable.availableFromStepId,
        __sourceStepVariant: variable.sourceStepVariant,
        __isStartingVariable: variable.isStartingVariable,
        __isEndingVariable: variable.isEndingVariable,
        __iteratorParentId: variable.iteratorParentId,
        __isAggregateOutput: variable.isAggregateOutput
        // To highlight usage later
        // _stepIds: variable.stepIds
      }
    };
  }, {});
};
