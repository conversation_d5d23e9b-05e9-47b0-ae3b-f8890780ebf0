import { Doc } from "@automerge/automerge-repo";
import { getQuestionTypeDetail } from "@components/flows/VariableField/variableFieldHelpers";
import { ConfigurationSectionWrapper } from "@helpers/ConfigurationSectionWrapper";
import { Dictionary } from "@src/hooks/useDictionary";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Question, QuestionTypes, ExtendedQuestionTypes } from "@src/types/Question";

const getWorkspaceConfigurationDefinition = ({
  to,
  document,
  type,
  d
}: {
  to?: string;
  document: Doc<WorkspaceDocument>;
  type?: string;
  d: Dictionary;
}) => {
  let formConfiguration;
  let seriesConfiguration;
  let questions: Question[] = [];

  const formConfigurationId = getQuestionTypeDetail(type).configurationId;

  if (formConfigurationId) {
    formConfiguration = document.forms?.[formConfigurationId];
    if (formConfiguration?.content) {
      const formConfigurationWrapper = new ConfigurationSectionWrapper(
        formConfiguration?.content
      );
      questions = formConfigurationWrapper.getQuestions();
    }
    seriesConfiguration = formConfiguration?.seriesId
      ? document.series[formConfiguration?.seriesId]
      : undefined;
  }
  return {
    __type: type,
    __path: to,
    __description: formConfiguration
      ? `${formConfiguration?.key} ${d("ui.terminology.form")}`
      : undefined,
    id: {
      __path: `${to}.id`,
      __identifier: "id",
      __type: QuestionTypes.NUMBER,
      __description: formConfiguration?.id
        ? `${formConfiguration?.key} ${d("ui.terminology.form")} ID`
        : `${d("ui.terminology.form")} ID`
    },
    foundation: getFoundationDefinition({
      to: `${to}.foundation`,
      document,
      type: formConfiguration?.foundationId
        ? `${ExtendedQuestionTypes.FOUNDATION}.${formConfiguration?.foundationId}`
        : ExtendedQuestionTypes.FOUNDATION,
      d
    }),
    properties: {
      __path: `${to}.properties`,
      __type: QuestionTypes.JSON,
      __identifier: "properties",
      __configuration: {
        identifier: "properties",
        type: QuestionTypes.JSON
      }
      // TODO: formConfiguration to contain the list of questions that are the "properties"
    },
    // Not for user
    // documentId: {
    //   __type: QuestionTypes.TEXT
    // },
    ...(formConfiguration?.seriesId && seriesConfiguration?.name
      ? {
          [seriesConfiguration?.name.replaceAll(" ", "")]:
            getSeriesIntervalDefinition({
              to: `${to}.seriesInterval`,
              identifier: seriesConfiguration.name.replaceAll(" ", ""),
              type: seriesConfiguration?.id
                ? `${ExtendedQuestionTypes.SERIES_INTERVAL}.${seriesConfiguration?.id}`
                : ExtendedQuestionTypes.SERIES_INTERVAL,
              document,
              d
            })
        }
      : {}),
    formConfiguration: getFormConfigurationDefinition({
      // TODO: fix type and path
      to: `${to}.formConfiguration`,
      formConfiguration,
      seriesConfiguration,
      document,
      d
    }),
    ...Object.fromEntries(
      (questions ?? []).map(q => {
        return [
          q.identifier,
          getQuestionDefinition({
            question: q,
            to: `${to}.${q.id ?? q.identifier}`
          })
        ];
      })
    )
  };
};
