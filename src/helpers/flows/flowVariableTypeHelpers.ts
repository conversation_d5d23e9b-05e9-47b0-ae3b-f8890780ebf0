import { Doc } from "@automerge/automerge-repo";
import { workerData } from "worker_threads";

import { ConfigurationSectionWrapper } from "@helpers/ConfigurationSectionWrapper";

import { getQuestionTypeDetail } from "@components/flows/VariableField/variableFieldHelpers";

import { Dictionary } from "@src/hooks/useDictionary";
import { MockVariable } from "@src/types/FlowConfiguration/FlowConfiguration";
import { VariableTypeDefinition } from "@src/types/FlowConfiguration/Variables";
import { ConfigurationFormType } from "@src/types/FormConfiguration";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration";
import {
  ExtendedQuestionTypes,
  Question,
  QuestionTypes,
  multiLevelQuestionTypes
} from "@src/types/Question";
import {
  JSONQuestionProperties,
  ListQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import { SeriesConfig } from "@src/types/Series";
import { WorkspaceDocument } from "@src/types/documentTypes";

import { WorkspacePrefix } from "./workspaceVariableHelpers";

export const getVariableTypeDefinition = ({
  variable,
  document,
  d
}: {
  variable: MockVariable;
  document: Doc<WorkspaceDocument>;
  d: Dictionary;
}): VariableTypeDefinition => {
  if (!variable.type) {
    variable.type = "unknown";
  }
  const { type } = getQuestionTypeDetail(variable.type);
  if (type === "workspaceConfiguration") {
    return {
      __path: `${WorkspacePrefix}.${variable.identifier.toLowerCase()}`,
      __identifier: variable.identifier,
      __type: QuestionTypes.TEXT
    };
  }
  if (type === "workspaceVariables") {
    return getWorkspaceVariablesDefinition({
      to: variable.identifier,
      document,
      type: variable.type,
      d
    });
  }
  if (type === ExtendedQuestionTypes.FOUNDATION) {
    return getFoundationDefinition({
      to: variable.identifier,
      document,
      type: variable.type,
      d
    });
  } else if (type === ExtendedQuestionTypes.FORM) {
    return getFormDefinition({
      to: variable.identifier,
      document,
      type: variable.type,
      d
    });
  } else if (type === ExtendedQuestionTypes.SERIES_INTERVAL) {
    return getSeriesIntervalDefinition({
      to: variable.identifier,
      identifier: variable.identifier,
      type: variable.type,
      document,
      d
    });
  } else if (type === QuestionTypes.TABLE) {
    return {
      __path: `${variable.identifier}`,
      __identifier: variable.identifier,
      __type: QuestionTypes.TABLE,
      __configuration: {
        identifier: variable?.identifier,
        type: QuestionTypes.TABLE,
        properties: {
          columns: [
            {
              id: "_rowIndex",
              identifier: "RowIndex",
              text: "Row Index",
              type: QuestionTypes.NUMBER
            },
            ...(variable.properties?.columns?.map(column => {
              return {
                ...(column.id ? { id: column.id } : {}),
                identifier: column.identifier,
                type: column.type
              };
            }) ?? [])
          ]
        }
      },
      ...Object.fromEntries(
        // Fix columns
        ((variable.properties?.columns as Question[]) ?? [])?.map(
          (item: Question) => {
            return [
              item.identifier,
              {
                __path: `${variable.identifier}.${item.identifier}`,
                __identifier: item.identifier,
                __description: `${item.type} column`,
                __type: QuestionTypes.LIST,
                __configuration: {
                  type: QuestionTypes.LIST,
                  items: [
                    {
                      type: item.type
                    }
                  ]
                }
              }
            ];
          }
        )
      )
      // TODO: add columns from variable?.properties?.columns
    };
  } else if (type === QuestionTypes.LIST) {
    const listOf = variable.properties?.items?.[0];
    return {
      __path: variable.identifier,
      __identifier: variable.identifier,
      __type: QuestionTypes.LIST,
      __configuration: {
        identifier: variable?.identifier,
        type: QuestionTypes.LIST,
        properties: {
          items: [
            {
              identifier: listOf?.identifier ?? `${variable.identifier}.item`,
              type: listOf?.type ?? "unknown",
              properties: listOf?.properties
            }
          ]
        }
      },
      ...Object.fromEntries(
        (Array.isArray(variable.properties?.items)
          ? (variable.properties.items as Question[])
          : []
        ).map(item => {
          return [
            item.identifier,
            {
              __path: `${variable.identifier}.${item.id ?? item.identifier}`,
              __identifier: item.identifier ?? item.id,
              __type: QuestionTypes.LIST,
              __description: `${item.type} item`,
              __configuration: {
                type: QuestionTypes.LIST,
                items: [
                  {
                    type: item.type,
                    properties: item.properties
                  }
                ]
              }
            }
          ];
        })
      )
    };
  } else if (type === QuestionTypes.JSON) {
    const processJsonVariableItem = (
      item: Question,
      parentPath: string
    ): [string, object] => {
      const itemPath = `${parentPath}.${item.id ?? item.identifier}`;

      if (item.type === QuestionTypes.JSON) {
        // Recursively process nested JSON
        return [
          item.identifier,
          {
            __path: itemPath,
            __identifier: item.identifier ?? item.id,
            __type: QuestionTypes.JSON,
            __configuration: item,
            ...Object.fromEntries(
              (
                ((item.properties as JSONQuestionProperties)
                  ?.items as Question[]) ?? []
              ).map(nestedItem => processJsonVariableItem(nestedItem, itemPath))
            )
          }
        ];
      }

      // Handle non-JSON types
      return [
        item.identifier,
        {
          __path: itemPath,
          __identifier: item.identifier ?? item.id,
          __type: item.type,
          __description: `${item.type} item`,
          __configuration: [
            {
              type: item.type
            }
          ]
        }
      ];
    };

    return {
      __path: `${variable.identifier}`,
      __identifier: variable.identifier,
      __type: QuestionTypes.JSON,
      ...Object.fromEntries(
        (Array.isArray(variable.properties?.items)
          ? (variable.properties.items as Question[])
          : []
        ).map(item => processJsonVariableItem(item, variable.identifier))
      )
    };
  } else if (type === QuestionTypes.FILES) {
    return {
      __path: variable.identifier,
      __identifier: variable.identifier,
      __type: QuestionTypes.FILES,
      __configuration: {
        identifier: variable?.identifier,
        type: QuestionTypes.LIST,
        properties: {
          items: [
            {
              identifier: `${variable.identifier}.item`,
              type: QuestionTypes.JSON,
              properties: {
                items: [
                  {
                    type: QuestionTypes.TEXT,
                    identifier: "path"
                  },
                  {
                    type: QuestionTypes.TEXT,
                    identifier: "name"
                  }
                ]
              }
            }
          ]
        }
      }
    };
  }

  return {
    __path: variable.identifier,
    __identifier: variable.identifier,
    __type: variable.type
  };
};

const getFoundationDefinition = ({
  to,
  document,
  type,
  d
}: {
  to: string;
  document: Doc<WorkspaceDocument>;
  type: string;
  d: Dictionary;
}) => {
  let foundationConfiguration;
  let parentFoundationConfiguration;
  let childFoundationConfiguration;

  const foundationConfigurationId = getQuestionTypeDetail(type).configurationId;

  if (foundationConfigurationId) {
    foundationConfiguration =
      document.foundations.entities[foundationConfigurationId];

    const foundationOrder = document.foundations.order.indexOf(
      foundationConfigurationId
    );

    if (foundationOrder > 0) {
      parentFoundationConfiguration =
        document.foundations.entities[
          document.foundations.order[foundationOrder - 1]
        ];
    }

    if (foundationOrder < document.foundations.order.length - 1) {
      childFoundationConfiguration =
        document.foundations.entities[
          document.foundations.order[foundationOrder + 1]
        ];
    }
  }

  return {
    __path: to,
    __type: type,
    __description: foundationConfiguration
      ? `${foundationConfiguration?.name} ${d("ui.terminology.foundation")}`
      : "",
    id: {
      __path: `${to}.id`,
      __identifier: "id",
      __type: QuestionTypes.NUMBER
    },
    key: {
      __path: `${to}.key`,
      __identifier: "key",
      __type: QuestionTypes.TEXT
    },
    name: {
      __path: `${to}.name`,
      __identifier: "name",
      __type: QuestionTypes.TEXT
    },
    properties: {
      __path: `${to}.properties`,
      __type: QuestionTypes.JSON,
      __identifier: "properties",
      __configuration: {
        identifier: "properties",
        type: QuestionTypes.JSON
      }
      // TODO: foundationConfiguration to contain the list of questions that are the "properties" ? Similarr to formConfiguration
    },
    foundationConfiguration: getFoundationConfigurationDefinition({
      to: `${to}.foundationConfiguration`,
      foundationConfiguration,
      parentFoundationConfiguration,
      childFoundationConfiguration
    }),
    ...(!foundationConfiguration || parentFoundationConfiguration
      ? {
          parentId: {
            __path: `${to}.parentId`,
            __type: QuestionTypes.NUMBER,
            __description: parentFoundationConfiguration
              ? `${parentFoundationConfiguration?.name} Foundation`
              : "Unknown foundation level (could be undefined)"
          }
        }
      : {})
  };
};

const getFoundationConfigurationDefinition = ({
  to,
  foundationConfiguration,
  parentFoundationConfiguration,
  childFoundationConfiguration
}: {
  to: string;
  foundationConfiguration?: FoundationConfiguration;
  parentFoundationConfiguration?: FoundationConfiguration;
  childFoundationConfiguration?: FoundationConfiguration;
}) => {
  return {
    __path: to,
    __type: "foundationConfiguration",
    __identifier: "foundationLevel",
    __description: foundationConfiguration
      ? `${foundationConfiguration?.name}`
      : "",
    id: {
      __path: `${to}.id`,
      __identifier: "id",
      __type: QuestionTypes.TEXT
    },
    name: {
      __path: `${to}.name`,
      __identifier: "name",
      __type: QuestionTypes.TEXT
    },
    key: {
      __path: `${to}.key`,
      __identifier: "key",
      __type: QuestionTypes.TEXT
    },
    ...(!foundationConfiguration ||
    (foundationConfiguration && parentFoundationConfiguration)
      ? {
          parentId: {
            __path: `${to}.parentId`,
            __identifier: "parentId",
            __type: QuestionTypes.TEXT,
            __description: parentFoundationConfiguration
              ? parentFoundationConfiguration?.name
              : "Unknown foundation level (could be undefined)"
          }
        }
      : {}),
    ...(!foundationConfiguration ||
    (foundationConfiguration && childFoundationConfiguration)
      ? {
          childId: {
            __path: `${to}.childId`,
            __identifier: "childId",
            __type: QuestionTypes.TEXT,
            __description: childFoundationConfiguration
              ? childFoundationConfiguration?.name
              : "Unknown foundation level (could be undefined)"
          }
        }
      : {})
  };
};
const getFormDefinition = ({
  to,
  document,
  type,
  d
}: {
  to?: string;
  document: Doc<WorkspaceDocument>;
  type?: string;
  d: Dictionary;
}) => {
  let formConfiguration;
  let seriesConfiguration;
  let questions: Question[] = [];

  const formConfigurationId = getQuestionTypeDetail(type).configurationId;

  if (formConfigurationId) {
    formConfiguration = document.forms?.[formConfigurationId];
    if (formConfiguration?.content) {
      const formConfigurationWrapper = new ConfigurationSectionWrapper(
        formConfiguration?.content
      );
      questions = formConfigurationWrapper.getQuestions();
    }
    seriesConfiguration = formConfiguration?.seriesId
      ? document.series[formConfiguration?.seriesId]
      : undefined;
  }
  return {
    __type: type,
    __path: to,
    __description: formConfiguration
      ? `${formConfiguration?.key} ${d("ui.terminology.form")}`
      : undefined,
    id: {
      __path: `${to}.id`,
      __identifier: "id",
      __type: QuestionTypes.NUMBER,
      __description: formConfiguration?.id
        ? `${formConfiguration?.key} ${d("ui.terminology.form")} ID`
        : `${d("ui.terminology.form")} ID`
    },
    foundation: getFoundationDefinition({
      to: `${to}.foundation`,
      document,
      type: formConfiguration?.foundationId
        ? `${ExtendedQuestionTypes.FOUNDATION}.${formConfiguration?.foundationId}`
        : ExtendedQuestionTypes.FOUNDATION,
      d
    }),
    properties: {
      __path: `${to}.properties`,
      __type: QuestionTypes.JSON,
      __identifier: "properties",
      __configuration: {
        identifier: "properties",
        type: QuestionTypes.JSON
      }
      // TODO: formConfiguration to contain the list of questions that are the "properties"
    },
    // Not for user
    // documentId: {
    //   __type: QuestionTypes.TEXT
    // },
    ...(formConfiguration?.seriesId && seriesConfiguration?.name
      ? {
          [seriesConfiguration?.name.replaceAll(" ", "")]:
            getSeriesIntervalDefinition({
              to: `${to}.seriesInterval`,
              identifier: seriesConfiguration.name.replaceAll(" ", ""),
              type: seriesConfiguration?.id
                ? `${ExtendedQuestionTypes.SERIES_INTERVAL}.${seriesConfiguration?.id}`
                : ExtendedQuestionTypes.SERIES_INTERVAL,
              document,
              d
            })
        }
      : {}),
    formConfiguration: getFormConfigurationDefinition({
      // TODO: fix type and path
      to: `${to}.formConfiguration`,
      formConfiguration,
      seriesConfiguration,
      document,
      d
    }),
    ...Object.fromEntries(
      (questions ?? []).map(q => {
        return [
          q.identifier,
          getQuestionDefinition({
            question: q,
            to: `${to}.${q.id ?? q.identifier}`
          })
        ];
      })
    )
  };
};

const getSeriesIntervalDefinition = ({
  to,
  identifier,
  type,
  document,
  d
}: {
  to: string;
  identifier?: string;
  type: string;
  document: Doc<WorkspaceDocument>;
  d: Dictionary;
}) => {
  const seriesConfigurationId = getQuestionTypeDetail(type).configurationId;
  const seriesConfiguration = seriesConfigurationId
    ? document.series[seriesConfigurationId]
    : undefined;

  return {
    __path: to,
    __identifier: identifier,
    __type: type,
    __description: seriesConfiguration?.name
      ? `${d("ui.terminology.series")}: ${seriesConfiguration?.name}`
      : `${d("ui.terminology.series")} unavailable`,
    id: {
      __path: `${to}.id`,
      __type: QuestionTypes.TEXT
    },
    name: {
      __path: `${to}.name`,
      __type: QuestionTypes.TEXT
    },
    next: {
      __path: `${to}.next.id`,
      __type: ExtendedQuestionTypes.SERIES_INTERVAL,
      __description: `${seriesConfiguration?.name} (next)`,
      id: {
        __path: `${to}.next.id`,
        __type: QuestionTypes.TEXT
      },
      name: {
        __path: `${to}.next.name`,
        __type: QuestionTypes.TEXT
      }
    },
    previous: {
      __path: `${to}.previous.id`,
      __type: ExtendedQuestionTypes.SERIES_INTERVAL,
      __description: `${seriesConfiguration?.name} (previous)`,
      id: {
        __path: `${to}.previous.id`,
        __type: QuestionTypes.TEXT
      },
      name: {
        __path: `${to}.previous.name`,
        __type: QuestionTypes.TEXT
      }
    },
    seriesConfiguration: {
      __path: `${to}.seriesConfiguration.id`,
      __type: "seriesConfiguration",
      __description: `${d("ui.terminology.series")} configuration (${seriesConfiguration?.name})`,
      id: {
        __path: `${to}.seriesConfiguration.id`,
        __type: QuestionTypes.TEXT
      },
      name: {
        __path: `${to}.seriesConfiguration.name`,
        __type: QuestionTypes.TEXT
      },
      intervals: {
        __path: `${to}.seriesConfiguration.intervals`,
        __type: QuestionTypes.LIST,
        __description: "List of intervals",
        __configuration: {
          identifier: "intervals",
          type: QuestionTypes.LIST,
          properties: {
            items: [
              {
                type: QuestionTypes.JSON,
                properties: {
                  items: [
                    {
                      type: QuestionTypes.TEXT,
                      identifier: "id"
                    },
                    {
                      type: QuestionTypes.TEXT,
                      identifier: "name"
                    }
                  ]
                }
              }
            ]
          }
        }
      }
    }
  };
};

const getFormConfigurationDefinition = ({
  to,
  formConfiguration,
  seriesConfiguration,
  document,
  d
}: {
  to: string;
  formConfiguration?: ConfigurationFormType;
  seriesConfiguration?: SeriesConfig;
  document: Doc<WorkspaceDocument>;
  d: Dictionary;
}) => {
  return {
    __path: `${to}.id`,
    __identifier: "formConfiguration",
    __type: "formConfiguration",
    id: {
      __type: QuestionTypes.TEXT,
      __identifier: "id",
      __path: `${to}.id`
    },
    key: {
      __type: QuestionTypes.TEXT,
      __identifier: "key",
      __path: `${to}.key`,
      __description: formConfiguration?.key
    },
    name: {
      __type: QuestionTypes.TEXT,
      __identifier: "name",
      __path: `${to}.name`,
      __description: formConfiguration?.name
    },
    ...(formConfiguration?.seriesId || seriesConfiguration
      ? {
          seriesId: {
            __path: `${to}.seriesId`,
            __type: QuestionTypes.TEXT,
            __identifier: "seriesId",
            __description: seriesConfiguration?.name
          },
          seriesInterval: getSeriesIntervalDefinition({
            to: `${to}.seriesInterval`,
            identifier: seriesConfiguration?.name.replaceAll(" ", ""),
            type: seriesConfiguration?.id
              ? `${ExtendedQuestionTypes.SERIES_INTERVAL}.${seriesConfiguration?.id}`
              : ExtendedQuestionTypes.SERIES_INTERVAL,
            document,
            d
          })
        }
      : {})
  };
};

// TODO: change to only get the id, text, type
const getQuestionDefinition = ({
  to,
  identifier,
  question,
  description = "Question"
}: {
  to: string;
  identifier?: string;
  question?: Question;
  description?: string;
}): VariableTypeDefinition => {
  return {
    ...(identifier ? { __identifier: identifier } : {}),
    __type: question?.type ?? "question",
    __description: description,
    __path: `${to}.answer`,
    __configuration: question,
    configuration: {
      __type: "questionConfiguration",
      __path: `${to}.id`,
      id: {
        __path: `${to}.id`,
        __identifier: "id",
        __type: QuestionTypes.TEXT
      },
      text: {
        __path: `${to}.text`,
        __identifier: QuestionTypes.TEXT,
        __type: QuestionTypes.TEXT,
        __description: question?.text
      },
      type: {
        __path: `${to}.type`,
        __identifier: "type",
        __type: QuestionTypes.TEXT,
        __description: question?.type
      }
    },
    ...getQuestionAnswerAndMultiLevelParts({
      question,
      to
    })
  } as VariableTypeDefinition;
};

const getQuestionAnswerAndMultiLevelParts = ({
  question,
  to
}: {
  question?: Question;
  to: string;
}) => {
  if (!question || !multiLevelQuestionTypes.includes(question.type)) {
    return {
      answer: {
        __path: `${to}.answer`,
        __identifier: "answer",
        __type: question?.type ?? "unknown"
      }
    };
  }

  // JSON
  if (question.type === QuestionTypes.JSON) {
    const processJsonQuestionItem = (
      item: Question,
      basePath: string
    ): [string, object] => {
      if (item.type === QuestionTypes.JSON) {
        // Handle nested JSON
        return [
          item.identifier,
          {
            __path: `${basePath}.${item.id ?? item.identifier}`,
            __identifier: item.identifier ?? item.id,
            __description: item.text,
            __type: item.type,
            __configuration: {
              identifier: item.identifier,
              type: item.type,
              properties: {
                items:
                  (item.properties as JSONQuestionProperties)?.items?.map(
                    (nestedItem: Question) => ({
                      identifier: nestedItem.identifier,
                      type: nestedItem.type
                    })
                  ) ?? []
              }
            },
            ...Object.fromEntries(
              (item.properties as JSONQuestionProperties)?.items?.map(
                nestedItem =>
                  processJsonQuestionItem(
                    nestedItem,
                    `${basePath}.${item.id ?? item.identifier}`
                  )
              ) ?? []
            )
          }
        ];
      }

      // Handle non-JSON types
      return [
        item.identifier,
        {
          __path: `${basePath}.${item.id ?? item.identifier}`,
          __identifier: item.identifier ?? item.id,
          __description: item.text,
          __type: item.type,
          __configuration: {
            identifier: item.identifier,
            type: item.type,
            properties: {
              items: [
                {
                  type: item.type ?? "unknown"
                }
              ]
            }
          }
        }
      ];
    };

    return {
      answer: {
        __path: `${to}.answer`,
        __identifier: "answer",
        __type: QuestionTypes.JSON,
        __configuration: {
          identifier: question?.identifier,
          type: QuestionTypes.JSON,
          properties: {
            items:
              (question.properties as JSONQuestionProperties)?.items?.map(
                (item: Question) => ({
                  identifier: item.identifier,
                  type: item.type
                })
              ) ?? []
          }
        },
        ...Object.fromEntries(
          (question.properties as JSONQuestionProperties)?.items?.map(item =>
            processJsonQuestionItem(item, `${to}.answer`)
          ) ?? []
        )
      },
      ...Object.fromEntries(
        (question.properties as JSONQuestionProperties)?.items?.map(item =>
          processJsonQuestionItem(item, `${to}.items`)
        ) ?? []
      )
    };
  }

  // List
  if (question.type === QuestionTypes.LIST) {
    const listOfQuestion = (question?.properties as ListQuestionProperties)
      ?.items[0];
    return {
      answer: {
        __path: `${to}.answer`,
        __identifier: "answer",
        __type: QuestionTypes.LIST,
        __description: `List items`, // TODO: list of type
        __configuration: {
          identifier: question?.identifier,
          type: QuestionTypes.LIST,
          properties: {
            items: [
              {
                type: listOfQuestion?.type ?? "unknown",
                properties: listOfQuestion?.properties
              }
            ]
          }
        }
      }
    };
  }

  // Table
  if (question.type === QuestionTypes.TABLE) {
    return {
      answer: {
        __path: `${to}.answer`,
        __identifier: "answer",
        __type: QuestionTypes.TABLE,
        __description: `Rows`,
        __configuration: {
          identifier: question?.identifier,
          type: QuestionTypes.TABLE,
          properties: {
            columns: [
              {
                id: "_rowId",
                identifier: "RowId",
                text: "Row ID",
                type: QuestionTypes.TEXT
              },
              {
                id: "_rowIndex",
                identifier: "RowIndex",
                text: "Row Index",
                type: QuestionTypes.NUMBER
              },
              ...((
                question.properties as TableQuestionProperties
              )?.columns?.map((column: Question) => {
                return {
                  ...(column.id ? { id: column.id } : {}),
                  identifier: column.identifier,
                  type: column.type
                };
              }) ?? [])
            ]
          }
        }
      },
      ...Object?.fromEntries(
        (question.properties as TableQuestionProperties)?.columns?.map(
          (item: Question) => {
            return [
              item.identifier,
              {
                __type: QuestionTypes.LIST, // item.type,
                __configuration: {
                  identifier: item.identifier,
                  type: item.type,
                  properties: {
                    items: [
                      {
                        type: item.type ?? "unknown"
                      }
                    ]
                  }
                },
                ...getQuestionDefinition({
                  question: item,
                  to: `${to}.columns.${item.id ?? item.identifier}`,
                  description: `Column`
                }),
                answer: {
                  __path: `${to}.columns.${item.id ?? item.identifier}.answer`,
                  __type: QuestionTypes.LIST,
                  __description: `Column values (list of ${item.type})`,
                  __configuration: {
                    identifier: item.identifier,
                    type: QuestionTypes.LIST,
                    properties: {
                      items: [
                        {
                          type: item.type ?? "unknown"
                        }
                      ]
                    }
                  }
                }
              }
            ];
          }
        ) ?? []
      )
    };
  }
};

const getWorkspaceVariablesDefinition = ({
  to,
  document,
  type,
  d
}: {
  to?: string;
  document: Doc<WorkspaceDocument>;
  type?: string;
  d: Dictionary;
}) => {
  const workspaceVariables = document.variables;
  console.log("workspaceVariables", workspaceVariables);

  const mappedVariables = Object.entries(workspaceVariables).reduce(
    (acc, [id, variable]) => {
      acc[variable.name] = {
        __path: `_workspace_.variables.${variable.name}`,
        __identifier: variable.name,
        __type: QuestionTypes.TEXT,
        __description: variable.description ?? `${variable.name} variable`
        // value: variable.value,
        // isSecured: variable.isSecured
      };
      return acc;
    },
    {} as Record<string, any>
  );

  console.log("mappedVariables", mappedVariables);

  return {
    __path: "_workspace_.variables",
    __type: "workspaceVariables",
    __identifier: to,
    ...mappedVariables
  };
};
