import React, { ReactNode, useEffect, useMemo } from "react";

import "@oneteam/onetheme/index.css";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  RouteObject,
  RouterProvider,
  createBrowserRouter
} from "react-router-dom";

import { CursorProvider } from "@components/shared/WhiteboardPageBodyTemplate/CursorProvider.tsx";

import { AppSettingsLayout } from "@pages/appSettings/AppSettingsLayout.tsx";
import { AppSettingsApiKeys } from "@pages/appSettings/apiKeys/AppSettingsApiKeys.tsx";
import { Auth } from "@pages/auth/Auth.tsx";
import { Logout } from "@pages/auth/Logout.js";
import { FlowRunner } from "@pages/collection/flows/FlowRunner.tsx";
import { ConfigurationDashboard } from "@pages/configuration/dashboard/ConfigurationDashboard.tsx";
import { ConfigurationFlow } from "@pages/configuration/flows/ConfigurationFlow.tsx";
import { ConfigurationFlowList } from "@pages/configuration/flows/ConfigurationFlowList.tsx";
import { ConfigurationForm } from "@pages/configuration/forms/ConfigurationForm.tsx";
import { ConfigurationFormList } from "@pages/configuration/forms/ConfigurationFormList.tsx";
import { ConfigurationFoundation } from "@pages/configuration/foundations/ConfigurationFoundation.tsx";
import { ConfigurationLabelListPage } from "@pages/configuration/labels/ConfigurationLabelListPage.tsx";
import { ConfigurationSeriesList } from "@pages/configuration/series/ConfigurationSeriesList.tsx";
import { ConfigurationVariablesListPage } from "@pages/configuration/variables/ConfigurationVariablesListPage.tsx";
import { AccessDeniedElement } from "@pages/errors/AccessDeniedElement.tsx";
import { NotFoundPage, NotFoundPageBody } from "@pages/errors/NotFoundPage.tsx";
import { SettingsDetails } from "@pages/settings/details/SettingsDetails.tsx";
import { SettingsPermissions } from "@pages/settings/permissions/SettingsPermissions.tsx";
import { useWorkspaceAccessLevel } from "@pages/settings/permissions/WorkspaceAccessLevelContext/WorkspaceAccessLevelContext.tsx";
import { WorkspaceAccessLevelProvider } from "@pages/settings/permissions/WorkspaceAccessLevelContext/WorkspaceAccessLevelProvider.tsx";
import { CreateWorkspace } from "@pages/workspace/CreateWorkspace.tsx";
import { DynamicCollectionRoutes } from "@pages/workspace/DynamicCollectionRoutes.tsx";
import { WorkspaceLayout } from "@pages/workspace/WorkspaceLayout.tsx";

import {
  routeConstants,
  workspaceRoutePrefix
} from "@src/constants/routeConstants.ts";
import { TenantProvider } from "@src/tenant/TenantProvider.tsx";

import "./App.scss";
import { AuthProvider } from "./authentication/AuthProvider.tsx";
import { useAuth } from "./hooks/useAuth.tsx";
import { ErrorElement } from "./pages/errors/ErrorElement.tsx";
import { WorkspaceAccessLevel } from "./types/WorkspaceUser.ts";

const queryClient = new QueryClient();

function RequireAuth({ children }: Readonly<{ children: ReactNode }>) {
  const { user, loadedInitially } = useAuth();

  if (loadedInitially && user) {
    return children;
  }

  // else we're still in limbo waiting for things to load - our Auth component handles this case
  return <Auth withRedirect={true} />;
}

const protectRoute = (route: RouteObject): RouteObject => {
  return {
    ...route,
    element: <RequireAuth>{route.element}</RequireAuth>
  };
};

function RequireWorkspaceLevelAccess({
  level,
  children
}: Readonly<{
  level: WorkspaceAccessLevel;
  children: ReactNode;
}>) {
  const { user, loadedInitially } = useAuth();
  const { currentLevel, setHasAccessToLevel } = useWorkspaceAccessLevel();

  const userHasAccessToLevel = useMemo(() => {
    if (!currentLevel || !level) {
      return false;
    }
    return currentLevel.includes(level);
  }, [currentLevel, level]);

  useEffect(() => {
    setHasAccessToLevel(userHasAccessToLevel);
  }, [userHasAccessToLevel, setHasAccessToLevel]);

  if (!currentLevel || !level) {
    return <></>;
  }

  if (loadedInitially && user && userHasAccessToLevel) {
    return children;
  }

  return <AccessDeniedElement />;
}

const protectWorkspaceRoute = (
  level: WorkspaceAccessLevel,
  route: RouteObject
): RouteObject => {
  // If the user does not have the required access level, they should be shown access denied
  const protectedRoute: RouteObject = {
    ...route,
    element: route.element ? (
      <RequireWorkspaceLevelAccess level={level}>
        {route.element}
      </RequireWorkspaceLevelAccess>
    ) : undefined
  };

  if (route.children) {
    protectedRoute.children = route.children.map(child =>
      protectWorkspaceRoute(level, child)
    );
  }

  return protectedRoute;
};

function App() {
  const router = createBrowserRouter(
    [
      {
        // Generally don't expect users to hit this route directly
        path: routeConstants.auth,
        element: <Auth />
      },
      { path: routeConstants.logout, element: <Logout /> },
      protectRoute({
        path: "/",
        element: <WorkspaceLayout />
      }),
      protectRoute({
        path: workspaceRoutePrefix,
        element: <WorkspaceLayout />
      }),
      protectRoute({
        path: routeConstants.home,
        element: <WorkspaceLayout />,
        errorElement: <WorkspaceLayout pageError={<ErrorElement />} />,
        // TODO: add access denied and protectWorkspace
        children: [
          protectWorkspaceRoute(WorkspaceAccessLevel.CONFIGURATION, {
            path: routeConstants.configuration,
            children: [
              {
                path: routeConstants.configuration,
                element: <ConfigurationDashboard />
              },
              {
                path: routeConstants.configurationFormList,
                element: <ConfigurationFormList />
              },
              {
                path: routeConstants.configurationForm,
                element: <ConfigurationForm />
              },
              {
                path: routeConstants.configurationFlowList,
                element: <ConfigurationFlowList />
              },
              {
                path: routeConstants.configurationFlow,
                element: <ConfigurationFlow />
              },
              {
                path: routeConstants.configurationFoundation,
                element: <ConfigurationFoundation />
              },
              {
                path: routeConstants.configurationSeries,
                element: <ConfigurationSeriesList />
              },
              {
                path: routeConstants.configurationLabels,
                element: <ConfigurationLabelListPage />
              },
              {
                path: routeConstants.configurationVariables,
                element: <ConfigurationVariablesListPage />
              }
            ]
          }),
          protectWorkspaceRoute(WorkspaceAccessLevel.SETTINGS, {
            path: routeConstants.settings,
            children: [
              {
                path: routeConstants.settings,
                element: <SettingsDetails />
              },
              {
                path: routeConstants.settingsPermissions,
                element: <SettingsPermissions />
              }
            ]
          }),
          protectWorkspaceRoute(WorkspaceAccessLevel.COLLECTION, {
            path: routeConstants.home,
            children: [
              {
                index: true,
                element: <></>
              },
              {
                path: routeConstants.flowRunner,
                element: <FlowRunner />
              },
              // Required to be last for wildcard
              {
                path: `${routeConstants.collectionHome}/*`,
                element: <DynamicCollectionRoutes />
              },
              {
                path: `*`,
                element: <NotFoundPageBody />
              }
            ]
          })
        ]
      }),
      protectRoute({
        path: "/workspaces",
        element: <WorkspaceLayout />
      }),
      protectRoute({
        path: routeConstants.creationWorkspace,
        element: <CreateWorkspace />
      }),
      protectRoute({
        path: routeConstants.settings,
        element: <SettingsDetails />
      }),
      protectRoute({
        path: routeConstants.appSettings,
        element: <AppSettingsLayout />,
        children: [
          {
            path: routeConstants.appSettingsApiKeys,
            element: <AppSettingsApiKeys />
          }
        ]
      }),
      {
        path: "*",
        element: <NotFoundPage />
      }
    ],
    {
      basename: "/ai"
    }
  );

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TenantProvider>
          <WorkspaceAccessLevelProvider>
            <CursorProvider>
              <RouterProvider router={router} />
            </CursorProvider>
          </WorkspaceAccessLevelProvider>
        </TenantProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
