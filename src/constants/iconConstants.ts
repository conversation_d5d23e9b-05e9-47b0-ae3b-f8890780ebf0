import { IconType } from "@oneteam/onetheme";

type CommonIcons =
  | "foundations"
  | "forms"
  | "series"
  | "flows"
  | "labels"
  | "variables"
  | "copy"
  | "duplicate"
  | "rename"
  | "delete"
  | "update";

export const commonIcons: {
  [key in CommonIcons]: IconType;
} = {
  foundations: {
    name: "roofing"
  },
  forms: {
    name: "article"
  },
  series: {
    name: "steppers"
  },
  flows: {
    name: "family_history"
  },
  labels: {
    name: "label"
  },
  variables: {
    name: "page_info"
  },
  copy: {
    name: "content_copy"
  },
  duplicate: {
    name: "difference"
  },
  rename: {
    name: "border_color"
  },
  delete: {
    name: "delete"
  },
  update: {
    name: "edit"
  }
};
