import { useCallback, useMemo } from "react";

import { TableColumn, useQueryParams, useTableSort } from "@oneteam/onetheme";
import { useSearchParams } from "react-router-dom";

import { mergeQueryParams } from "@helpers/pagination";

// This can possibly be moved to a common place if used in multiple components
export const usePaginatedSearchParams = <T,>({
  columns
}: {
  columns: TableColumn<T>[];
}) => {
  const { queryParams, updateQueryParams } = useQueryParams({
    initialDefaultIfNone: { page: "1" },
    useSearchParams
  });

  const doUpdateQueryParams = useCallback(
    (newParams: { [key: string]: string | undefined }) => {
      updateQueryParams(mergeQueryParams(queryParams, newParams));
    },
    [updateQueryParams, queryParams]
  );

  const onChangePage = useCallback(
    (newPage: number) => {
      doUpdateQueryParams({ page: String(newPage) });
    },
    [doUpdateQueryParams]
  );

  const onChangeSearchTerm = useCallback(
    (input: string) => {
      doUpdateQueryParams({
        search: input,
        page: "1" // reset page to 1 when changing filter criteria since the total count may change
      });
    },
    [doUpdateQueryParams]
  );

  const { searchTerm, page, pageSize, asc, dsc } = useMemo(
    () => ({
      searchTerm: queryParams.get("search") ?? undefined,
      page: queryParams.get("page") ?? undefined,
      pageSize: queryParams.get("pageSize") ?? undefined,
      asc: queryParams.get("asc") ?? undefined,
      dsc: queryParams.get("dsc") ?? undefined
    }),
    [queryParams]
  );

  const { sort, handleSetSort } = useTableSort<T>({
    columns,
    queryParams,
    updateQueryParams: newParams => {
      updateQueryParams({
        page: newParams.page,
        pageSize: pageSize,
        search: searchTerm,
        asc: newParams.asc,
        dsc: newParams.dsc
      });
    }
  });

  return {
    sort,
    searchTerm,
    page,
    pageSize,
    asc,
    dsc,
    onChangeSorting: handleSetSort,
    onChangePage,
    onChangeSearchTerm
  };
};
