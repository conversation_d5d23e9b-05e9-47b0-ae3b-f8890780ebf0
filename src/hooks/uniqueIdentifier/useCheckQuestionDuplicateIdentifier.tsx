import { useCallback } from "react";

import {
  autogenerateIdentifierHelper,
  isDuplicateIdentifier
} from "@src/hooks/uniqueIdentifier/question";
import { Question } from "@src/types/Question";

import { useConfigurationForm } from "../formConfiguration/useConfigurationForm";

export function useCheckQuestionDuplicateIdentifier() {
  const { configurationForm } = useConfigurationForm();

  const checkDuplicateIdentifier = useCallback(
    (question: Question, identifier?: string, parentQuestion?: Question) => {
      return isDuplicateIdentifier({
        question,
        identifier,
        parentQuestion,
        withinContent: configurationForm?.content
      });
    },
    [configurationForm]
  );

  const autogenerateIdentifier = (
    question: Question,
    text: string,
    parentQuestion?: Question
  ) => {
    return autogenerateIdentifierHelper({
      question,
      text,
      parentQuestion,
      withinContent: configurationForm?.content
    });
  };

  return { checkDuplicateIdentifier, autogenerateIdentifier };
}
