import { FoundationConfiguration } from "@src/types/FoundationConfiguration";

import { createUniqueIdentifier } from "./question";

export function useCheckFoundationDuplicateIdentifier() {
  //should take the identifier and check if it is already used in the foundations
  //if it is, then take that identifier and append _1, _2, etc. until it finds a unique identifier
  const autogenerateIdentifier = (
    thisFoundation: FoundationConfiguration,
    foundations: FoundationConfiguration[]
  ) => {
    //remove special characters and spaces, then camelCase the name
    const newIdentifier = createUniqueIdentifier({
      text: thisFoundation?.name,
      existingIdentifiers: foundations
        .filter(f => f.id !== thisFoundation?.id)
        .map(f => f.identifier)
    });
    return newIdentifier;
  };

  return { autogenerateIdentifier };
}
