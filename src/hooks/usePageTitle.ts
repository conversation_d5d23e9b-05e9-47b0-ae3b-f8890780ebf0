import { useEffect } from "react";

export function buildShortPageTitle({
  section,
  context
}: {
  section: string;
  context: string;
}) {
  return `${section} | ${context}`;
}

export function buildConfigurationEntityPageTitle({
  section,
  entityName,
  context
}: {
  section: string;
  entityName: string;
  context: string;
}) {
  const title = `${section} — ${entityName} | ${context}`;
  return title;
}

export function buildCollectionFormPageTitle({
  section,
  formName,
  intervalName,
  context
}: {
  section: string;
  formName: string;
  intervalName?: string;
  context: string;
}) {
  const maybeIntervalString = intervalName ? intervalName + " " : "";
  const title = `${section} — ${formName} ${maybeIntervalString}| ${context}`;
  return title;
}

export function usePageTitle(title: string, enabled: boolean = false) {
  useEffect(() => {
    if (enabled) {
      document.title = title;
    }
  }, [title, enabled]);
}
