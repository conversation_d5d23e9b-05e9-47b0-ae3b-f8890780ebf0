import React, { useEffect, useRef } from "react";

import { Box, Form, Inline } from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";

import { autoGenerateKeyFromName } from "@components/shared/NameKeyFormField/autoGenerateKeyFromName";

export const NameKeyFormField = ({
  nameField: { label: nameLabel },
  keyField: {
    label: keyLabel,
    tooltip: keyTooltip,
    error: keyError,
    disabled: keyDisabled = false,
    afterChange: afterKeyChange,
    existingKeys = [],
    allowLowerCase: keyAllowLowerCase = false
  } = {},
  disabled = false
}: {
  nameField: {
    label: string;
  };
  keyField: {
    label?: string | undefined;
    tooltip?: string | undefined;
    error?: string | undefined;
    disabled?: boolean | undefined;
    afterChange?: (key: string) => void | undefined;
    existingKeys?: string[] | undefined;
    allowLowerCase?: boolean | undefined;
  };
  disabled?: boolean | undefined;
}) => {
  const formContext = useFormContext();
  const [suggestedKey, setSuggestedKey] = React.useState<string | undefined>();

  const isMounted = useRef(false);

  useEffect(() => {
    if (isMounted.current) {
      // If the component is already mounted, we can update the suggested key
      setSuggestedKey(
        autoGenerateKeyFromName(formContext.getValues("name"), {
          existingKeys
        })
      );
    }
    // If it's the first render, we set the initial suggested key
    isMounted.current = true;
  }, [existingKeys, formContext]);

  return (
    <Inline
      gap="100"
      width="100"
      style={{
        maxWidth: "100%"
      }}
    >
      <Box
        width="100"
        style={{
          maxWidth: "calc(100% - 150px - var(--spacing-100))"
        }}
      >
        <Form.TextField
          name="name"
          label={nameLabel}
          required
          autoFocus
          onChange={(input: string) => {
            setSuggestedKey(
              autoGenerateKeyFromName(input, {
                existingKeys
              })
            );
          }}
          width="100"
          disabled={disabled}
        />
      </Box>
      <Box style={{ minWidth: "150px", maxWidth: "150px", width: "150px" }}>
        <Form.TextField
          width="100"
          name="key"
          label={keyLabel}
          tooltip={keyTooltip}
          placeholder={suggestedKey}
          onKeyDown={
            keyDisabled
              ? undefined
              : (event: React.KeyboardEvent<HTMLInputElement>) => {
                  if (event.key === "Tab" && !event?.shiftKey) {
                    // Set the key to the suggested key if it's not already set
                    if (!formContext.getValues("key") && suggestedKey) {
                      event.preventDefault();

                      formContext.setValue("key", suggestedKey ?? "");
                      afterKeyChange?.(suggestedKey ?? "");
                    }
                  }
                }
          }
          onChange={
            keyDisabled
              ? undefined
              : (value: string) => {
                  if (!keyAllowLowerCase) {
                    const upperCaseValue = value.toUpperCase();
                    if (value !== upperCaseValue) {
                      formContext.setValue("key", value.toUpperCase());
                    }
                  }
                  afterKeyChange?.(value);
                }
          }
          regex={/^([A-Za-z][A-Za-z0-9_]*|)$/}
          error={
            keyError ?? formContext.formState.errors.key?.message?.toString()
          }
          disabled={disabled || keyDisabled}
        />
      </Box>
    </Inline>
  );
};
