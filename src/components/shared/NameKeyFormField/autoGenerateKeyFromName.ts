import { MAX_KEY_LENGTH, MIN_KEY_LENGTH } from "@src/constants/errorMessages";

const getIdealKey = (
  name: string,
  { min, max }: { min: number; max: number }
): string | undefined => {
  const words = name.trim().split(" ");

  if (words.length === 0) {
    return undefined;
  }

  const upperCaseLettersAndFullNumbers = words.map(word =>
    word.trim().replace(/[^A-Z0-9]/g, "")
  );

  const keySuggestion1 = (upperCaseLettersAndFullNumbers ?? []).join("");
  if (keySuggestion1.length >= min && keySuggestion1.length <= max) {
    return keySuggestion1;
  }

  if (words.length === 1) {
    const word = words[0].trim().replace(/[^A-Za-z0-9]/g, "");
    if (word.length < min) {
      return undefined;
    }
    const autoGeneratedWithinWord =
      word.charAt(0).toUpperCase() + word.charAt(1).toUpperCase();
    return autoGeneratedWithinWord;
  }
  const keySuggestion2 = words
    .map(word =>
      word
        .replace(/[^A-Za-z0-9]/g, "")
        .charAt(0)
        .toUpperCase()
    )
    .join("");

  if (keySuggestion2.length > max) {
    return keySuggestion2.slice(0, max);
  }
  return keySuggestion2.length >= min ? keySuggestion2 : undefined;
};

export const autoGenerateKeyFromName = (
  name: string = "",
  options?: {
    min?: number;
    max?: number;
    existingKeys?: string[];
  }
): string | undefined => {
  const idealKey = getIdealKey(name, {
    min: options?.min ?? MIN_KEY_LENGTH,
    max: options?.max ?? MAX_KEY_LENGTH
  });

  if (!idealKey) {
    return undefined;
  }

  // add number if the key already exists
  let autoGeneratedKey = idealKey;
  let counter = 1;
  while ((options?.existingKeys ?? []).includes(autoGeneratedKey)) {
    autoGeneratedKey = `${idealKey}${counter}`;
    counter++;
  }
  return autoGeneratedKey;
};
