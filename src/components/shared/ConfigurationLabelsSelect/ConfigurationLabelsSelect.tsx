import React, { useMemo } from "react";

import {
  Color,
  Form,
  MultiSelect,
  MultiSelectProps,
  SelectValue
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import {
  LabelAvailableTo,
  LabelColor,
  labelColorToOptionColor
} from "@src/types/Label";
import { WorkspaceDocument } from "@src/types/documentTypes";

export const ConfigurationLabelsSelect = ({
  name,
  value,
  onChange,
  labelFor,
  disabled,
  placeholder,
  label,
  width,
  isInForm = false,
  withSelectAll
}: {
  name?: string;
  value?: string[];
  onChange?: (labels: string[]) => void;
  labelFor: `${LabelAvailableTo}`;
  disabled?: boolean;
  label?: string;
  placeholder?: MultiSelectProps["placeholder"];
  width?: MultiSelectProps["width"];
  isInForm?: boolean;
  withSelectAll?: MultiSelectProps["withSelectAll"];
}) => {
  const { document } = useOutletContext<{
    document: WorkspaceDocument;
  }>();

  const labels = useMemo(() => document.labels ?? {}, [document.labels]);
  const configurationLabelOptions = useMemo(
    () =>
      Object.values(labels)
        .filter(label => label.availableTo?.includes(labelFor))
        .sort((a, b) => a.name.localeCompare(b.name))
        .map(label => ({
          value: label.id,
          label: label.name
        })),
    [labels, labelFor]
  );

  const props: MultiSelectProps = useMemo(
    () => ({
      name,
      label,
      options: configurationLabelOptions,
      disabled,
      onChange: onChange as
        | ((value?: SelectValue | undefined) => void)
        | undefined,
      renderOptionLeftElement: ({ option }) => {
        if (!option) {
          return;
        }
        const label = labels[option.value];
        return (
          <Color
            value={labelColorToOptionColor[label.color ?? LabelColor.COLOR_1]}
            size={16}
          />
        );
      },
      labelFor,
      fitOptionText: true,
      width,
      placeholder,
      withSelectAll
    }),
    [
      name,
      label,
      configurationLabelOptions,
      disabled,
      onChange,
      labelFor,
      width,
      placeholder,
      withSelectAll,
      labels
    ]
  );

  if (!isInForm) {
    return <MultiSelect {...props} fitOptionText value={value} />;
  }
  return (
    <Form.MultiSelect
      {...props}
      name={name ?? "labels"}
      {...(value ? { value } : {})}
    />
  );
};
