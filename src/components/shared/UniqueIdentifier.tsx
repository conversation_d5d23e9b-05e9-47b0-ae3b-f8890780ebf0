import React from "react";

import {
  Box,
  Form,
  IconButton,
  Inline,
  TextFieldProps
} from "@oneteam/onetheme";

import { useDictionary } from "@src/hooks/useDictionary";
import { FormPath } from "@src/types/Form";

export const UniqueIdentifierField = (
  props: TextFieldProps & {
    autoGeneratedIdentifier: string;
    configPath: FormPath;
    identifier: string;
    onRefresh?: () => void;
    disabled?: boolean;
  }
) => {
  const {
    autoGeneratedIdentifier,
    identifier,
    onRefresh,
    disabled,
    ...tfProps
  } = props;
  const d = useDictionary();

  return (
    <Inline position="relative">
      <Form.TextField
        name={props.name ?? "identifier"}
        {...tfProps}
        allowClear={false}
        description={d(
          "ui.configuration.forms.question.identifier.description"
        )}
        regex={/^[A-Za-z][A-Za-z0-9_]*$/}
        disabled={disabled}
        onlyTriggerChangeWhenBlur
      />

      {autoGeneratedIdentifier !== identifier && (
        <Box
          position="absolute"
          style={{
            right: "var(--spacing-100)",
            bottom: 0,
            height: "var(--components-inputs-height, 32px)"
          }}
          alignment="center"
        >
          <IconButton
            name="autorenew"
            onClick={onRefresh}
            disabled={disabled}
          />
        </Box>
      )}
    </Inline>
  );
};
