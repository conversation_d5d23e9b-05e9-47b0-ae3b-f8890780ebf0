import React, {
  CSSProperties,
  PropsWithChildren,
  ReactNode,
  useEffect,
  useRef,
  useState
} from "react";

import {
  Box,
  Breadcrumbs,
  BreadcrumbsItem,
  BreadcrumbsItemType,
  Divider,
  DividerDirection,
  Floating,
  FloatingPosition,
  FontWeight,
  Heading,
  HeadingSize,
  Inline,
  Stack,
  Text,
  WhiteboardTool,
  WhiteboardToolVariant,
  WhiteboardToolbar
} from "@oneteam/onetheme";
import {
  ReactZoomPanPinchRef,
  TransformComponent,
  TransformWrapper,
  useControls,
  useTransformEffect
} from "react-zoom-pan-pinch";

import "./WhiteboardPageBodyTemplate.scss";
import { useCursorPositionStore } from "./useCursorPositionStore";

interface WhiteboardPageBodyTemplateProps {
  handlePanning?: () => void;
  isCursorOnElement?: boolean;
  name: string | ReactNode;
  isRenamable?: boolean;
  breadcrumbs?: BreadcrumbsItemType[];
  floatingLayer?: ({
    parentRef
  }: {
    parentRef: React.RefObject<HTMLDivElement>;
  }) => React.ReactNode;
  headingPills?: React.ReactNode;
  headingFilters?: React.ReactNode;
  toolbar?: React.ReactNode;
  modeSelection?: React.ReactNode;
  className?: string;
}

const transition = "all 0.2s";

export const WhiteboardPageBodyTemplate = ({
  handlePanning,
  name,
  isRenamable = true,
  headingPills,
  headingFilters = <></>,
  breadcrumbs = [],
  floatingLayer, // Made up of <Floating>{children}</Floating> components
  toolbar,
  modeSelection,
  className = "",
  children
}: PropsWithChildren<WhiteboardPageBodyTemplateProps>) => {
  const isCursorOnElement = useCursorPositionStore(state => state.isOnElement);
  const [floatingLayerStyle, setFloatingLayerStyle] = useState<CSSProperties>({
    opacity: 0,
    transition
  });
  const isMounted = useRef(false);
  const parentRef = useRef<HTMLDivElement>(null);
  const ref = useRef<ReactZoomPanPinchRef>(null);

  useEffect(() => {
    isMounted.current = true;

    if (isMounted.current) {
      setTimeout(() => {
        setFloatingLayerStyle({ opacity: 1, transition });
      }, 100);
    }

    return () => {
      setFloatingLayerStyle({ opacity: 0, transition });
      isMounted.current = false;
    };
  }, []);

  const blurCurrentInput = () => {
    (document.activeElement as HTMLElement)?.blur();
  };

  const [cursorOnIcon, setCursorOnIcon] = useState(false);

  useEffect(() => {
    const handleMouseEvent = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const isReorderIcon = target.closest(".reorder-icon");

      if (isReorderIcon) {
        setCursorOnIcon(event.type === "mouseover");
      }
    };
    document.addEventListener("mouseover", handleMouseEvent);
    document.addEventListener("mouseout", handleMouseEvent);

    return () => {
      document.removeEventListener("mouseover", handleMouseEvent);
      document.removeEventListener("mouseout", handleMouseEvent);
    };
  });

  return (
    <TransformWrapper
      ref={ref}
      doubleClick={{ disabled: true }}
      limitToBounds={false}
      minScale={0.1}
      maxScale={1.5}
      onPanningStart={blurCurrentInput}
      smooth
      onTransformed={handlePanning}
      initialScale={1}
      initialPositionY={150}
      panning={{
        wheelPanning: !isCursorOnElement,
        allowMiddleClickPan: true,
        excluded: ["input", "textarea", "button", "td"]
      }}
      centerZoomedOut={false}
      wheel={{
        step: 0.002,
        smoothStep: 0.005,
        wheelDisabled: true
      }}
      zoomAnimation={{ disabled: true }}
      pinch={{ step: 0.003 }}
      disabled={cursorOnIcon}
    >
      <Stack
        classNames={["whiteboard-page-body-template", className]}
        gap="200"
        width="100"
        height="100"
        position="relative"
        alignment="center"
        overflow="hidden"
      >
        <Box
          width="100"
          height="100"
          className="whiteboard-page-body-template__floating-elements"
          position="relative"
          ref={parentRef}
          style={floatingLayerStyle}
        >
          <Floating parentRef={parentRef} position={FloatingPosition.TOP_LEFT}>
            <Stack gap="025" className="whiteboard-page-body-template__heading">
              {breadcrumbs?.length > 0 && (
                <Breadcrumbs>
                  {breadcrumbs?.map(breadcrumb => (
                    <BreadcrumbsItem key={breadcrumb.href} {...breadcrumb} />
                  ))}
                </Breadcrumbs>
              )}
              <Stack gap="075" alignment="left">
                <Inline
                  gap="025"
                  className="whiteboard-page-body-template__heading__content"
                  alignment="left"
                >
                  <Heading
                    weight={
                      isRenamable ? FontWeight.REGULAR : FontWeight.MEDIUM
                    }
                    size={HeadingSize.L}
                  >
                    {name}
                  </Heading>
                  {headingPills}
                </Inline>
                {headingFilters}
              </Stack>
            </Stack>
          </Floating>
          <Floating
            parentRef={parentRef}
            position={FloatingPosition.BOTTOM_RIGHT}
          >
            <WhiteboardPageZoomControls />
          </Floating>

          <Floating
            parentRef={parentRef}
            position={FloatingPosition.BOTTOM_CENTER}
          >
            {modeSelection ?? (
              <WhiteboardToolbar>
                <WhiteboardTool icon={{ name: "arrow_selector_tool" }} />
                <WhiteboardTool icon={{ name: "edit" }} isSelected />
                <WhiteboardTool icon={{ name: "approval_delegation" }} />
              </WhiteboardToolbar>
            )}
          </Floating>

          {floatingLayer?.({ parentRef })}
          <Floating
            parentRef={parentRef}
            position={FloatingPosition.TOP_CENTER}
          >
            {toolbar ?? (
              <WhiteboardToolbar
                style={{
                  padding: "var(--spacing-050, 4px)",
                  gap: "var(--components-whiteboard-toolbar-gap, 8px)"
                }}
              >
                <WhiteboardTool
                  variant={WhiteboardToolVariant.TEXT}
                  icon={{ name: "center_focus_weak" }}
                  text="File"
                />
                <WhiteboardTool
                  variant={WhiteboardToolVariant.TEXT}
                  icon={{ name: "center_focus_weak" }}
                  text="Edit"
                />
                <WhiteboardTool
                  variant={WhiteboardToolVariant.TEXT}
                  icon={{ name: "center_focus_weak" }}
                  text="View"
                />
              </WhiteboardToolbar>
            )}
          </Floating>
        </Box>
        <Box
          position="absolute"
          width="100"
          height="100"
          alignment="top-center"
          overflow="auto"
        >
          <TransformComponent
            wrapperStyle={{ width: "100%", height: "100%" }}
            contentStyle={{ width: "100%", height: "fit-content" }}
          >
            <Box width="100" height="100" alignment="top-center">
              {children}
            </Box>
          </TransformComponent>
        </Box>
      </Stack>
    </TransformWrapper>
  );
};

const WhiteboardPageZoomControls = () => {
  const [zoom, setZoom] = useState(100);
  const { zoomIn, zoomOut, centerView, resetTransform } = useControls();

  useTransformEffect(({ state }) => {
    const oneHundredPercentScale = 1;
    const currentScaleAsPercentage = Math.round(
      (state.scale / oneHundredPercentScale) * 100
    );
    setZoom(currentScaleAsPercentage);
  });

  return (
    <WhiteboardToolbar isRounded>
      <WhiteboardTool
        variant={WhiteboardToolVariant.ROUNDED}
        icon={{ name: "reset_focus" }}
        onClick={() => {
          resetTransform();
        }}
      />
      <WhiteboardTool
        variant={WhiteboardToolVariant.ROUNDED}
        icon={{ name: "center_focus_weak" }}
        onClick={() => {
          centerView();
        }}
      />
      <Divider direction={DividerDirection.VERTICAL} />
      <WhiteboardTool
        variant={WhiteboardToolVariant.ROUNDED}
        icon={{ name: "remove" }}
        onClick={() => zoomOut()}
      />
      <Text>{zoom}%</Text>
      <WhiteboardTool
        variant={WhiteboardToolVariant.ROUNDED}
        icon={{ name: "add" }}
        onClick={() => zoomIn()}
      />
    </WhiteboardToolbar>
  );
};
