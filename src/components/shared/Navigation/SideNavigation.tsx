import React, { useCallback, useMemo, useState } from "react";

import {
  <PERSON><PERSON>,
  BadgeVariant,
  Divider,
  DividerSize,
  Inline,
  SideNavigation as OneThemeSideNavigation,
  SideNavigationItem,
  SideNavigationItemGroup,
  Stack,
  Text,
  Tooltip
} from "@oneteam/onetheme";
import flatMap from "lodash/flatMap";
import { generatePath, useLocation, useParams } from "react-router-dom";

import { DocumentErrorHelper } from "@helpers/DocumentErrorHelper.ts";

import { PublishButton } from "@pages/configuration/buttons/PublishButton.tsx";
import { PushToastNotification } from "@pages/workspace/WorkspaceLayout.tsx";

import { UserLastActiveTabs } from "@src/authentication/AuthContext.tsx";
import { routeConstants } from "@src/constants/routeConstants.ts";
import { useAuth } from "@src/hooks/useAuth.tsx";
import { useBuild } from "@src/hooks/useBuild.tsx";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { useMatchPath } from "@src/hooks/useMatchPath.tsx";
import { useNavigateOnClick } from "@src/hooks/useNavigateOnClick.tsx";
import { Workspace } from "@src/types/workspace.ts";

import { ViewAsDocumentButton } from "../ViewAsDocumentButton.tsx";
import "./SideNavigation.scss";
import {
  ContextProperties,
  Resource,
  SideNavigationContext,
  SideNavigationItemType
} from "./sideNavigationContexts.ts";

interface SideNavigationProps {
  context: SideNavigationContext;
  workspace?: Workspace;
  docErrorHelper?: DocumentErrorHelper;
  pushToastNotification: PushToastNotification;
}

export const SideNavigation = ({
  context,
  workspace,
  docErrorHelper,
  pushToastNotification
}: SideNavigationProps) => {
  const d = useDictionary();
  const { feBuild, beBuild } = useBuild();
  const navigateOnClick = useNavigateOnClick();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { matchPath } = useMatchPath();
  const { workspaceKey } = useParams();
  const { lastActiveTabs, updateLastActiveTabs } = useAuth();
  const { pathname, search, hash } = useLocation();

  const hasErrors = useMemo(() => {
    return docErrorHelper?.hasErrors() ?? false;
  }, [docErrorHelper]);

  const activeSideNavigationItem = useMemo(
    () =>
      flatMap([
        ...(context.content?.top?.map(group => group.items) ?? []),
        ...(context.content?.bottom?.map(group => group.items) ?? [])
      ]).find(item => item.isActive?.(matchPath)),
    [context.content?.bottom, context.content?.top, matchPath]
  );

  const navigateToSideNavigationItem = useCallback(
    (href: string) => (e: React.MouseEvent) => {
      // If the href is the same as the current path, do nothing
      if (matchPath(href, { exact: true }) && !e.metaKey) {
        return;
      }

      const lastActivePathAtDestination = lastActiveTabs[href];
      const isNavigateToSameTab = matchPath(href, { exact: false });

      if (activeSideNavigationItem) {
        const currentActiveTab = `${pathname}${search}${hash}`;
        updateLastActiveTabs({
          ...lastActiveTabs,
          [activeSideNavigationItem.href]: currentActiveTab
        } as UserLastActiveTabs);
      }

      if (!isNavigateToSameTab && lastActivePathAtDestination) {
        navigateOnClick(lastActivePathAtDestination)(e);
      } else {
        navigateOnClick(generatePath(href, { workspaceKey }))(e);
      }
    },
    [
      activeSideNavigationItem,
      hash,
      lastActiveTabs,
      matchPath,
      navigateOnClick,
      pathname,
      search,
      updateLastActiveTabs,
      workspaceKey
    ]
  );

  const prefix = useCallback((resourceName: Resource | undefined) => {
    if (!resourceName) {
      return "";
    }
    return `$.${resourceName}`;
  }, []);

  const sideNavItemBase = useCallback(
    (
      item: SideNavigationItemType,
      rightElement?: React.ReactNode
    ): React.ReactNode => {
      const renderedItem = (
        <SideNavigationItem
          icon={item.icon}
          key={item.href}
          onClick={navigateToSideNavigationItem(item.href)}
          isActive={activeSideNavigationItem?.href === item.href}
          rightElement={rightElement}
        >
          {d(item.name)}
        </SideNavigationItem>
      );

      if (isCollapsed) {
        return (
          <Tooltip content={d(item.name)} delay={500}>
            {renderedItem}
          </Tooltip>
        );
      }
      return renderedItem;
    },
    [
      activeSideNavigationItem?.href,
      d,
      isCollapsed,
      navigateToSideNavigationItem
    ]
  );

  const topSideNavItem: (item: SideNavigationItemType) => React.ReactNode =
    useCallback(
      (item: SideNavigationItemType): React.ReactNode => {
        const errorCount =
          docErrorHelper?.getErrorCountOfErroneousEntitiesByPrefix(
            prefix(item.resource)
          ) ?? 0;
        if (errorCount <= 0) {
          return sideNavItemBase(item);
        }

        if (isCollapsed) {
          return (
            <Badge variant={BadgeVariant.DANGER} count={errorCount} width="fit">
              {sideNavItemBase(item)}
            </Badge>
          );
        }

        // Show badge in rightElement if there are errors && !isCollapsed
        return sideNavItemBase(
          item,
          <Badge variant={BadgeVariant.DANGER} count={errorCount} width="fit" />
        );
      },
      [docErrorHelper, isCollapsed, prefix, sideNavItemBase]
    );

  const heading = useMemo(() => {
    if (context?.heading) {
      return d(context.heading);
    }

    return workspace?.name ?? "";
  }, [context?.heading, d, workspace]);

  if (!context) {
    return undefined;
  }

  if (context.requires?.includes(ContextProperties.WORKSPACE) && !workspace) {
    // TODO: change depending on URL
    return undefined;
  }

  return (
    <OneThemeSideNavigation
      heading={heading}
      headingMaxLines={context.headingMaxLines}
      isCollapsed={isCollapsed}
      onChangeCollapse={collapse => setIsCollapsed(collapse)}
    >
      {context.content?.top && (
        <Stack width="100" height="100" className="side-navigation__top">
          {context.content?.top.map((group, i) => (
            <Stack key={group.name} className="side-navigation__top-group">
              {i !== 0 && <Divider size={DividerSize.SMALL} />}
              <SideNavigationItemGroup
                title={
                  group.shouldDisplayName && !isCollapsed ? d(group.name) : ""
                }
              >
                {group.items.map(item => topSideNavItem(item))}
              </SideNavigationItemGroup>
            </Stack>
          ))}
        </Stack>
      )}
      {context.content?.bottom && (
        <Stack width="100" className="side-navigation__bottom">
          {context.content?.bottom.map((group, i) => (
            <React.Fragment key={group.name}>
              {/* TODO: This section should really be coming from Context, Then SideNavigation is more generic */}
              {i === (context.content?.bottom ?? [])?.length - 1 &&
                pathname.startsWith(
                  generatePath(routeConstants.configuration, { workspaceKey })
                ) && (
                  <>
                    <Divider size={DividerSize.SMALL} />
                    {workspace!.documentId && (
                      <ViewAsDocumentButton
                        documentId={workspace!.documentId}
                      />
                    )}
                    <PublishButton
                      workspace={workspace!}
                      hasErrors={hasErrors}
                      pushToastNotification={pushToastNotification}
                    />
                  </>
                )}
              {i !== 0 && <Divider size={DividerSize.SMALL} />}
              <SideNavigationItemGroup
                title={
                  group.shouldDisplayName && !isCollapsed ? d(group.name) : ""
                }
              >
                {group.items.map(item => sideNavItemBase(item))}
              </SideNavigationItemGroup>
            </React.Fragment>
          ))}
          <Inline
            gap="025"
            alignment="left"
            width="100"
            style={{
              padding: "0 var(--spacing-100)"
            }}
          >
            <Text size="xs" color="text-tertiary">
              <Text size="inherit" weight="semi-bold">
                FE:
              </Text>
              {feBuild?.version}
              {" - "}
              <Text size="inherit" weight="semi-bold">
                BE:
              </Text>
              {beBuild?.version}
            </Text>
          </Inline>
        </Stack>
      )}
    </OneThemeSideNavigation>
  );
};
