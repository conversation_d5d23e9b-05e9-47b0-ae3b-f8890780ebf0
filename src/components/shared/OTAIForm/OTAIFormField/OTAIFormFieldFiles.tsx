import React, { useCallback, useEffect, useState } from "react";

import {
  Attachment,
  AttachmentGroup,
  FileUpload,
  Loading,
  Stack
} from "@oneteam/onetheme";

import { downloadFile, uploadFile } from "@helpers/files.ts";

import {
  MAX_FILE_SIZE_MB,
  MAX_NUMBER_OF_FILES,
  fileFormatsToMimeType
} from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Files/FilesHelper";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";

import { useDictionary } from "@src/hooks/useDictionary";
import { Question } from "@src/types/Question";
import { FilesQuestionProperties } from "@src/types/QuestionProperties";

import { OTAIFormFieldCommonProps } from "../OTAIFormType";

type UploadingFile = { status: string; file: File; path: string };

export const OTAIFormFieldFiles = ({
  id,
  question,
  commonProps,
  value = [],
  onChange: onChangeProp,
  disabled
}: {
  id: string;
  question: Question<FilesQuestionProperties>;
  commonProps: OTAIFormFieldCommonProps;
  value: { path: string; name: string }[];
  onChange?: (value: unknown) => void;
  disabled?: boolean;
}) => {
  const restrictedFileTypes = fileFormatsToMimeType(
    question.properties?.restrictedFileTypes
  );
  const d = useDictionary();
  const { collectionFormId } = useCollectionFormContext();

  const [uploading, setUploading] = useState<UploadingFile[]>([]);
  const [transientError, setTransientError] = useState<string | undefined>();

  const validateFileSize = useCallback(
    (file: File) => {
      const maxFileSizeMB = Math.min(
        question.properties?.maxFileSizeMB ?? MAX_FILE_SIZE_MB,
        MAX_FILE_SIZE_MB
      );
      if (maxFileSizeMB === undefined) {
        return true;
      }

      const maxFileSizeBytes = maxFileSizeMB * 1024 * 1024;
      if (file.size > maxFileSizeBytes) {
        setTransientError(
          d(
            "errors.configurationForm.question.files.maxFileSize.fileTooLarge",
            { maxFileSize: maxFileSizeMB }
          )
        );
        return false;
      }
      return true;
    },
    [d, question.properties]
  );

  useEffect(() => {
    setTransientError(undefined);
    const min = question.properties?.min ?? null;
    if (min && value.length < min && value.length !== 0) {
      setTransientError(
        d("errors.configurationForm.question.files.min.notEnoughFiles", {
          min
        })
      );
    }
  }, [question.properties, value.length, d]);

  useEffect(() => {
    for (const file of value) {
      setUploading(old =>
        old.filter(uploadingFile => uploadingFile.file.name !== file.name)
      );
    }
  }, [value]);

  const validateNumberOfFiles = useCallback(
    (numberOfFiles: number) => {
      const max = Math.min(
        question.properties?.max ?? MAX_NUMBER_OF_FILES,
        MAX_NUMBER_OF_FILES
      );
      if (max && numberOfFiles + value.length > max) {
        setTransientError(
          d("errors.configurationForm.question.files.max.tooManyFiles", { max })
        );
        return false;
      }
      return true;
    },
    [question.properties, d, value]
  );

  const onChange = useCallback(
    (_: string | string[], fileList: FileList | null) => {
      setTransientError(undefined);
      if (!fileList) {
        alert(d("errors.configurationForm.question.files.noFilesSelected"));
        return;
      }
      if (!validateNumberOfFiles(fileList.length ?? 0)) {
        return;
      }

      for (const file of fileList) {
        if (!validateFileSize(file)) {
          return;
        }
      }
      const newFiles = Array.from(fileList);

      setUploading(old => {
        return old.concat(
          newFiles?.map(file => ({
            status: "new",
            file,
            path: ""
          }))
        );
      });
      if (collectionFormId != undefined) {
        newFiles.forEach(file =>
          uploadFile(collectionFormId, question.id, file).then(result => {
            if (result.status === 200) {
              const newFile = { path: result.path ?? "", name: file.name };
              onChangeProp?.({ op: "ADD", file: newFile });
            }
            setUploading(old =>
              old.map(uploadingFile =>
                uploadingFile.file.name === file.name
                  ? { ...uploadingFile, path: result.path ?? "" }
                  : uploadingFile
              )
            );
          })
        );
      }
      return true; // that will reset the list of files!
    },
    [
      validateNumberOfFiles,
      collectionFormId,
      validateFileSize,
      question.id,
      onChangeProp,
      d
    ]
  );

  const uploadInstructionsKey =
    "ui.configuration.forms.question.files.uploadInstructions";

  // TODO: change to Form.FileUpload and use zod
  return (
    <Stack
      alignment="center"
      contentsHeight="fill"
      contentsWidth="fill"
      gap="100"
      height="fill"
      width="fill"
    >
      {value?.length > 0 && (
        <AttachmentGroup>
          {value.map(file => {
            if (
              uploading.find(uploadingFile => file.path === uploadingFile.path)
            ) {
              return null;
            }

            return (
              <Attachment
                key={file.path}
                file={{ name: file.name }}
                onClickRemove={
                  disabled || commonProps.disabled
                    ? undefined
                    : () => onChangeProp?.({ op: "DELETE", file })
                }
                onClickDownload={async () => {
                  if (collectionFormId != undefined) {
                    await downloadFile(
                      file,
                      String(collectionFormId),
                      question.id
                    );
                  }
                }}
              />
            );
          })}
        </AttachmentGroup>
      )}
      {uploading?.length > 0 && (
        <AttachmentGroup>
          {uploading.map(uploadingFile => (
            <Attachment
              key={uploadingFile.file.name}
              file={{ name: uploadingFile.file.name }}
              rightElement={() => <Loading size={18} />}
            />
          ))}
        </AttachmentGroup>
      )}
      <FileUpload
        {...commonProps}
        placeholder={d(`${uploadInstructionsKey}.placeholder`)}
        orText={d(`${uploadInstructionsKey}.orText`)}
        browseButtonLabel={d(`${uploadInstructionsKey}.browseButtonLabel`)}
        name={id}
        multiple={true}
        acceptableTypes={restrictedFileTypes}
        error={transientError}
        onChange={onChange}
        disabled={
          disabled ||
          commonProps.disabled ||
          value.length >= (question.properties?.max ?? MAX_NUMBER_OF_FILES) ||
          uploading.length > 0
        }
        displayFilesUploaded={false}
      />
    </Stack>
  );
};
