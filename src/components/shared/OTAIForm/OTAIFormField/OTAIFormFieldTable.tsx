import React, { useCallback, useEffect, useMemo, useState } from "react";

import {
  Box,
  Button,
  FileUpload,
  IconFillStyle,
  Inline,
  Label,
  Stack,
  TableField,
  TableFieldCellValue,
  TableFieldColumnId,
  TableFieldColumns,
  TableFieldReorderRow,
  TableFieldRowValue,
  TableFieldSetColumn,
  TableFieldSetRow,
  TableFieldSetTable,
  TableFieldValue,
  ToastNotificationVariant
} from "@oneteam/onetheme";
import { range } from "lodash";
import { useOutletContext } from "react-router-dom";

import {
  addItem,
  mapOverResource,
  removeItem
} from "@helpers/OrderedMapNoState";
import { getByPath } from "@helpers/automergeDocumentHelper";
import { downloadTableData, uploadTableDataForPrefill } from "@helpers/files";
import { updateAnswer } from "@helpers/forms/answerHelper";

import {
  MAX_FILE_SIZE_MB,
  fileFormatsToMimeType
} from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Files/FilesHelper";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";
import { HighlightAnnotationLocation } from "@pages/collection/forms/HighlightAnnotationLocation/HighlightAnnotationLocation";
import { LabelWithHighlight } from "@pages/collection/forms/LabelWithHighlight";
import { CollectionQuestionBlockAlert } from "@pages/collection/forms/questions/CollectionQuestionBlock/CollectionQuestionBlockAlert/CollectionQuestionBlockAlert";
import { CollectionQuestionBlockComment } from "@pages/collection/forms/questions/CollectionQuestionBlock/CollectionQuestionBlockComment/CollectionQuestionBlockComment";
import { QuestionAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionAnswer";
import { QuestionTableHelper } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionTableHelper";
import { PushToastNotification } from "@pages/workspace/WorkspaceLayout";

import { useDictionary } from "@src/hooks/useDictionary";
import { Question, QuestionTypes } from "@src/types/Question";
import { TableQuestionProperties } from "@src/types/QuestionProperties";
import {
  FormAnswer,
  TableAnswer,
  TableAnswerRow
} from "@src/types/collection/CollectionForm";
import { Resource } from "@src/types/documentTypes";

import { OTAIFormFieldCommonProps } from "../OTAIFormType";
import {
  mapOverCellValues,
  setRowCellValue
} from "../helpers/OTAIFormFieldTableHelper";
import "./OTAIFormFieldTable.scss";

export const OTAIFormFieldTable = ({
  id,
  question,
  commonProps,
  answer
}: {
  id: string;
  question: Question<TableQuestionProperties>;
  commonProps: OTAIFormFieldCommonProps;
  answer?: TableAnswer;
}) => {
  const d = useDictionary();
  const showPreviewAnswerRow = useMemo(() => {
    const columns = question.properties?.columns ?? [];
    return columns.length > 0;
  }, [question.properties?.columns]);

  const { pushToastNotification } = useOutletContext<{
    pushToastNotification: PushToastNotification;
  }>();
  const { docChange, collectionFormId, documentId } =
    useCollectionFormContext();
  const restrictedFileTypes = fileFormatsToMimeType(["spreadsheet"]);
  const [transientError, setTransientError] = useState<string | undefined>();
  const [stickyColumnId, setStickyColumnId] = useState<string | undefined>();
  const validateFileSize = useCallback(
    (file: File) => {
      const maxFileSizeBytes = MAX_FILE_SIZE_MB * 1024 * 1024;
      if (file.size > maxFileSizeBytes) {
        setTransientError(
          d(
            "errors.configurationForm.question.files.maxFileSize.fileTooLarge",
            { maxFileSize: MAX_FILE_SIZE_MB }
          )
        );
        return false;
      }
      return true;
    },
    [d]
  );

  const questionDisabled = useMemo(
    () => question.properties?.disabled || commonProps.disabled,
    [question.properties?.disabled, commonProps.disabled]
  );

  const downloadDataFile = useCallback(async () => {
    if (!collectionFormId) {
      return;
    }

    pushToastNotification(
      d("ui.forms.question.table.notifications.download.started.heading"),
      d("ui.forms.question.table.notifications.download.started.description"),
      ToastNotificationVariant.INFO
    );

    await downloadTableData(
      collectionFormId,
      question.id,
      question.identifier
    ).catch(() => {
      pushToastNotification(
        d("ui.forms.question.table.notifications.download.failed.heading"),
        d("ui.forms.question.table.notifications.download.failed.description"),
        ToastNotificationVariant.DANGER
      );
    });
  }, [
    pushToastNotification,
    d,
    collectionFormId,
    question.id,
    question.identifier
  ]);

  const onChange = useCallback(
    (_: string | string[], fileList: FileList | null) => {
      setTransientError(undefined);
      if (!collectionFormId) {
        return;
      }
      if (!fileList) {
        alert(d("errors.configurationForm.question.files.noFilesSelected"));
        return;
      }

      for (const file of fileList) {
        if (!validateFileSize(file)) {
          return;
        }
      }

      // NB: We expect at most 1 file
      const newFiles = Array.from(fileList);
      newFiles.forEach(file => {
        pushToastNotification(
          d("ui.forms.question.table.notifications.upload.started.heading"),
          d("ui.forms.question.table.notifications.upload.started.description"),
          ToastNotificationVariant.INFO
        );

        uploadTableDataForPrefill(collectionFormId, question.id, file)
          .then(({ status }) => {
            if (status === 500) {
              throw new Error("Failed to process file");
            }

            pushToastNotification(
              d("ui.forms.question.table.notifications.upload.success.heading"),
              d(
                "ui.forms.question.table.notifications.upload.success.description"
              ),
              ToastNotificationVariant.SUCCESS
            );
          })
          .catch(() => {
            pushToastNotification(
              d("ui.forms.question.table.notifications.upload.failed.heading"),
              d(
                "ui.forms.question.table.notifications.upload.failed.description"
              ),
              ToastNotificationVariant.DANGER
            );
          });
      });
      return true;
    },
    [collectionFormId, validateFileSize, question.id, d, pushToastNotification]
  );

  const questionTableHelper = useMemo(() => {
    return new QuestionTableHelper(question);
  }, [question]);

  const createEmptyAnswerRow = useCallback(() => {
    return addItem(questionTableHelper.createNewRow(), {
      entities: {},
      order: []
    });
  }, [questionTableHelper]);

  useEffect(() => {
    // if there are no rows on the table answer yet, create the first one
    if (docChange) {
      docChange(d => {
        const v = d.answers[question.id]?.value as TableAnswer;
        if (!v?.order?.length && question.properties) {
          const resource = createEmptyAnswerRow();
          d.answers[question.id] = {
            questionId: question.id,
            value: resource,
            type: question.type
          };
        }
      });
    }
  }, [docChange, createEmptyAnswerRow, question]);

  const displayColumnQuestions = useMemo(() => {
    return question.properties?.columns?.filter(c => !c.properties?.hidden);
  }, [question.properties?.columns]);

  const rowValues = useMemo(() => {
    const tableAnswer =
      showPreviewAnswerRow && !docChange ? createEmptyAnswerRow() : answer;
    if (!tableAnswer || !Array.isArray(tableAnswer.order)) {
      return [];
    }
    const rows: TableFieldValue = mapOverResource(tableAnswer, row => {
      const rowData: TableFieldRowValue = {};
      // Map each column value to corresponding questionId
      if (row.columns) {
        Object.entries(row.columns).forEach(([questionId, columnData]) => {
          rowData[questionId] =
            columnData.type === QuestionTypes.LIST
              ? JSON.stringify(columnData.value)
              : columnData.value;
        });
      }

      return rowData;
    });

    return rows as TableFieldValue;
  }, [showPreviewAnswerRow, answer, createEmptyAnswerRow, docChange]);

  const columns = useMemo(() => {
    return displayColumnQuestions?.map(column => ({
      label: column.text,
      name: column.text,
      id: column.id,
      type: column.type,
      properties: {
        ...column.properties
      },

      customHeaderRenderer: (
        <>
          <HighlightAnnotationLocation
            location={{
              variant: "question",
              questionId: question.id,
              columnId: column.id
            }}
          >
            <LabelWithHighlight
              className="table-question__table__header__cell__label"
              label={column?.text}
              required={column?.properties?.required}
              description={column?.description}
            />
          </HighlightAnnotationLocation>
          <CollectionQuestionBlockAlert
            location={{
              variant: "question",
              questionId: question.id,
              columnId: column.id
            }}
            stylingVariant="cellIndicator"
            floatingAnnotationPosition="bottom-left"
          />
          <CollectionQuestionBlockComment
            location={{
              variant: "question",
              questionId: question.id,
              columnId: column.id
            }}
            stylingVariant="cellIndicator"
            floatingAnnotationPosition="bottom-left"
          />
        </>
      ),
      customCellRenderer: ({
        value,
        disabled,
        rowIndex,
        onPaste,
        onKeyDown,
        onFocus,
        onBlur,
        controlFocus
      }: {
        value: TableFieldCellValue;
        disabled?: boolean;
        rowIndex: number;
        onPaste?: React.ClipboardEventHandler<Element>;
        onKeyDown?: React.KeyboardEventHandler<Element>;
        onFocus?: React.FocusEventHandler<Element>;
        onBlur?: React.FocusEventHandler<Element>;
        controlFocus?: boolean;
      }) => {
        const rowId = answer?.order?.[rowIndex];
        if (column.type === QuestionTypes.LIST) {
          value = value ? JSON.parse(value as string) : undefined;
        }
        return (
          <Inline
            className="question-answer"
            height="100"
            width="100"
            alignment="left"
            gap="050"
          >
            <QuestionAnswer
              question={column}
              disabled={
                disabled ||
                column.properties?.disabled ||
                question.properties?.disabled
              }
              answer={value as FormAnswer["value"]}
              answerAccessor={`${id}.entities.${rowId}.columns.${column.id}.value`}
              location={{
                variant: "answer",
                questionId: question.id,
                rowId,
                columnId: column.id
              }}
              onPaste={onPaste}
              onKeyDown={onKeyDown}
              onFocus={onFocus}
              onBlur={onBlur}
              controlFocus={controlFocus}
            />
            <CollectionQuestionBlockAlert
              location={{
                variant: "question",
                questionId: question.id,
                rowId,
                columnId: column.id
              }}
              stylingVariant="cellIndicator"
            />
            <CollectionQuestionBlockComment
              location={{
                variant: "question",
                questionId: question.id,
                rowId,
                columnId: column.id
              }}
              stylingVariant="cellIndicator"
              onFocus={onFocus}
              onBlur={onBlur}
            />
          </Inline>
        );
      }
    }));
  }, [displayColumnQuestions, answer, id, question]);

  const removeRow = useCallback(
    (rowIndex: number) => {
      if (docChange) {
        docChange(d => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            d,
            id.split(".")
          );
          if (!questionAnswers || !questionAnswers.order) {
            console.error("questionAnswers not found", id);
            return;
          }
          const rowId = questionAnswers?.order?.[rowIndex];
          const updatedTable = removeItem(
            rowId,
            JSON.parse(JSON.stringify(questionAnswers))
          );
          updateAnswer(question, updatedTable as TableAnswer, id, documentId);
        });
      }
    },
    [id, question, documentId, docChange]
  );

  const setCell = useCallback(
    (
      cellValue: TableFieldCellValue | undefined,
      rowIndex: number,
      columnId: TableFieldColumnId
    ) => {
      if (docChange) {
        docChange(d => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            d,
            id.split(".")
          );
          if (!questionAnswers || !questionAnswers.order) {
            console.error("questionAnswers not found", id);
            return;
          }

          const rowId = questionAnswers.order[rowIndex];
          if (
            questionAnswers.entities[rowId]?.columns?.[columnId]?.value !==
            cellValue
          ) {
            const answer = questionAnswers.entities[rowId];
            setRowCellValue(
              answer,
              columnId,
              cellValue,
              displayColumnQuestions
            );
          }
        });
      }
    },
    [id, displayColumnQuestions, docChange]
  );

  const setTable: TableFieldSetTable = useCallback(
    (valueToSet: TableFieldValue, options: { fromRowIndex?: number }) => {
      if (docChange) {
        docChange(doc => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            doc,
            id.split(".")
          );
          if (!questionAnswers || !questionAnswers.order) {
            console.error("questionAnswers not found", id);
            return;
          }

          const fromRowIndex = options.fromRowIndex ?? 0;
          const maxRows = Math.max(
            questionAnswers.order.length,
            valueToSet.length + fromRowIndex
          );

          // Process each row
          range(fromRowIndex, maxRows).forEach(rowIndex => {
            const existingRowId = questionAnswers.order[rowIndex];
            const newRowValue = valueToSet[rowIndex - fromRowIndex];

            if (newRowValue && existingRowId) {
              // Update existing row - merge with existing data
              const answer = questionAnswers.entities[existingRowId];
              mapOverCellValues(newRowValue, (columnId, cellValue) => {
                setRowCellValue(
                  answer,
                  columnId,
                  cellValue,
                  displayColumnQuestions
                );
              });
            } else if (newRowValue && !existingRowId) {
              // Add new row
              const newRow = questionTableHelper.createNewRow();
              const newRowId = newRow.id;
              questionAnswers.order.splice(rowIndex, 0, newRowId);

              mapOverCellValues(newRowValue, (columnId, cellValue) => {
                setRowCellValue(
                  newRow,
                  columnId,
                  cellValue,
                  displayColumnQuestions
                );
              });

              questionAnswers.entities[newRowId] = newRow;
            }
          });
        });
      }
    },
    [id, displayColumnQuestions, docChange, questionTableHelper]
  );

  const setColumn: TableFieldSetColumn = useCallback(
    (
      columnValues: TableFieldCellValue[],
      columnId: TableFieldColumnId,
      options?: {
        keepOtherColumnValues?: boolean;
        fromRowIndex?: number;
      }
    ) => {
      if (docChange) {
        docChange(doc => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            doc,
            id.split(".")
          );

          if (!questionAnswers || !questionAnswers.order) {
            console.error("questionAnswers not found", id);
            return;
          }

          const fromRowIndex = options?.fromRowIndex ?? 0;
          const maxRows = Math.max(
            questionAnswers.order.length,
            columnValues.length + fromRowIndex
          );
          // Process each row
          range(fromRowIndex, maxRows).forEach(rowIndex => {
            const newColumnValue = columnValues[rowIndex - fromRowIndex];
            let existingRowId = questionAnswers.order[rowIndex];

            if (newColumnValue !== undefined && existingRowId) {
              const answer = questionAnswers.entities[existingRowId];
              setRowCellValue(
                answer,
                columnId,
                newColumnValue,
                displayColumnQuestions
              );
            } else if (newColumnValue !== undefined && !existingRowId) {
              // Add new row
              const newRow = questionTableHelper.createNewRow();
              const newRowId = newRow.id;
              questionAnswers.order.splice(rowIndex, 0, newRowId);

              setRowCellValue(
                newRow,
                columnId,
                newColumnValue,
                displayColumnQuestions
              );

              questionAnswers.entities[newRowId] = newRow;
            } else if (
              newColumnValue === undefined &&
              existingRowId &&
              !options?.keepOtherColumnValues
            ) {
              // Remove the column value
              delete questionAnswers.entities[existingRowId].columns[columnId];
            }
          });
        });
      }
    },
    [id, displayColumnQuestions, docChange, questionTableHelper]
  );

  const setRow: TableFieldSetRow = useCallback(
    (
      rowValue: TableFieldRowValue = {},
      rowIndex: number = (rowValues as TableFieldValue).length,
      options: {
        newRow?: boolean;
        keepOtherRowValues?: boolean;
      } = {}
    ) => {
      if (docChange) {
        docChange(doc => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            doc,
            id.split(".")
          );
          if (!questionAnswers || !questionAnswers.order) {
            console.error("questionAnswers not found", id);
            return;
          }

          const toDelete =
            !options?.newRow || rowIndex === questionAnswers.order.length
              ? 1
              : 0;
          const existingRowId = questionAnswers.order[rowIndex];

          if (options?.keepOtherRowValues && existingRowId && toDelete === 1) {
            // Merge with existing row
            const answer = questionAnswers.entities[existingRowId];
            mapOverCellValues(rowValue, (columnId, cellValue) => {
              setRowCellValue(
                answer,
                columnId,
                cellValue,
                displayColumnQuestions
              );
            });
          } else {
            if (existingRowId && toDelete === 1) {
              // Remove existing row
              delete questionAnswers.entities[existingRowId];
              questionAnswers.order.splice(rowIndex, 1);
            }

            // Create new row
            const newRow = questionTableHelper.createNewRow();
            const newRowId = newRow.id;
            questionAnswers.order.splice(rowIndex, 0, newRowId);

            mapOverCellValues(rowValue, (columnId, cellValue) => {
              setRowCellValue(
                newRow,
                columnId,
                cellValue,
                displayColumnQuestions
              );
            });
            questionAnswers.entities[newRowId] = newRow;
          }
        });
      }
    },
    [id, displayColumnQuestions, docChange, questionTableHelper, rowValues]
  );

  const reorderRow: TableFieldReorderRow = useCallback(
    (rowIndexToMove: number, toRowIndex: number) => {
      if (docChange) {
        docChange(doc => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            doc,
            id.split(".")
          );
          if (!questionAnswers || !questionAnswers.order) {
            console.error("questionAnswers not found", id);
            return;
          }

          const rowIdToMove = questionAnswers.order[rowIndexToMove];
          questionAnswers.order.splice(rowIndexToMove, 1);
          questionAnswers.order.splice(toRowIndex, 0, rowIdToMove);

          return doc;
        });
      }
    },
    [docChange, id]
  );

  return (
    <Stack gap="025">
      <Label
        label={commonProps.label ?? ""}
        description={commonProps.description}
        required={commonProps.required}
        disabled={commonProps?.disabled}
      />
      <Box width="100" overflow="auto">
        <Stack className="table-question__container">
          <TableField
            id="tableField"
            name="table-field"
            value={rowValues as TableFieldValue}
            setTable={setTable}
            setRow={setRow}
            removeRow={removeRow}
            setColumn={setColumn}
            setCell={setCell}
            reorderRow={reorderRow}
            stickyColumnId={stickyColumnId}
            onChangeStickyColumnId={setStickyColumnId}
            disableAddRow={true}
            maxHeight={`${440}px`}
            columns={(columns as TableFieldColumns) ?? []}
          />
          <Inline alignment="right">
            <Inline gap="150">
              {!questionDisabled && (
                <FileUpload
                  multiple={false}
                  acceptableTypes={restrictedFileTypes}
                  error={transientError}
                  onChange={onChange}
                  trigger={({ onClick }) => (
                    <Button
                      variant="text"
                      leftIcon={{
                        name: "upload",
                        fillStyle: IconFillStyle.FILLED
                      }}
                      onClick={onClick}
                      label={d("ui.forms.question.table.upload")}
                    />
                  )}
                />
              )}
              <Button
                variant="text"
                leftIcon={{ name: "download", fillStyle: IconFillStyle.FILLED }}
                onClick={downloadDataFile}
                label={d("ui.forms.question.table.download")}
              />
            </Inline>
          </Inline>
        </Stack>
      </Box>
    </Stack>
  );
};
