import React, { useEffect, useMemo } from "react";

import { Box, Label, Stack } from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";

import { QuestionTableAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionTableAnswer";

import { useDictionary } from "@src/hooks/useDictionary";
import { Question } from "@src/types/Question";
import { TableQuestionProperties } from "@src/types/QuestionProperties";

import { OTAIFormFieldCommonProps } from "../OTAIFormType";
import "./OTAIFormFieldTable.scss";

export const OTAIFormFieldTable = ({
  id,
  question,
  commonProps
  // TODO: use onChange
  // onChange
}: {
  id: string;
  question: Question<TableQuestionProperties>;
  commonProps: OTAIFormFieldCommonProps;
  // onChange?: (value: unknown) => void;
}) => {
  const d = useDictionary();
  const { watch, setValue } = useFormContext();
  const answers = watch(id) ?? [{}];
  const showPreviewAnswerRow = useMemo(() => {
    const columns = question.properties?.columns ?? [];
    return columns.length > 0;
  }, [question.properties?.columns]);

  useEffect(() => {
    setValue(id, undefined);
  }, [id, question, setValue]);

  return (
    <Stack gap="025">
      <Label
        label={commonProps.label ?? ""}
        description={commonProps.description}
        required={commonProps.required}
        disabled={commonProps?.disabled}
      />
      <Box width="100" overflow="auto">
        <QuestionTableAnswer
          d={d}
          question={question}
          answer={answers}
          answerAccessor={id}
          showPreviewAnswerRow={showPreviewAnswerRow}
          // Currently only used for QuestionPreview
          disableAddRow={true}
          // onChange={onChange}
          disabled={commonProps.disabled}
        />
      </Box>
    </Stack>
  );
};
