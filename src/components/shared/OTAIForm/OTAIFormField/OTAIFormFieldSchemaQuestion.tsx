import React, { useCallback, useEffect, useMemo, useRef } from "react";

import {
  Alert,
  Box,
  JsonEditor,
  Label,
  Stack,
  TabGroup,
  Text
} from "@oneteam/onetheme";
import { isEqual } from "lodash";

import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import { ConfigurationQuestionBlock } from "@components/forms/configuration/question/ConfigurationQuestionBlock/ConfigurationQuestionBlock";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { ConfigurationFormMode } from "@src/types/FormConfiguration.ts";
import { Question } from "@src/types/Question";
import { SchemaAnswer } from "@src/types/collection/CollectionForm.ts";

import { OTAIFormField } from "../OTAIFormField";
import { OTAIFormFieldCommonProps } from "../OTAIFormType";
import { generateSchemaFromSampleData } from "../helpers/generateConfigurationFromSample";
import "./OTAIFormFieldTable.scss";

enum SchemaQuestionTabs {
  SAMPLE = "sample",
  SCHEMA = "schema",
  PREVIEW = "preview"
}

export const OTAIFormFieldSchemaQuestion = ({
  id,
  commonProps,
  onBlur: onBlurProp,
  value
}: {
  id: string;
  commonProps: OTAIFormFieldCommonProps;
  onBlur?: (value: object, identifier: string) => void;
  value: SchemaAnswer;
}) => {
  const d = useDictionary();
  const [tab, setTab] = React.useState(SchemaQuestionTabs.SAMPLE);

  const [tempSample, setTempSample] = React.useState<string>(
    JSON.stringify(structuredClone(value?.sample), null, 2) ?? "{}"
  );
  const [jsonError, setJsonError] = React.useState<string | null>(null);

  const [errorInSchemaGen, setErrorInSchemaGen] = React.useState<boolean>(
    tempSample === "{}"
  );

  const schema = useMemo(() => value?.schema, [value?.schema]);

  const sampleRef = useRef<SchemaAnswer["sample"]>(value?.sample);

  const tryParseJson = useCallback(
    (sample: string, consoleError: boolean = false) => {
      let result = null;
      try {
        result = JSON.parse(sample);
        setJsonError(null);
      } catch (e) {
        setJsonError("Invalid JSON. Please fix the errors before saving.");
        if (consoleError) {
          console.error("Invalid JSON", e);
        }
      }
      return result;
    },
    []
  );

  useEffect(() => {
    // Update the ref whenever value changes
    sampleRef.current = value?.sample;
    const newSample =
      JSON.stringify(structuredClone(value?.sample), null, 2) ?? "{}";
    setTempSample(newSample); // Update the tempSample with the new value
    tryParseJson(newSample); // Ensure the sample is parsed when the value changes
  }, [value?.sample, tryParseJson]);

  useEffect(() => {
    if (errorInSchemaGen) {
      setTab(SchemaQuestionTabs.SAMPLE);
    }
  }, [errorInSchemaGen]);

  const handleSaveData = useCallback(
    (sample: unknown, schema?: Question) => {
      const valueToUpdate = {
        sample: sample ?? {},
        schema: schema ?? {}
      };
      setTempSample(JSON.stringify(valueToUpdate.sample, null, 2));
      onBlurProp?.(valueToUpdate, id);
    },
    [onBlurProp, id]
  );

  const onChange = useCallback(
    (sample?: string) => {
      setTempSample(sample ?? "");

      if (sample === undefined || sample === "" || sample === null) {
        // this should be after the setTempSample so that UI shows updated value correctly
        sample = "{}";
      }

      tryParseJson(sample);
    },
    [setTempSample, tryParseJson]
  );

  const onBlur = useCallback(
    (valueToUpdate?: string) => {
      // the reason I use a useRef here is because
      // I'm unable to access the latest value of `value` otherwise
      const prev = sampleRef.current ?? {};
      if (
        valueToUpdate === undefined ||
        valueToUpdate === "" ||
        valueToUpdate === null
      ) {
        // user cleared the sample
        setTempSample("{}"); // reset the sample to empty object
        valueToUpdate = "{}";
      }

      const currentSampleAsJson = tryParseJson(valueToUpdate, true);
      if (currentSampleAsJson == null || isEqual(prev, currentSampleAsJson)) {
        // if the value cannot be parsed
        // or if the value is the same as the previous one,
        // then we do not want to update the state
        return;
      }
      try {
        const configuration = generateSchemaFromSampleData({
          sampleData: currentSampleAsJson,
          options: { useTableForJsonLists: false },
          identifier: customNanoId(),
          d
        });
        setErrorInSchemaGen(false);
        setJsonError(null);
        handleSaveData(currentSampleAsJson, configuration);
      } catch (error) {
        console.error("Error parsing sample JSON:", error);
        setErrorInSchemaGen(true);
        // Do not call handleSaveData, just keep tempSample in memory
      }
    },
    [tryParseJson, handleSaveData, d]
  );

  return (
    <Stack gap="025" width="100">
      <Label
        label={commonProps.label ?? ""}
        description={commonProps.description}
        required={commonProps.required}
      />
      <Box width="100" overflow="auto" gap="100">
        {!errorInSchemaGen && (
          <TabGroup
            options={[
              {
                value: SchemaQuestionTabs.SAMPLE,
                label: d(
                  "ui.configuration.forms.question.type.schema.tabHeaders.sample"
                )
              },
              ...(!errorInSchemaGen
                ? [
                    {
                      value: SchemaQuestionTabs.SCHEMA,
                      label: d(
                        "ui.configuration.forms.question.type.schema.tabHeaders.schema"
                      )
                    },
                    {
                      value: SchemaQuestionTabs.PREVIEW,
                      label: d(
                        "ui.configuration.forms.question.type.schema.tabHeaders.preview"
                      )
                    }
                  ]
                : [])
            ]}
            value={tab}
            handleChange={(value: string) => {
              setTab(value as SchemaQuestionTabs);
            }}
          />
        )}
        <Box>
          {tab === SchemaQuestionTabs.SAMPLE && (
            <Stack overflow="auto" gap="050">
              <Text size="s" color="text-tertiary">
                Paste a sample of your most complex schema and we will handle
                the schema generation
              </Text>
              <JsonEditor
                localJSONContent={tempSample}
                onBlur={onBlur}
                onChange={onChange}
                height="200px"
                disabled={false}
              />
              {jsonError && (
                <Alert variant="danger">
                  <Stack gap="025">
                    <Text weight="semi-bold">Errors:</Text>
                    <Text>{jsonError}</Text>
                  </Stack>
                </Alert>
              )}
            </Stack>
          )}
          {tab === SchemaQuestionTabs.SCHEMA && schema && (
            <Box overflow="auto" style={{ minHeight: "200px" }}>
              <ConfigurationQuestionBlock
                style={{ width: "100%" }}
                showReorder
                question={schema}
                mode={ConfigurationFormMode.VIEW}
                path={["test"]}
                onClick={() => {}} // todo: handleSelectQuestion
              />
            </Box>
          )}
          {tab === SchemaQuestionTabs.PREVIEW && schema && (
            <Box overflow="auto">
              <OTAIFormField question={schema} id={id} />
            </Box>
          )}
        </Box>
      </Box>
    </Stack>
  );
};
