import React from "react";

import { Form, Stack } from "@oneteam/onetheme";
import type { Meta, StoryObj } from "@storybook/react";
import { z } from "zod";

import { ZodAnswerSchemaBuilder } from "@helpers/forms/ZodAnswerSchemaBuilder/ZodAnswerSchemaBuilder.ts";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import { Section } from "@src/types/FormConfiguration";
import { Question } from "@src/types/Question";

import { OTAIForm } from "./OTAIForm";
import { OTAIFormField } from "./OTAIFormField";

const meta: Meta<typeof OTAIForm> = {
  component: OTAIForm,
  title: "shared/OTAIForm"
};

export default meta;

type Story = StoryObj<typeof OTAIForm>;

const defaultHandlers: Story["args"] = {
  formWrapperProperties: {
    handleCancel: () => {
      console.log("Cancel");
    },
    handleSubmit: data => {
      console.log("Save", data);
    }
  }
};

const defaultArgs: Story["args"] = {
  ...getMockFormQuestions(),
  ...defaultHandlers
};

const defaultRenderer = (args: Story["args"]) => {
  return <OTAIForm {...args} content={args?.content ?? []} />;
};

const hardcodedSchema = z.object({
  name_Id: z.string().min(2, "Name length should be at least 2"),
  workspaceKey: z
    .string()
    .trim()
    .regex(/^[A-Z0-9]+$/, "Invalid workspace key")
    .min(1)
});

export const MinimalHardcodedForm: Story = {
  render: () => {
    const d = useDictionary();

    return (
      <Form
        d={d}
        handleSubmit={data => console.log("Submitted data:", data)}
        schema={hardcodedSchema}
      >
        <Form.TextField name={"name_Id"} label="Name" required />
        <Form.TextField name={"workspaceKey"} label="Workspace Key" />
      </Form>
    );
  }
};

export const MinimalHardcodedOtaiFormFieldsForm: Story = {
  render: () => {
    const d = useDictionary();

    return (
      <Form
        d={d}
        handleSubmit={data => console.log("Submitted data:", data)}
        schema={hardcodedSchema}
      >
        <OTAIFormField question={nameKeyQuestions()[0]} />
        <OTAIFormField question={nameKeyQuestions()[1]} />
      </Form>
    );
  }
};

export const MinimalHardcodedOtaiForm: Story = {
  render: () => {
    return (
      <OTAIForm
        content={nameKeyQuestions()}
        formWrapperProperties={{
          schema: hardcodedSchema,
          handleSubmit: data => console.log("Submitted data:", data)
        }}
      />
    );
  }
};

export const Questions: Story = {
  args: defaultArgs,
  render: defaultRenderer
};

export const QuestionsInSections: Story = {
  args: { ...defaultArgs, ...getMockFormQuestionsWithSection() },
  render: defaultRenderer
};

export const QuestionsInSubSections: Story = {
  args: { ...defaultArgs, ...getMockFormQuestionsWithSubSections() },
  render: defaultRenderer
};

export const QuestionsInNLevelSections: Story = {
  args: { ...defaultArgs, ...getMockFormQuestionsWithNLevelSections() },
  render: defaultRenderer
};

export const WithPositioning: Story = {
  args: {
    ...getMockFormDataWithPositioning()
  },
  render: defaultRenderer
};

const standaloneFieldsRender = () => {
  const questions: Question[] = [
    {
      description: "",
      id: "name",
      identifier: "name",
      properties: {
        allowReuseAcrossForms: false,
        defaultValue: "",
        placeholder: "",
        required: true
      },
      text: "Name",
      type: "text"
    },
    {
      tooltip: "A unique key to identify the workspace",
      id: "workspaceKey",
      identifier: "workspaceKey",
      properties: {
        allowReuseAcrossForms: false,
        defaultValue: "",
        placeholder: "",
        required: true,
        regex: /^[A-Z0-9]+$/
      },
      text: "Workspace Key",
      type: "text"
    }
  ];
  const d = (value => value) as Dictionary;
  return (
    <Form
      // We do not use OTAIForm here to show that OTAIFormField can be used standalone
      d={d}
      handleSubmit={data => console.log("Submit", data)}
      schema={new ZodAnswerSchemaBuilder(questions, d).generatedSchema}
    >
      <Stack gap="100" contentsWidth="100">
        {questions.map(question => (
          <OTAIFormField key={question.id} question={question} />
        ))}
      </Stack>
    </Form>
  );
};

export const StandaloneFieldsRender: Story = {
  args: {
    // ...getMockFormDataWithPositioning()
  },
  render: standaloneFieldsRender
};

function nameKeyQuestions() {
  const nameQuestion: Question = {
    description: "",
    id: "name_Id",
    identifier: "name_Identifier",
    properties: {
      allowReuseAcrossForms: false,
      defaultValue: "",
      placeholder: "",
      required: true,
      minLength: 2,
      maxLength: 100
    },
    text: "Name",
    type: "text"
  };
  const keyQuestion: Question = {
    tooltip: "A unique key to identify the workspace",
    id: "workspaceKey_Id",
    identifier: "workspaceKey_Identifier",
    properties: {
      allowReuseAcrossForms: false,
      defaultValue: "",
      placeholder: "",
      required: true,
      regex: /^[A-Z0-9]+$/,
      minLength: 2,
      maxLength: 20
    },
    text: "Workspace Key",
    type: "text"
  };
  return [nameQuestion, keyQuestion];
}

function getMockFormQuestions(): Story["args"] {
  const mockForm: Section["content"] = nameKeyQuestions();
  return { content: mockForm };
}

function getMockFormQuestionsWithSection(): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: nameKeyQuestions(),
      id: "q_m0UwMYlg",
      name: "Create Workspace"
    }
  ];
  return { content: mockForm };
}

function mockQuestions() {
  const question1: Question = {
    description: "",
    id: "question1",
    identifier: "question1",
    properties: {
      allowReuseAcrossForms: false,
      defaultValue: "",
      placeholder: "",
      required: true,
      minLength: 2,
      maxLength: 100
    },
    text: "Question 1",
    type: "text"
  };
  const question2: Question = {
    id: "question2",
    identifier: "question2",
    properties: {
      allowReuseAcrossForms: false,
      defaultValue: "",
      placeholder: "",
      required: false,
      regex: /^[A-Z0-9]+$/
    },
    text: "Question 2",
    type: "text"
  };
  return [question1, question2];
}

function getMockFormQuestionsWithSubSections() {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          id: "1",
          name: "General",
          content: mockQuestions()
        }
      ],
      id: "q_m0UwMYlg",
      name: "Create Workspace"
    }
  ];
  return { content: mockForm };
}

function getMockFormQuestionsWithNLevelSections(): Story["args"] {
  const mockForm: Section["content"] = [
    {
      content: [
        {
          id: "1",
          name: "General",
          content: [
            {
              id: "2",
              name: "Sub General",
              content: [
                {
                  id: "3",
                  name: "Sub Sub General",
                  content: mockQuestions()
                }
              ]
            }
          ]
        },
        {
          id: "4",
          name: "Another General",
          content: [
            {
              description: "",
              id: "question3",
              identifier: "question3",
              properties: {
                allowReuseAcrossForms: false,
                defaultValue: "",
                placeholder: "",
                required: true
              },
              text: "Question 3",
              type: "text"
            }
          ]
        }
      ],
      id: "q_m0UwMYlg",
      name: "Create Workspace"
    }
  ];
  return { content: mockForm };
}

function getMockFormDataWithPositioning(): Story["args"] {
  const mockForm: Section[] = [
    {
      content: [
        {
          description: "",
          id: "text",
          identifier: "text",
          properties: {
            defaultValue: "",
            placeholder: "",
            required: true,
            minLength: 2,
            maxLength: 100
          },
          text: "Question",
          type: "text"
        },
        {
          description: "",
          id: "description",
          identifier: "description",
          properties: {
            defaultValue: "",
            placeholder: "",
            required: false,
            isTextArea: true
          },
          text: "Description",
          type: "text"
        },
        {
          description: "",
          id: "properties.required",
          identifier: "properties.required",
          properties: {
            defaultValue: false,
            required: false
          },
          text: "Required",
          type: "boolean"
        },
        {
          description: "",
          id: "properties.minLength",
          identifier: "properties.minLength",
          properties: {
            defaultValue: false,
            required: false
          },
          text: "Min Length",
          type: "number"
        },
        {
          description: "",
          id: "properties.maxLength",
          identifier: "properties.maxLength",
          properties: {
            defaultValue: false,
            required: false
          },
          text: "Max Length",
          type: "number"
        },
        {
          description: "Whether multiple lines are visible in the answer",
          id: "properties.isTextArea",
          identifier: "properties.isTextArea",
          properties: {
            defaultValue: false,
            required: false
          },
          text: "Multiple lines",
          type: "boolean"
        },
        {
          description: "",
          id: "properties.placeholder",
          identifier: "properties.placeholder",
          properties: {
            defaultValue: "",
            required: false
          },
          text: "Placeholder",
          type: "text"
        },
        {
          description: "",
          id: "properties.defaultValue",
          identifier: "properties.defaultValue",
          properties: {
            defaultValue: "",
            required: false
          },
          text: "Default Value",
          type: "text"
        },
        {
          description: "",
          id: "properties.identifier",
          identifier: "properties.identifier",
          properties: {
            defaultValue: "",
            required: true
          },
          text: "Identifier",
          type: "text"
        }
      ],
      id: "q_m0UwMYlg",
      name: "Text Question Configuration"
    }
  ];
  return {
    content: mockForm,
    positioning: [
      "text",
      "description",
      "properties.required",
      ["properties.minLength", "properties.maxLength"],
      ["properties.placeholder", "properties.defaultValue"]
    ]
  };
}
