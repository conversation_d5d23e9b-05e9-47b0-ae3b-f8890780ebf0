import React, { useEffect, useMemo } from "react";

import {
  FermionProps,
  Form,
  NumberType,
  Text,
  TextFieldProps
} from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";

import { getPercentageValue } from "@helpers/forms/numberHelpers.ts";
import { formatDecimalPlaces } from "@helpers/numericalHelper";

import { OTAIFormFieldSelect } from "@components/shared/OTAIForm/OTAIFormField/OTAIFormFieldSelect.tsx";

import { EXTRA_DECIMAL_PLACES_FOR_PERCENTAGE } from "@src/constants/numberConstants.ts";
import { useDictionary } from "@src/hooks/useDictionary";
import { Question, QuestionTypes } from "@src/types/Question";
import {
  BooleanQuestionProperties,
  DateQuestionProperties,
  FilesQuestionProperties,
  JSONQuestionProperties,
  ListQuestionProperties,
  NumberQuestionProperties,
  SelectQuestionProperties,
  TableQuestionProperties,
  TextQuestionProperties
} from "@src/types/QuestionProperties";

import { OTAIFormFieldFiles } from "./OTAIFormField/OTAIFormFieldFiles";
import { OTAIFormFieldJSON } from "./OTAIFormField/OTAIFormFieldJSON";
import { OTAIFormFieldListQuestion } from "./OTAIFormField/OTAIFormFieldListQuestion";
import { OTAIFormFieldSchemaQuestion } from "./OTAIFormField/OTAIFormFieldSchemaQuestion";
import { OTAIFormFieldTable } from "./OTAIFormField/OTAIFormFieldTable";
import { OTAIFormFieldCommonProps } from "./OTAIFormType";

const formFieldDefaultByType: {
  [key: string]: unknown;
} = {
  [QuestionTypes.TEXT]: "",
  [QuestionTypes.NUMBER]: "",
  [QuestionTypes.SELECT]: "",
  [QuestionTypes.MULTISELECT]: [],
  [QuestionTypes.DATE]: "",
  [QuestionTypes.BOOLEAN]: undefined,
  [QuestionTypes.JSON]: {},
  [QuestionTypes.FILES]: [],
  [QuestionTypes.LIST]: {},
  [QuestionTypes.SCHEMA]: {}
  // Has no default
  // [QuestionTypes.TABLE]: [],
};

export const OTAIFormField = ({
  question,
  id = question.id ?? question.identifier,
  // Hide the label + description
  isLabelExternal = false,
  autoFocus = false,
  answer,
  onChange,
  onFocus,
  onlyTriggerChangeWhenBlur,
  disabled,
  error,
  width = "100",
  className,
  style = {}
}: {
  question: Question;
  // Unique ID for the question in the form
  id?: string;
  autoFocus?: boolean;
  // Hide the label + description
  isLabelExternal?: boolean;
  answer?: unknown;
  onChange?: (value: unknown) => void;
  onFocus?: React.FocusEventHandler;
  disabled?: boolean;
  error?: string;
  onlyTriggerChangeWhenBlur?: boolean;
  width?: FermionProps["width"];
  className?: string;
  style?: React.CSSProperties;
}) => {
  const d = useDictionary();
  const { watch, resetField, setValue } = useFormContext();
  const questionAnswer = answer ?? watch(question.id ?? question.identifier);

  useEffect(() => {
    if (
      question.type === QuestionTypes.TABLE ||
      question.type === QuestionTypes.FILES
    ) {
      return;
    }
    setValue(
      id,
      (question as Question<TextQuestionProperties>)?.properties
        ?.defaultValue ?? formFieldDefaultByType[question.type]
    );
    // Only set value when the default value changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    id,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    (question as Question<TextQuestionProperties>)?.properties?.defaultValue,
    resetField
  ]);

  const commonProps: OTAIFormFieldCommonProps = useMemo(() => {
    const defaults = {
      autoFocus,
      disabled: disabled || question.properties?.disabled,
      hidden: question.properties?.hidden
    };
    if (isLabelExternal) {
      return defaults;
    }
    return {
      ...defaults,
      label: question.text,
      description: question.description,
      required: question.properties?.required,
      width,
      ...(error ? { error } : {}),
      className,
      style
    };
  }, [
    isLabelExternal,
    question.text,
    question.description,
    question.properties?.required,
    question.properties?.hidden,
    question.properties?.disabled,
    disabled,
    autoFocus,
    width,
    error,
    className,
    style
  ]);

  const questionField = useMemo(() => {
    if (commonProps.hidden) {
      return null;
    }
    // TODO: build zod schema for questionList
    switch (question.type) {
      case QuestionTypes.TEXT: {
        const textQuestion = question as Question<TextQuestionProperties>;
        const props = {
          ...commonProps,
          name: id,
          minLength: textQuestion.properties?.minLength,
          maxLength: textQuestion.properties?.maxLength,
          placeholder: textQuestion.properties?.placeholder ?? "",
          regex: textQuestion.properties?.regex,
          value: questionAnswer ?? textQuestion?.properties?.defaultValue ?? "",
          type: (textQuestion.properties?.isSecret
            ? "password"
            : "text") as TextFieldProps["type"],
          onChange,
          onFocus,
          onlyTriggerChangeWhenBlur: onlyTriggerChangeWhenBlur ?? true
        };
        if (props.disabled) {
          return (
            <Text style={{ userSelect: "text" }}>{props.value ?? ""}</Text>
          );
        }
        if (
          textQuestion.properties?.isTextArea &&
          // Cannot have a secret text area
          !textQuestion.properties?.isSecret
        ) {
          return <Form.TextAreaField {...props} />;
        }
        return <Form.TextField {...props} />;
      }
      case QuestionTypes.NUMBER: {
        const numberQuestion = question as Question<NumberQuestionProperties>;

        const allowedDecimalPlaces = numberQuestion.properties?.decimalPlaces;
        const decimalPlacesForUI =
          numberQuestion.properties?.type === NumberType.PERCENTAGE &&
          allowedDecimalPlaces
            ? allowedDecimalPlaces - EXTRA_DECIMAL_PLACES_FOR_PERCENTAGE
            : allowedDecimalPlaces;

        const format = formatDecimalPlaces(allowedDecimalPlaces);

        const min = getPercentageValue(
          numberQuestion.properties?.type,
          numberQuestion.properties?.min,
          numberQuestion.properties?.decimalPlaces
        );
        const max = getPercentageValue(
          numberQuestion.properties?.type,
          numberQuestion.properties?.max,
          numberQuestion.properties?.decimalPlaces
        );

        const props = {
          ...commonProps,
          name: id,
          min: min,
          max: max,
          placeholder: numberQuestion.properties?.placeholder ?? "",
          type: numberQuestion.properties?.type,
          value:
            questionAnswer ?? numberQuestion?.properties?.defaultValue ?? "",
          onlyTriggerChangeWhenBlur: onlyTriggerChangeWhenBlur ?? true,
          onChange,
          onFocus
        };
        return (
          <Form.NumberField
            {...props}
            format={format}
            step={decimalPlacesForUI ? Math.pow(10, -decimalPlacesForUI) : 1}
            allowedDecimalPlaces={allowedDecimalPlaces}
            extraDecimalPlacesForPercentage={
              EXTRA_DECIMAL_PLACES_FOR_PERCENTAGE
            }
          />
        );
      }
      case QuestionTypes.MULTISELECT:
      case QuestionTypes.SELECT: {
        const selectQuestion = question as Question<SelectQuestionProperties>;
        return (
          <OTAIFormFieldSelect
            id={id}
            question={selectQuestion}
            answer={questionAnswer}
            onChange={onChange}
            onFocus={onFocus}
            onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
            isLabelExternal={isLabelExternal}
            {...(error ? { error } : {})}
            width={width}
            className={className}
            style={style}
            clearIfOptionMissing={
              selectQuestion?.properties?.clearIfOptionMissing
            }
          />
        );
      }
      case QuestionTypes.DATE: {
        const dateQuestion = question as Question<DateQuestionProperties>;
        const props = {
          ...commonProps,
          name: id,
          min: dateQuestion.properties?.min,
          max: dateQuestion.properties?.max,
          value:
            questionAnswer ??
            dateQuestion?.properties?.defaultValue ??
            undefined,
          onChange,
          onFocus,
          onlyTriggerChangeWhenBlur
        };
        if (dateQuestion.properties?.isDateRange) {
          return <Form.DateRangePicker {...props} width="100" />;
        }
        return <Form.DatePicker {...props} width="100" />;
      }
      case QuestionTypes.BOOLEAN: {
        const booleanQuestion = question as Question<BooleanQuestionProperties>;
        const props = {
          ...commonProps,
          name: id,
          options: [
            {
              label:
                booleanQuestion.properties?.trueText ??
                d("ui.configuration.forms.question.boolean.trueDefault"),
              value: true
            },
            {
              label:
                booleanQuestion.properties?.falseText ??
                d("ui.configuration.forms.question.boolean.falseDefault"),
              value: false
            }
          ],
          allowClear: !commonProps.required,
          value:
            questionAnswer ??
            booleanQuestion?.properties?.defaultValue ??
            undefined,
          onChange
        };
        return (
          <Form.RadioGroup
            {...props}
            onChange={value => {
              if (typeof value === "number" || typeof value === "string") {
                return;
              }
              setValue(id, value);
              onChange?.(value);
            }}
          />
        );
      }
      case QuestionTypes.FILES: {
        const filesQuestion = question as Question<FilesQuestionProperties>;

        return (
          <OTAIFormFieldFiles
            id={id}
            value={questionAnswer}
            question={filesQuestion}
            commonProps={commonProps}
            onChange={onChange}
          />
        );
      }
      case QuestionTypes.TABLE: {
        const tableQuestion = question as Question<TableQuestionProperties>;
        return (
          <OTAIFormFieldTable
            id={id}
            question={tableQuestion}
            commonProps={commonProps}
            // onChange={onChange}
          />
        );
      }
      case QuestionTypes.JSON: {
        const jsonQuestion = question as Question<JSONQuestionProperties>;
        return (
          <OTAIFormFieldJSON
            id={id}
            question={jsonQuestion}
            commonProps={commonProps}
            // onChange={onChange}
          />
        );
      }
      case QuestionTypes.LIST: {
        const listQuestion = question as Question<ListQuestionProperties>;
        return (
          <OTAIFormFieldListQuestion
            id={id}
            question={listQuestion}
            commonProps={commonProps}
            value={questionAnswer}
          />
        );
      }
      case QuestionTypes.SCHEMA: {
        return (
          <OTAIFormFieldSchemaQuestion
            id={id}
            commonProps={commonProps}
            onBlur={onChange}
            value={questionAnswer}
          />
        );
      }
      default: {
        return (
          <div>
            {question.type} is not supported yet. Please contact the support
            team.
          </div>
        );
      }
    }
  }, [
    commonProps,
    question,
    id,
    questionAnswer,
    onChange,
    onFocus,
    onlyTriggerChangeWhenBlur,
    isLabelExternal,
    error,
    width,
    className,
    style,
    d,
    setValue
  ]);

  return questionField;
};
