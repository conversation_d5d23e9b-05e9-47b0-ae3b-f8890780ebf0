import React, { useMemo } from "react";

import {
  Box,
  ColorText,
  Icon,
  IconSize,
  Inline,
  Text,
  TextSize,
  Tooltip
} from "@oneteam/onetheme";

import { getQuestionTypeDetail } from "@components/flows/VariableField/variableFieldHelpers";

import { useQuestionTypeDisplay } from "@src/hooks/useQuestionTypeDisplay";
import {
  ExtendedQuestionTypes,
  QuestionTypes,
  questionTypeIcon
} from "@src/types/Question.ts";

export const QuestionType = ({
  type,
  size = "small",
  color = undefined,
  iconOnly = false,
  withTooltip = !!iconOnly,
  contentStyle,
  className
}: {
  type: string;
  size?: "regular" | "small";
  color?:
    | `${ColorText.PRIMARY}`
    | `${ColorText.SECONDARY}`
    | `${ColorText.TERTIARY}`;
  iconOnly?: boolean;
  withTooltip?: boolean;
  contentStyle?: React.CSSProperties;
  className?: string;
}) => {
  const { displayQuestionType } = useQuestionTypeDisplay();

  const questionTypeDisplay = useMemo(
    () => displayQuestionType(type),
    [displayQuestionType, type]
  );

  const questionType = useMemo(() => getQuestionTypeDetail(type).type, [type]);

  const icon = useMemo(() => {
    const iconProps =
      questionTypeIcon[
        questionType as `${QuestionTypes | ExtendedQuestionTypes}`
      ];
    if (!iconProps) {
      return <></>;
    }
    return (
      <Icon
        {...(iconProps ?? questionTypeIcon.unknown)}
        size={size === "regular" ? IconSize.REGULAR : IconSize.SMALL}
        color={color}
      />
    );
  }, [size, questionType, color]);

  const content = useMemo(
    () => (
      <>
        {icon}
        {!iconOnly && (
          <Text
            size={size === "regular" ? TextSize.M : TextSize.XS}
            color={color}
          >
            {questionTypeDisplay.label}
          </Text>
        )}
      </>
    ),
    [color, icon, iconOnly, questionTypeDisplay.label, size]
  );

  return (
    <Inline
      key={`question-${questionType}`}
      gap="025"
      alignment="left"
      className={className}
    >
      {withTooltip ? (
        <Tooltip content={questionTypeDisplay.label}>
          <Box style={contentStyle}>{content}</Box>
        </Tooltip>
      ) : (
        content
      )}
    </Inline>
  );
};
