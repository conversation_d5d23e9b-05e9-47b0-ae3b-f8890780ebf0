import { Prop } from "@automerge/automerge-repo";
import { SelectOptionType } from "@oneteam/onetheme";

import { getByPath } from "@helpers/configurationFormHelper.ts";

import { Question } from "@src/types/Question.ts";
import {
  SelectQuestionMultipleValue,
  SelectQuestionOption,
  SelectQuestionProperties,
  SelectQuestionSingleValue
} from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

export const selectOnChange = ({
  field,
  path,
  docChange
}: {
  field: keyof SelectQuestionProperties;
  path: Prop[];
  docChange: (cb: (d: WorkspaceDocument) => void) => void;
}) => {
  return (value: SelectQuestionSingleValue | SelectQuestionMultipleValue) => {
    docChange((d: WorkspaceDocument): void => {
      const q = getByPath<Question<SelectQuestionProperties>>(d, path);
      if (!q?.properties) {
        console.error("Question not found", path);
        return;
      }
      if (value === undefined || value === "") {
        delete q.properties[field];
        return;
      }

      if (field === "defaultValue") {
        q.properties[field] = value;
      }
    });
  };
};

// maps the question select options to the format required by the component
export const optionsAdapterForComponent = (
  options?: SelectQuestionOption[]
): SelectOptionType[] => {
  if (!options) {
    return [];
  }
  return options?.map(option => {
    const label = !option.label?.length
      ? String(option.value)
      : (option.label ?? String(option.value));
    return {
      label: label,
      description: option.description,
      value: option.value
    } as SelectOptionType;
  });
};
