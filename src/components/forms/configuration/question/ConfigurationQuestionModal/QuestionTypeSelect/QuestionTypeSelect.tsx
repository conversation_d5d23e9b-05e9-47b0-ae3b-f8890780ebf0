import React, { useMemo } from "react";

import { Prop } from "@automerge/automerge-repo";
import { Form, SelectOptions } from "@oneteam/onetheme";

import { getQuestionTypeOptions } from "@helpers/configurationFormHelper";

import { QuestionType } from "@components/forms/QuestionType/QuestionType.tsx";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { QuestionTypes } from "@src/types/Question.ts";

export const QuestionTypeSelect = ({
  type,
  path,
  handleChange,
  disabled,
  options = getQuestionTypeOptions() as QuestionTypes[]
}: {
  type: `${QuestionTypes}`;
  path: Prop[];
  handleChange: (type: QuestionTypes) => void;
  disabled?: boolean;
  options?: QuestionTypes[];
}) => {
  const d = useDictionary();

  const questionTypeOptions: SelectOptions = useMemo(
    () =>
      options.map(type => {
        return {
          value: type,
          label: d(`ui.configuration.forms.question.type.${type}.label`),
          description: d(
            `ui.configuration.forms.question.type.${type}.description`
          )
        };
      }),
    [d, options]
  );

  const displayType = useMemo(() => {
    if (type === QuestionTypes.MULTISELECT) {
      return QuestionTypes.SELECT;
    }
    return type;
  }, [type]);

  return (
    <Form.PillSelect
      name={`${path.join(".")}.type`}
      value={displayType}
      options={questionTypeOptions}
      required
      leftElement={<QuestionType size="regular" type={type} iconOnly />}
      disabled={disabled}
      handleChange={value => {
        handleChange(value as QuestionTypes);
      }}
      renderOptionLeftElement={({ option }) => (
        <QuestionType
          type={option?.value as QuestionTypes}
          iconOnly
          size="regular"
        />
      )}
      fitOptionText
    />
  );
};
