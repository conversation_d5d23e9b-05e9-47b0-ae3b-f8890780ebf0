import React, { useMemo, useState } from "react";

import {
  Box,
  ColorText,
  FontWeight,
  Heading,
  HeadingSize,
  Icon,
  IconButton,
  IconFillStyle,
  Inline,
  Label,
  Renamable,
  Stack,
  WhiteboardBlock,
  getClassNames
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { DocumentErrorHelper } from "@helpers/DocumentErrorHelper.ts";
import { isMultiLevelQuestion } from "@helpers/configurationFormHelper.ts";
import { eventWithoutPropagationDefault } from "@helpers/eventWithoutPropagation.ts";

import { QuestionType } from "@components/forms/QuestionType/QuestionType.tsx";
import { ReorderIcon } from "@components/shared/ReorderIcon.tsx";
import { WhiteboardStatusCircle } from "@components/shared/WhiteboardStatusCircle.tsx";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { FormPath } from "@src/types/Form.ts";
import { ConfigurationFormMode } from "@src/types/FormConfiguration.ts";
import { Question } from "@src/types/Question.ts";

import { MultiLevelProperties } from "../ConfigurationQuestionModal/QuestionConfigurationFields/MultiLevel/MultiLevelProperties.tsx";
import "./ConfigurationQuestionBlock.scss";

export const ConfigurationQuestionBlock = ({
  question,
  onClick,
  path,
  selectedQuestionPath,
  isHighlighted,
  isSelected,
  isDisabled,
  mode,
  handleEdit,
  handleDuplicate,
  handleDelete,
  handleRename,
  showReorder,
  className = "",
  style = {},
  onlyContainOneQuestion,
  hideMultiLevel = false,
  isRenaming = false
}: {
  question: Question;
  onClick?: (path: FormPath) => void;
  path: FormPath;
  selectedQuestionPath?: string;
  isHighlighted?: boolean;
  isSelected?: boolean;
  isDisabled?: boolean;
  mode: `${ConfigurationFormMode}`;
  handleEdit?: () => void;
  handleDuplicate?: () => void;
  handleDelete?: () => void;
  handleRename?: (name: string) => void;
  showReorder?: boolean;
  className?: string;
  style?: React.CSSProperties;
  onlyContainOneQuestion?: boolean;
  hideMultiLevel?: boolean;
  isRenaming?: boolean;
}) => {
  const d = useDictionary();

  const { docErrorHelper } = useOutletContext<{
    docErrorHelper: DocumentErrorHelper;
  }>();

  const isMultiLevel = useMemo(
    () => isMultiLevelQuestion(question.type),
    [question]
  );

  const [isExpanded, setIsExpanded] = useState(true);

  const isQuestionBlockSelected = useMemo(() => {
    if (isSelected) {
      return true;
    }
    const currentPath = path.join(".");
    if (!isExpanded) {
      return selectedQuestionPath?.includes(currentPath);
    }
    return selectedQuestionPath === currentPath;
  }, [isSelected, path, isExpanded, selectedQuestionPath]);

  if (!question) {
    return <></>;
  }

  return (
    <WhiteboardBlock
      key={question.id}
      onClick={() => onClick?.(path)}
      isSelected={isQuestionBlockSelected}
      isHighlighted={isHighlighted}
      className={getClassNames(["question-configuration-block", className])}
      style={style}
      isDisabled={isDisabled}
    >
      {docErrorHelper.getErrorCountByPrefix(`$.${path.join(".")}`, true) >
        0 && (
        <WhiteboardStatusCircle
          tooltipContent={d("errors.configurationForm.question.tooltip")}
        />
      )}
      <Inline
        className="question-configuration-block__header"
        width="100"
        style={{
          padding: "var(--spacing-100) var(--spacing-150)"
        }}
        alignment="left"
        spaceBetween
      >
        <Stack gap="025">
          <QuestionType type={question?.type} color={ColorText.TERTIARY} />
          {!onlyContainOneQuestion && (
            <Inline gap="050" alignment="left">
              {question.properties?.hidden && (
                <Icon name="visibility_off" size="s" color="text-secondary" />
              )}
              {question.properties?.disabled && (
                <Icon name="edit_off" size="s" color="text-secondary" />
              )}
              <Heading size={HeadingSize.XXS} weight={FontWeight.MEDIUM}>
                {handleRename && mode === "edit" ? (
                  <Renamable
                    value={question?.text}
                    onChange={handleRename}
                    controlFocus={isRenaming}
                  />
                ) : (
                  question.text
                )}
              </Heading>
              <Label
                className="question-configuration-block__label"
                label={" "}
                required={question?.properties?.required}
              />
            </Inline>
          )}
        </Stack>
        {mode === ConfigurationFormMode.EDIT && !onlyContainOneQuestion && (
          <Inline
            className="question-configuration-block__actions"
            height="fit"
            gap="300"
          >
            <Inline gap="150">
              {handleEdit && mode === "edit" && (
                <IconButton
                  name="edit"
                  fillStyle={IconFillStyle.FILLED}
                  onClick={handleEdit}
                  skipFocus
                  label={d("ui.common.edit")}
                />
              )}
              {handleDuplicate && mode === "edit" && (
                <IconButton
                  name="content_copy"
                  fillStyle={IconFillStyle.FILLED}
                  onClick={eventWithoutPropagationDefault(handleDuplicate)}
                  skipFocus
                  label={d("ui.common.duplicate")}
                />
              )}
              {handleDelete && mode === "edit" && (
                <IconButton
                  name="delete"
                  fillStyle={IconFillStyle.FILLED}
                  onClick={eventWithoutPropagationDefault(handleDelete)}
                  skipFocus
                  label={d("ui.common.delete")}
                />
              )}
            </Inline>
            {showReorder && <ReorderIcon />}
          </Inline>
        )}
      </Inline>
      {isMultiLevel && !hideMultiLevel && (
        <Box
          style={{
            padding: "0 var(--spacing-150) var(--spacing-100)",
            marginTop: "calc(var(--spacing-050) * -1)"
          }}
        >
          <MultiLevelProperties
            showReorder={showReorder}
            question={question}
            path={path}
            d={d}
            isExpanded={isExpanded}
            onChangeExpanded={setIsExpanded}
            mode={mode}
          />
        </Box>
      )}
    </WhiteboardBlock>
  );
};

ConfigurationQuestionBlock.displayName = "ConfigurationQuestionBlock";
