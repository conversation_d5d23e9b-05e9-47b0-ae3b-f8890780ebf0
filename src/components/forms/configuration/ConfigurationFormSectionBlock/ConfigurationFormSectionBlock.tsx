import React, {
  PropsWithChildren,
  useCallback,
  useMemo,
  useState
} from "react";

import { Prop } from "@automerge/automerge-repo";
import {
  Accordion,
  Box,
  Card,
  ColorText,
  CustomAccordionTrigger,
  Divider,
  DividerSize,
  FontWeight,
  Form,
  Heading,
  HeadingSize,
  IconButton,
  Inline,
  OpenCloseIcon,
  Stack
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { DocumentErrorHelper } from "@helpers/DocumentErrorHelper";

import { ConfigurationFormAddLine } from "@components/forms/configuration/ConfigurationFormAddLine/ConfigurationFormAddLine.tsx";
import { ReorderIcon } from "@components/shared/ReorderIcon";
import { WhiteboardStatusCircle } from "@components/shared/WhiteboardStatusCircle";

import { commonIcons } from "@src/constants/iconConstants";
import { useConfigurationFormContent } from "@src/hooks/formConfiguration/useConfigurationFormContent";
import { useDictionary } from "@src/hooks/useDictionary";
import {
  ConfigurationFormMode,
  Section
} from "@src/types/FormConfiguration.ts";

import "./ConfigurationFormSectionBlock.scss";

export const ConfigurationFormSectionBlock = ({
  section,
  path,
  mode,
  handleDuplicate,
  handleDelete,
  showReorder,
  children
}: PropsWithChildren<{
  section: Section;
  path: Prop[];
  mode: ConfigurationFormMode;
  handleDuplicate?: () => void;
  handleDelete?: () => void;
  showReorder?: boolean;
}>) => {
  const [isOpen, setIsOpen] = useState(true);
  const { updateTextField, addQuestion, addSection } =
    useConfigurationFormContent({
      content: section.content,
      path,
      level: section.level
    });

  const d = useDictionary();

  const { docErrorHelper } = useOutletContext<{
    docErrorHelper: DocumentErrorHelper;
  }>();

  const sectionActions = useMemo(() => {
    return (
      <Inline
        className="configuration-form-section-block__actions"
        gap="200"
        width="fit"
        height="fit"
        style={{ background: "white", padding: "0 2px" }}
      >
        <Inline gap="100">
          {handleDuplicate && (
            <IconButton
              {...commonIcons.duplicate}
              onClick={handleDuplicate}
              label={d("ui.common.duplicate")}
            />
          )}
          {handleDelete && (
            <IconButton {...commonIcons.delete} onClick={handleDelete} />
          )}
        </Inline>
        {showReorder && <ReorderIcon />}
      </Inline>
    );
  }, [handleDuplicate, d, handleDelete, showReorder]);

  const accordionTrigger: CustomAccordionTrigger = useCallback(
    ({ isOpen, onClick }) => (
      <Inline
        className="configuration-form-section-block__heading"
        width="100"
        gap="100"
        alignment="left"
        spaceBetween
      >
        <Inline gap="050" width="100" onClick={onClick} alignment="left">
          <OpenCloseIcon isOpen={isOpen} />
          <Heading
            size={HeadingSize.S}
            weight={FontWeight.REGULAR}
            style={{
              maxWidth: "100%"
            }}
          >
            <Form.Renamable
              name={path.join(".")}
              value={section.name}
              onChange={updateTextField}
            />
          </Heading>
          {/* TODO: show count, use dictionary and use plural when required */}
          {/* <Pill label={`${section.content.length} sections`} />
          <Pill
            label={`${_.sum(section.content.map((s: Section) => s.content.length))} questions`}
          /> */}
        </Inline>
        {sectionActions}
      </Inline>
    ),
    [path, section.name, sectionActions, updateTextField]
  );

  if (section.level === 1) {
    return (
      <Box className="configuration-form-section-block-wrapper">
        {!isOpen &&
          docErrorHelper.getErrorCountByPrefix(`$.${path.join(".")}`, false) >
            0 && (
            <WhiteboardStatusCircle
              tooltipContent={d("errors.configurationForm.question.tooltip")}
            />
          )}
        <Card
          className="configuration-form-section-block"
          key={path.join(".")}
          size="medium"
        >
          <Accordion
            contentOverflow="visible"
            isOpen={isOpen}
            onOpenChange={setIsOpen}
            key={section.level}
            trigger={accordionTrigger}
          >
            <Stack gap="100" style={{ padding: "var(--spacing-100) 0 0 0" }}>
              {children}
            </Stack>
          </Accordion>
        </Card>
      </Box>
    );
  }

  return (
    <Stack
      className="configuration-form-section-block"
      style={{
        paddingLeft: `calc(var(--spacing-100) * ${(section.level ?? 0) - 2})`
      }}
    >
      <Inline alignment="left" width="100" position="relative">
        <Box position="absolute" width="100">
          <Divider size={DividerSize.SMALL} />
        </Box>
        <Inline
          className="configuration-form-section-block__heading"
          width="100"
          gap="050"
          alignment="left"
          spaceBetween
          style={{ zIndex: 1, padding: "0 0 0 8px" }}
        >
          <Inline
            gap="050"
            alignment="left"
            style={{
              background: "var(--color-surface-secondary)",
              padding: "0 3px"
            }}
          >
            <Heading
              size={HeadingSize.XS}
              color={ColorText.SECONDARY}
              weight={FontWeight.REGULAR}
            >
              <Form.Renamable
                name={path.join(".")}
                value={section.name}
                onChange={updateTextField}
              />
            </Heading>
          </Inline>
          {sectionActions}
        </Inline>
      </Inline>
      {children}
      {(section.level ?? 0) > 1 && mode === ConfigurationFormMode.EDIT && (
        <ConfigurationFormAddLine
          key={`add-line-${path.join(".")}`}
          addQuestion={addQuestion(section.content.length)}
          addSection={addSection((path[path.length - 1] as number) + 1)}
        />
      )}
    </Stack>
  );
};

ConfigurationFormSectionBlock.displayName = "ConfigurationFormSectionBlock";
