import React, { SyntheticEvent, useCallback } from "react";

import {
  getClassNames,
  returnStringIfTrue
} from "../../../helpers/componentHelpers.ts";
import { Text } from "../../Text/index.ts";
import { BreadcrumbsItemType } from "../BreadcrumbsTypes.ts";
import "./BreadcrumbsItem.scss";

export const BreadcrumbsItem = ({
  text,
  href,
  onClick,
  isActive,
  leftElement,
  rightElement
}: BreadcrumbsItemType) => {
  const handleOnClick = useCallback(
    (e: SyntheticEvent) => {
      if (isActive) {
        return;
      }
      if (onClick) {
        onClick?.(e);
      } else if (href) {
        window.location.pathname = href;
      }
    },
    [href, isActive, onClick]
  );

  return (
    <button
      className={getClassNames([
        "breadcrumbs-item",
        returnStringIfTrue(isActive, "breadcrumbs-item--active")
      ])}
      onClick={handleOnClick}
      disabled={isActive}
      onMouseDown={e => e.preventDefault()}
      type="button"
    >
      {leftElement}
      <Text className="breadcrumbs-item__text" maxLines={1} truncate>
        {text}
      </Text>
      {rightElement}
    </button>
  );
};
