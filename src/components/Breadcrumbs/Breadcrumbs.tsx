import React, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>actNode,
  useCallback,
  useState
} from "react";

import { Inline } from "../../fermions/index.ts";
import { Text } from "../Text/index.ts";
import "./Breadcrumbs.scss";

export interface BreadcrumbsProps {
  maxItems?: number;
  itemsBeforeCollapse?: number;
  itemsAfterCollapse?: number;
  className?: string;
}

export const Breadcrumbs = ({
  maxItems = 8,
  itemsBeforeCollapse = 1,
  itemsAfterCollapse = 1,
  className = "",
  children
}: React.PropsWithChildren<BreadcrumbsProps>) => {
  const [openAll, setOpenAll] = useState(false);
  const renderItems = useCallback(
    (node?: ReactNode) => {
      const items = Array.isArray(node) ? node : [node];
      let displayedItems = items ?? [];
      if (!openAll && maxItems && displayedItems.length > maxItems) {
        const before = displayedItems.slice(0, itemsBeforeCollapse);
        const after = displayedItems.slice(
          displayedItems.length - itemsAfterCollapse
        );
        displayedItems = [
          ...before,
          <BreadcrumbsCollapsedItem
            key="breadcrumb-item"
            onClick={() => setOpenAll(true)}
          />,
          ...after
        ];
      }

      return displayedItems.map((child: ReactNode, index: number) => (
        <React.Fragment key={index}>
          {index !== 0 && <Text className="breadcrumbs__spacer">/</Text>}
          {child}
        </React.Fragment>
      ));
    },
    [itemsAfterCollapse, itemsBeforeCollapse, maxItems, openAll]
  );

  return (
    <Inline classNames={["breadcrumbs", className]} alignment="left">
      {renderItems(children)}
    </Inline>
  );
};

const BreadcrumbsCollapsedItem = ({
  onClick
}: {
  onClick: MouseEventHandler<HTMLButtonElement>;
}) => {
  return (
    <button
      className="breadcrumbs__collapsed-item"
      onClick={onClick}
      type="button"
    >
      <Text>...</Text>
    </button>
  );
};
