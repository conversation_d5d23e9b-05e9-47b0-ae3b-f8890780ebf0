import React, { useEffect, useRef } from "react";

import useOnClickOutside from "use-onclickoutside";

import {
  getClassNames,
  returnStringIfTrue
} from "../../../helpers/componentHelpers.ts";
import "./ModalDialog.scss";

interface ModalDialogProps {
  isOpen: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  className?: string;
  closeOnClickOutside?: boolean;
}

export const ModalDialog = ({
  isOpen,
  onOpenChange,
  closeOnClickOutside = true,
  className = "",
  children
}: React.PropsWithChildren<ModalDialogProps>) => {
  const dialogRef = useRef<HTMLDialogElement>(null);

  useOnClickOutside(dialogRef, () => {
    if (closeOnClickOutside) {
      onOpenChange?.(false);
    }
  });

  useEffect(() => {
    if (isOpen) {
      dialogRef?.current?.showModal?.();
    } else {
      dialogRef?.current?.close?.();
    }
  }, [isOpen]);

  if (!isOpen) {
    return <></>;
  }
  return (
    <dialog
      ref={dialogRef}
      className={getClassNames([
        "modal-dialog",
        returnStringIfTrue(isOpen, "modal-dialog--visible"),
        className
      ])}
      aria-modal="true"
      role="modal"
      onCancel={() => {
        onOpenChange?.(false);
        dialogRef.current?.close();
      }}
      autoFocus={false}
    >
      {children}
    </dialog>
  );
};
