import React, { <PERSON>psWith<PERSON>hildren, ReactNode, SyntheticEvent } from "react";

import { Box } from "../../../fermions/index.ts";
import {
  getClassNames,
  returnStringIfTrue
} from "../../../helpers/componentHelpers.ts";
import { TextSize } from "../../Text/TextTypes.ts";
import { Text } from "../../Text/index.ts";
import "./DropdownItem.scss";

export interface DropdownItemProps {
  id?: string;
  onClick?: (e: SyntheticEvent) => void;
  description?: string | ReactNode;
  leftElement?: ReactNode;
  rightElement?: ReactNode;
  isSelected?: boolean;
  isHighlighted?: boolean;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

export const DropdownItem = ({
  id,
  onClick,
  description,
  leftElement,
  rightElement,
  isSelected,
  isHighlighted,
  style = {},
  className = "",
  disabled = false,
  children
}: PropsWithChildren<DropdownItemProps>) => {
  return (
    <button
      id={id}
      role="option"
      className={getClassNames([
        className,
        "dropdown-item",
        returnStringIfTrue(!disabled && isSelected, "dropdown-item--selected"),
        returnStringIfTrue(isHighlighted, "dropdown-item--highlighted")
      ])}
      onClick={e => {
        onClick?.(e);
        e.preventDefault();
        e.stopPropagation();
      }}
      onMouseDown={e => e.preventDefault()}
      disabled={disabled}
      style={style}
      type="button"
    >
      {leftElement && (
        <Box className="dropdown-item__left-element">{leftElement}</Box>
      )}

      <span className="dropdown-item__label">
        {children}
        {typeof description === "string" ? (
          <Text size={TextSize.S} className="dropdown-item__label__description">
            {description}
          </Text>
        ) : (
          description
        )}
      </span>
      {rightElement && (
        <Box className="dropdown-item__right-element">{rightElement}</Box>
      )}
    </button>
  );
};
