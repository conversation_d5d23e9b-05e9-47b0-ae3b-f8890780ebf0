import React, {
  PropsWithChildren,
  ReactNode,
  SyntheticEvent,
  useCallback,
  useRef
} from "react";

import useOnClickOutside from "use-onclickoutside";

import { Box, FermionProps, Inline } from "../../fermions/index.ts";
import { FloatingWithParent } from "../FloatingWithParent/FloatingWithParent.tsx";
import { FloatingWithParentPosition } from "../FloatingWithParent/FloatingWithParentTypes.ts";
import { OpenCloseIcon } from "../Icon/index.ts";
import { Text } from "../Text/index.ts";
import "./DropdownMenu.scss";

export interface CustomDropdownMenuTriggerProps {
  onClick?: (e: SyntheticEvent) => void;
  isOpen?: boolean;
  disabled?: boolean;
}

export type CustomDropdownMenuTrigger = (
  props: CustomDropdownMenuTriggerProps
) => ReactNode;

interface DropdownMenuProps {
  trigger: string | CustomDropdownMenuTrigger;
  isOpen: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  position?: `${FloatingWithParentPosition}`;
  width?: FermionProps["width"];
  isNested?: boolean;
  minWidthAsParent?: boolean;
  disabled?: boolean;
  className?: string;
}
export const DropdownMenu = ({
  trigger,
  width,
  isOpen,
  onOpenChange,
  position,
  minWidthAsParent = false,
  disabled = false,
  className = "",
  children
}: PropsWithChildren<DropdownMenuProps>) => {
  const dropdownMenuRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  useOnClickOutside(dropdownMenuRef, () => {
    if (isOpen) {
      onOpenChange?.(false);
    }
  });

  const handleClick = useCallback(
    (e?: SyntheticEvent<Element, Event>) => {
      e?.preventDefault();
      e?.stopPropagation();
      if (disabled) {
        return;
      }
      onOpenChange?.(!isOpen);
    },
    [disabled, isOpen, onOpenChange]
  );

  return (
    <Box
      classNames={["dropdown-menu", className]}
      width={width ?? "fit"}
      ref={dropdownMenuRef}
    >
      <Box ref={triggerRef} className="dropdown-menu__trigger">
        {typeof trigger === "string" ? (
          <button
            className="dropdown-menu__trigger__default"
            onClick={handleClick}
            onMouseDown={e => e.preventDefault()}
            disabled={disabled}
            type="button"
          >
            <Inline
              className="dropdown-menu__trigger__default"
              alignment="center"
            >
              <Text>{trigger}</Text>
              <OpenCloseIcon isOpen={isOpen} />
            </Inline>
          </button>
        ) : (
          trigger({ onClick: handleClick, isOpen, disabled })
        )}
      </Box>

      {isOpen && (
        <FloatingWithParent
          parentRef={triggerRef}
          className="dropdown-menu__floating-container"
          position={position}
          onOpenChange={onOpenChange}
          minWidthAsParent={minWidthAsParent}
        >
          {children}
        </FloatingWithParent>
      )}
    </Box>
  );
};

DropdownMenu.displayName = "DropdownMenu";
