.variable-field {
  &__value {
    min-height: var(--components-inputs-height, 32px);
    border-radius: var(--components-inputs-border-radius, 8px);
    border-top: var(--components-inputs-border-width-top, 1px) solid
      var(--components-inputs-color-border, #dae3ed);
    border-right: var(--components-inputs-border-width-right, 1px) solid
      var(--components-inputs-color-border, #dae3ed);
    border-bottom: var(--components-inputs-border-width-bottom, 1px) solid
      var(--components-inputs-color-border, #dae3ed);
    border-left: var(--components-inputs-border-width-left, 1px) solid
      var(--components-inputs-color-border, #dae3ed);
    background: var(--components-inputs-color-background, #fff);
    background: var(
      --components-inputs-color-background,
      color(display-p3 1 1 1)
    );
    outline-offset: -1px;
    cursor: text;
    padding: 0px;

    &:focus-within {
      border-color: var(--components-inputs-focus-color-border, #0083ff);
    }
  }

  &:has(.variable-field__incorrect-type) {
    & .variable-field__value:not(:focus-within) {
      border-color: var(--color-traffic-warning);
    }
  }

  &:has(.variable-field-part-variable--invalid),
  &:has(.alert--variant-danger) {
    & .variable-field__value:not(:focus-within) {
      border-color: var(--color-traffic-danger);
    }
  }

  &__mock-highlight {
    background: #79baff73;
    z-index: 1;
    cursor: text;
  }

  &__suggestions.floating-with-parent {
    top: anchor(bottom) !important;
    left: anchor(left) !important;
  }

  &__type,
  &__clear-button {
    visibility: hidden;
    display: none;
  }

  &:hover,
  &:focus-within {
    & .variable-field__type,
    & .variable-field__clear-button {
      visibility: visible;
      display: flex;
    }
  }
}
