import { useMemo } from "react";

import { Doc } from "@automerge/automerge-repo";
import { useOutletContext } from "react-router-dom";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import {
  DisplayQuestionType,
  useQuestionTypeDisplay
} from "@src/hooks/useQuestionTypeDisplay";
import { VariableTypeDefinition } from "@src/types/FlowConfiguration/Variables";
import { ExtendedQuestionTypes, QuestionTypes } from "@src/types/Question";
import { WorkspaceDocument } from "@src/types/documentTypes";

import {
  HandleChangeInternally,
  VariableFieldPart,
  VariableSchema
} from "./VariableFieldTypes";
import {
  getMatchingVariable,
  getQuestionTypeDetail
} from "./variableFieldHelpers";

export const useVariableFieldValidation = ({
  value,
  schema,
  isExpression,
  handleChangeInternally,
  isFocused
}: {
  value?: VariableFieldPart[];
  schema?: VariableSchema;
  isExpression?: boolean;
  handleChangeInternally: HandleChangeInternally;
  isFocused?: boolean;
}) => {
  const d = useDictionary();
  const { document } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
  }>();
  const { variablesByName, variablesByPath } = useConfigurationFlowContext();
  const { displayQuestionType } = useQuestionTypeDisplay();

  const { isCorrectType, errorMessage } = useMemo(() => {
    const defaultWhenNoValidation = {
      isCorrectType: true,
      errorMessage: undefined
    };

    if (
      !value?.length ||
      !schema ||
      !schema.type ||
      schema.type === "unknown"
    ) {
      return defaultWhenNoValidation;
    }

    // TODO: add schema type validation to expressions
    if (isExpression) {
      return defaultWhenNoValidation;
    }

    if (value.length > 3) {
      return {
        isCorrectType: false,
        // Since it is not an expression, it should only contain 1 variable
        errorMessage: d("ui.configuration.flows.variables.errors.tooManyParts")
      };
    }

    // Text (no variables)
    if (value.length <= 1) {
      const textPart = value[0];
      if (!textPart.value) {
        return defaultWhenNoValidation;
      }
      // These question types must use a variable (not value directly)
      if (
        [
          QuestionTypes.JSON,
          QuestionTypes.LIST,
          QuestionTypes.TABLE,
          QuestionTypes.FILES,
          QuestionTypes.MULTISELECT
        ].includes(schema.type as QuestionTypes)
      ) {
        const questionTypeDisplay = displayQuestionType(schema.type);
        return {
          isCorrectType: false,
          errorMessage: d(
            "ui.configuration.flows.variables.errors.noDirectValues",
            { type: questionTypeDisplay.label }
          )
        };
      }
      if (schema.type === QuestionTypes.NUMBER) {
        const isValidNumber = !isNaN(Number(textPart.value));
        return {
          isCorrectType: isValidNumber,
          errorMessage: isValidNumber
            ? ""
            : d("ui.configuration.flows.variables.errors.invalidNumber")
        };
      } else if (schema.type === QuestionTypes.BOOLEAN) {
        const isValidBoolean = [
          "true",
          "false",
          "yes",
          "no",
          "1",
          "0"
        ].includes(textPart.value.toLowerCase());
        return {
          isCorrectType: isValidBoolean,
          errorMessage: isValidBoolean
            ? ""
            : d("ui.configuration.flows.variables.errors.invalidBoolean")
        };
      } else if (schema.type === QuestionTypes.DATE) {
        // Regex match "YYYY-MM-DD"
        const isValidDate =
          /^\d{4}-(0[1-9]|1[012])-(0[1-9]|[12]\d|3[01])$/.test(textPart.value);
        return {
          isCorrectType: isValidDate,
          errorMessage: isValidDate
            ? ""
            : d("ui.configuration.flows.variables.errors.invalidDate")
        };
      }
      return defaultWhenNoValidation;
    }

    // Since it is not an expression (checked above), it should only contain 1 variable
    if (
      value[0].value.length > 0 ||
      value[value.length - 1].value.length > 0 ||
      value.length > 3
    ) {
      return {
        isCorrectType: false,
        errorMessage: d(
          "ui.configuration.flows.variables.errors.cannotBeExpression"
        )
      };
    }

    // Contains variable
    const variablePart = value[1];

    const variable =
      variablesByName?.[variablePart.value] ??
      variablesByPath?.[variablePart.value];

    if (!variable || variable.__type === "unknown") {
      return defaultWhenNoValidation;
    }

    if (
      schema.type.startsWith(ExtendedQuestionTypes.FORM) ||
      schema.type.startsWith(ExtendedQuestionTypes.FOUNDATION) ||
      schema.type.startsWith(ExtendedQuestionTypes.SERIES_INTERVAL)
    ) {
      const { matchingVariable, pathToMatchingVariable } = getMatchingVariable({
        value: variablePart.value,
        variablesByName,
        variablesByPath
      });

      const typeValidationProps = {
        schema,
        variable,
        matchingVariable,
        pathToMatchingVariable,
        variablePart,
        handleChangeInternally,
        displayQuestionType,
        isFocused,
        document,
        d
      };
      if (schema.type.startsWith(ExtendedQuestionTypes.FORM)) {
        return validateIsFormType(typeValidationProps);
      } else if (schema.type?.startsWith(ExtendedQuestionTypes.FOUNDATION)) {
        return validateIsFoundationType(typeValidationProps);
      } else if (
        schema.type?.startsWith(ExtendedQuestionTypes.SERIES_INTERVAL)
      ) {
        return validateIsSeriesIntervalType(typeValidationProps);
      }
    }

    const isCorrectType = (() => {
      if (
        [`${QuestionTypes.SELECT}`, `${QuestionTypes.MULTISELECT}`].includes(
          schema.type
        )
      ) {
        // TODO: Validate against the options
        return true;
      }

      if (schema.type === QuestionTypes.LIST) {
        return (
          variable.__type === QuestionTypes.LIST ||
          variable.__type === QuestionTypes.TABLE
        );
      } else if (schema.type === QuestionTypes.JSON) {
        const { type } = getQuestionTypeDetail(variable.__type);
        return (
          type === QuestionTypes.JSON ||
          (Object.values(ExtendedQuestionTypes) as string[]).includes(type)
        );
      }

      return variable.__type === schema.type;
    })();

    const expectedType = displayQuestionType(schema.type);
    const actualType = displayQuestionType(variable.__type);
    return {
      isCorrectType,
      errorMessage: isCorrectType
        ? ""
        : d("ui.configuration.flows.variables.errors.invalidVariableType", {
            expectedType: expectedType.label,
            actualType: actualType.label
          })
    };
  }, [
    value,
    schema,
    isExpression,
    variablesByName,
    variablesByPath,
    d,
    displayQuestionType,
    handleChangeInternally,
    isFocused,
    document
  ]);

  return {
    isCorrectType,
    errorMessage
  };
};

type TypeValidationProps = {
  schema: VariableSchema;
  variable: VariableTypeDefinition;
  matchingVariable?: VariableTypeDefinition;
  pathToMatchingVariable?: string;
  variablePart: VariableFieldPart;
  handleChangeInternally: HandleChangeInternally;
  isFocused?: boolean;
  displayQuestionType: DisplayQuestionType;
  document: Doc<WorkspaceDocument>;
  d: Dictionary;
};

const validateIsFormType = ({
  schema,
  variable,
  matchingVariable,
  pathToMatchingVariable,
  variablePart,
  handleChangeInternally,
  isFocused,
  displayQuestionType,
  document,
  d
}: TypeValidationProps) => {
  const { type, configurationId } = getQuestionTypeDetail(
    matchingVariable?.__type
  );
  const formConfiguration = document.forms[configurationId ?? ""];
  const { configurationId: schemaConfigurationId } = getQuestionTypeDetail(
    schema.type
  );
  const schemaFormConfiguration = document.forms[schemaConfigurationId ?? ""];

  if (matchingVariable && type === ExtendedQuestionTypes.FORM) {
    const expectedVariable = `${pathToMatchingVariable}.id`;
    if (
      pathToMatchingVariable === variablePart.value &&
      expectedVariable !== variablePart.value &&
      !isFocused
    ) {
      // Ensure the variable is pointing to the form ID (live form), not the form directly
      handleChangeInternally({
        value: expectedVariable,
        index: 1,
        notifyExternalChange: true
      });
    }

    if (
      variablePart.value === pathToMatchingVariable ||
      expectedVariable === variablePart.value
    ) {
      if (
        !formConfiguration ||
        !schemaFormConfiguration ||
        configurationId === schemaConfigurationId
      ) {
        return {
          isCorrectType: true
        };
      }

      // Incorrect form configuration
      const expectedType = displayQuestionType(schema.type);
      const actualType = displayQuestionType(matchingVariable.__type);
      return {
        isCorrectType: false,
        errorMessage: d(
          "ui.configuration.flows.variables.errors.invalidVariableType",
          {
            expectedType: expectedType.label,
            actualType: actualType.label
          }
        )
      };
    }
  } else if (
    matchingVariable &&
    (type === ExtendedQuestionTypes.FOUNDATION ||
      type === ExtendedQuestionTypes.SERIES_INTERVAL) &&
    variablePart.value === `${pathToMatchingVariable}.id`
  ) {
    // Should not point to a foundation
    const expectedType = displayQuestionType(schema.type);
    const actualType = displayQuestionType(matchingVariable.__type);
    return {
      isCorrectType: false,
      errorMessage: d(
        "ui.configuration.flows.variables.errors.invalidVariableType",
        {
          expectedType: expectedType.label,
          actualType: actualType.label
        }
      )
    };
  }

  const isCorrectType = variable.__type === QuestionTypes.NUMBER;

  if (isCorrectType) {
    return {
      isCorrectType: true
    };
  }

  const expectedType = displayQuestionType(schema.type);
  const actualType = displayQuestionType(variable.__type);
  return {
    isCorrectType: false,
    errorMessage: d(
      "ui.configuration.flows.variables.errors.invalidVariableType",
      {
        expectedType: expectedType.label,
        actualType: actualType.label
      }
    )
  };
};

const validateIsFoundationType = ({
  schema,
  variable,
  matchingVariable,
  pathToMatchingVariable,
  variablePart,
  document,
  handleChangeInternally,
  displayQuestionType,
  isFocused,
  d
}: TypeValidationProps) => {
  const { type, configurationId } = getQuestionTypeDetail(
    matchingVariable?.__type
  );
  const foundationConfiguration =
    document.foundations.entities[configurationId ?? ""];

  const { configurationId: schemaConfigurationId } = getQuestionTypeDetail(
    schema.type
  );
  const schemaFoundationConfiguration =
    document.foundations.entities[schemaConfigurationId ?? ""];

  if (matchingVariable && type === ExtendedQuestionTypes.FOUNDATION) {
    // Pointing to the parent ID
    if (variablePart.value === `${pathToMatchingVariable}.parentId`) {
      // Do the check to match the parent foundation level
      if (schemaFoundationConfiguration && foundationConfiguration) {
        const currentFoundationLevelIndex = foundationConfiguration
          ? document.foundations.order.indexOf(foundationConfiguration.id)
          : -1;

        const parentFoundationConfigurationId =
          currentFoundationLevelIndex <= 0
            ? undefined
            : document.foundations.order[currentFoundationLevelIndex - 1];

        if (parentFoundationConfigurationId !== schemaConfigurationId) {
          const expectedType = displayQuestionType(schema.type);
          const actualType = displayQuestionType(
            `foundation.${parentFoundationConfigurationId}`
          );
          return {
            isCorrectType: false,
            errorMessage: d(
              "ui.configuration.flows.variables.errors.invalidVariableType",
              {
                expectedType: expectedType.label,
                actualType: actualType.label
              }
            )
          };
        }
      }
      return {
        isCorrectType: true
      };
    }

    // Ensure the variable is pointing to the foundation ID, or parentId (not the foundation directly)
    const expectedVariable = `${pathToMatchingVariable}.id`;
    if (
      pathToMatchingVariable === variablePart.value &&
      expectedVariable !== variablePart.value &&
      !isFocused
    ) {
      handleChangeInternally({
        value: expectedVariable,
        index: 1,
        notifyExternalChange: true
      });
    }

    if (
      variablePart.value === pathToMatchingVariable ||
      expectedVariable === variablePart.value
    ) {
      if (
        !foundationConfiguration ||
        !schemaFoundationConfiguration ||
        configurationId === schemaConfigurationId
      ) {
        return {
          isCorrectType: true
        };
      }

      const expectedType = displayQuestionType(schema.type);
      const actualType = displayQuestionType(matchingVariable.__type);
      return {
        isCorrectType: false,
        errorMessage: d(
          "ui.configuration.flows.variables.errors.invalidVariableType",
          {
            expectedType: expectedType.label,
            actualType: actualType.label
          }
        )
      };
    }
  } else if (
    matchingVariable &&
    (type === ExtendedQuestionTypes.FORM ||
      type === ExtendedQuestionTypes.SERIES_INTERVAL) &&
    variablePart.value === `${pathToMatchingVariable}.id`
  ) {
    // Should not point to a form
    const expectedType = displayQuestionType(schema.type);
    const actualType = displayQuestionType(matchingVariable.__type);
    return {
      isCorrectType: false,
      errorMessage: d(
        "ui.configuration.flows.variables.errors.invalidVariableType",
        {
          expectedType: expectedType.label,
          actualType: actualType.label
        }
      )
    };
  }

  const isCorrectType = variable.__type === QuestionTypes.NUMBER;

  if (isCorrectType) {
    return {
      isCorrectType: true
    };
  }

  const expectedType = displayQuestionType(schema.type);
  const actualType = displayQuestionType(variable.__type);
  return {
    isCorrectType,
    errorMessage: d(
      "ui.configuration.flows.variables.errors.invalidVariableType",
      {
        expectedType: expectedType.label,
        actualType: actualType.label
      }
    )
  };
};

const validateIsSeriesIntervalType = ({
  schema,
  variable,
  matchingVariable,
  pathToMatchingVariable,
  variablePart,
  handleChangeInternally,
  isFocused,
  displayQuestionType,
  document,
  d
}: TypeValidationProps) => {
  const { type, configurationId } = getQuestionTypeDetail(
    matchingVariable?.__type
  );
  const seriesConfiguration = document.series[configurationId ?? ""];
  const { configurationId: schemaConfigurationId } = getQuestionTypeDetail(
    schema.type
  );
  const schemaSeriesConfiguration =
    document.series[schemaConfigurationId ?? ""];

  if (matchingVariable && type === ExtendedQuestionTypes.SERIES_INTERVAL) {
    const expectedVariable = `${pathToMatchingVariable}.id`;
    if (
      pathToMatchingVariable === variablePart.value &&
      expectedVariable !== variablePart.value &&
      !isFocused
    ) {
      // Ensure the variable is pointing to the form ID (live form), not the form directly
      handleChangeInternally({
        value: expectedVariable,
        index: 1,
        notifyExternalChange: true
      });
    }

    if (
      expectedVariable === variablePart.value ||
      variablePart.value === pathToMatchingVariable
    ) {
      if (
        !seriesConfiguration ||
        !schemaSeriesConfiguration ||
        matchingVariable.__type === schema.type
      ) {
        return {
          isCorrectType: true
        };
      }

      // Incorrect form configuration
      const expectedType = displayQuestionType(schema.type);
      const actualType = displayQuestionType(matchingVariable.__type);
      return {
        isCorrectType: false,
        errorMessage: d(
          "ui.configuration.flows.variables.errors.invalidVariableType",
          {
            expectedType: expectedType.label,
            actualType: actualType.label
          }
        )
      };
    }
  } else if (
    matchingVariable &&
    (type === ExtendedQuestionTypes.FOUNDATION ||
      type === ExtendedQuestionTypes.FORM) &&
    variablePart.value === `${pathToMatchingVariable}.id`
  ) {
    // Should not point to a foundation
    const expectedType = displayQuestionType(schema.type);
    const actualType = displayQuestionType(matchingVariable.__type);
    return {
      isCorrectType: false,
      errorMessage: d(
        "ui.configuration.flows.variables.errors.invalidVariableType",
        {
          expectedType: expectedType.label,
          actualType: actualType.label
        }
      )
    };
  }

  const isCorrectType = variable.__type === QuestionTypes.TEXT;

  if (isCorrectType) {
    return {
      isCorrectType: true
    };
  }

  const expectedType = displayQuestionType(schema.type);
  const actualType = displayQuestionType(variable.__type);
  return {
    isCorrectType: false,
    errorMessage: d(
      "ui.configuration.flows.variables.errors.invalidVariableType",
      {
        expectedType: expectedType.label,
        actualType: actualType.label
      }
    )
  };
};
