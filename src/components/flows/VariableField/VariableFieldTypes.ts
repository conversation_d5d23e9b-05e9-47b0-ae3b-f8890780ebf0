import { Question } from "@src/types/Question";

export type HandleFocusOnPartFocusLocation =
  | undefined
  | "start"
  | "end"
  | "all"
  | {
      startIndex: number;
      endIndex: number;
    };

export type HandleFocusOnPart = (props: {
  index: number;
  focusLocation: HandleFocusOnPartFocusLocation;
  onNextRender?: boolean;
}) => void;

export type HandleChangeInternally = (props: {
  value?: string;
  index?: number;
  notifyExternalChange?: boolean;
  skipHistoryUpdate?: boolean;
  skipFocusOnPart?: boolean;
}) => void;

export type VariableSchema = Omit<
  Question,
  "identifier" | "id" | "text" | "type"
> & {
  type?: string;
};

export enum VariableFieldPartVariant {
  TEXT = "text",
  VARIABLE = "variable"
}

export type VariableFieldPart = {
  value: string;
  variant: `${VariableFieldPartVariant}`;
};
