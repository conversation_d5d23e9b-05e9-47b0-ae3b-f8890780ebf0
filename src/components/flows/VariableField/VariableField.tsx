import React, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>board<PERSON><PERSON><PERSON><PERSON><PERSON>,
  use<PERSON><PERSON><PERSON>,
  useEffect,
  useMemo
} from "react";

import {
  Box,
  ColorText,
  FermionProps,
  Icon,
  IconButton,
  Inline,
  Label,
  LabelTextPosition,
  Tooltip
} from "@oneteam/onetheme";
import useOnClickOutside from "use-onclickoutside";

import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import { QuestionType } from "@components/forms/QuestionType/QuestionType";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";

import { QuestionTypes } from "@src/types/Question";

import "./VariableField.scss";
import { VariableFieldPartText } from "./VariableFieldPart/VariableFieldPartText";
import { VariableFieldPartVariable } from "./VariableFieldPart/VariableFieldPartVariable";
import {
  <PERSON>le<PERSON><PERSON>eInter<PERSON><PERSON>,
  <PERSON>leF<PERSON>us<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>leFocusOnPartFocusLocation,
  VariableFieldPart,
  VariableFieldPartVariant,
  VariableSchema
} from "./VariableFieldTypes";
import { useVariableFieldValidation } from "./useVariableFieldValidation";
import {
  extractValueForExternal,
  getCursorLocationWithinParts,
  getVariableFieldParts
} from "./variableFieldHelpers";

export interface VariableFieldProps {
  label?: string;
  description?: string;
  required?: boolean;
  hidden?: boolean;
  error?: string;
  disabled?: boolean;
  width?: FermionProps["width"];
  value?: string;
  onChange?: (value: string) => void;
  regex?: RegExp;
  className?: string;

  isExpression?: boolean;
  schema?: VariableSchema;
}

export const VariableField = ({
  label = "",
  value,
  onChange,
  description,
  required,
  hidden,
  error,
  disabled,
  width = "100",
  regex,
  className,
  isExpression,
  schema
}: VariableFieldProps) => {
  // isFocused will be used shortly to show the suggestions
  const [isFocused, setIsFocused] = React.useState(false);
  const [history, setHistory] = React.useState<{
    past: string[];
    present: string;
    future: string[];
  }>({
    past: [],
    present: value ?? "",
    future: []
  });

  const variableFieldId = useMemo(() => customNanoId(), []);
  const [highlightAll, setHighlightAll] = React.useState(false);
  const variableFieldRef = React.useRef<HTMLDivElement>(null);
  const [tempValue, setTempValue] = React.useState<VariableFieldPart[]>(
    getVariableFieldParts(value)
  );
  const { variablesByName, variablesByPath } = useConfigurationFlowContext();

  useEffect(() => {
    setTempValue(getVariableFieldParts(value));
  }, [value]);

  useEffect(() => {
    if (!variableFieldRef.current?.contains(document.activeElement)) {
      setHighlightAll(false);
      setIsFocused(false);
    }
  }, [value, variableFieldRef]);

  const handleFocusOnPart: HandleFocusOnPart = useCallback(
    ({ index, focusLocation, onNextRender }) => {
      const focusOnPart = () => {
        const input = document.getElementById(
          `${variableFieldId}-input-${index}`
        ) as HTMLInputElement;
        if (input) {
          input.focus();
          setIsFocused(true);

          if (typeof focusLocation === "object") {
            const { startIndex, endIndex } = focusLocation;
            input.setSelectionRange(startIndex, endIndex);
          } else {
            switch (focusLocation) {
              case "end":
                input.setSelectionRange(input.value.length, input.value.length);
                break;
              case "start":
                input.setSelectionRange(0, 0);
                break;
              case "all":
              default:
                input.setSelectionRange(0, input.value.length);
                break;
            }
          }
        }
      };

      if (onNextRender) {
        setTimeout(() => focusOnPart(), 0);
      } else {
        focusOnPart();
      }
    },
    [variableFieldId]
  );

  const handleChangeInternally: HandleChangeInternally = useCallback(
    ({
      value: newValue,
      index,
      notifyExternalChange = false,
      skipHistoryUpdate = false,
      skipFocusOnPart = false
    }) => {
      if (!variablesByPath || !variablesByName) {
        return;
      }
      if (newValue && regex && !regex?.test(newValue)) {
        return;
      }

      setTempValue(prev => {
        let newParts = [...prev];
        if (index === undefined) {
          // Changing the entire value
          newParts = getVariableFieldParts(newValue);
          if (!skipFocusOnPart) {
            handleFocusOnPart({
              index: newParts.length - 1,
              focusLocation: "end",
              onNextRender: true
            });
          }
        } else {
          // Changing a specific part of the value
          const existingPart = newParts[index];
          if (newValue === undefined) {
            // Remove part
            newParts.splice(index, 1);
            // TODO: check if before and after parts are text and merge them
            if (!skipFocusOnPart) {
              handleFocusOnPart({
                index: Math.max(0, index - 1),
                focusLocation: "end",
                onNextRender: true
              });
            }
          } else if (
            existingPart.variant === VariableFieldPartVariant.VARIABLE
          ) {
            // Variable variant
            newParts[index] = {
              ...newParts[index],
              value: newValue
            };
          } else {
            // Text variant
            const updatedParts = getVariableFieldParts(
              newValue,
              newParts[index - 1]
            );

            if (updatedParts.length === 1) {
              // User has updated the text
              newParts[index] = {
                ...newParts[index],
                ...updatedParts[0]
              };
            } else {
              // User has added variable(s) into the text
              newParts = [
                ...newParts.slice(0, index),
                ...updatedParts,
                ...(newParts.slice(index + 1) ?? [])
              ];

              const { focusIndexWithinParts, cursorIndex } =
                getCursorLocationWithinParts({
                  nextValue: newValue,
                  existingValue: existingPart.value,
                  nextParts: updatedParts
                });

              if (!skipFocusOnPart) {
                handleFocusOnPart({
                  index: index + focusIndexWithinParts,
                  focusLocation: {
                    startIndex: cursorIndex,
                    endIndex: cursorIndex
                  },
                  onNextRender: true
                });
              }
            }
          }
        }

        const extractValue = extractValueForExternal(newParts, {
          variablesByPath,
          variablesByName
        });

        // If the user is typing
        if (!skipHistoryUpdate && extractValue !== value) {
          setHistory(prev => ({
            past: [...prev.past, prev.present],
            present: extractValue,
            future: []
          }));
        }

        if (notifyExternalChange && extractValue !== value) {
          onChange?.(extractValue);
        }
        return newParts;
      });
    },
    [
      handleFocusOnPart,
      onChange,
      regex,
      value,
      variablesByName,
      variablesByPath
    ]
  );

  const { isCorrectType, errorMessage } = useVariableFieldValidation({
    value: tempValue,
    schema,
    isExpression,
    handleChangeInternally,
    isFocused
  });

  const handleClear = useCallback(
    (startingValue = "") => {
      handleChangeInternally({
        value: startingValue
      });
    },
    [handleChangeInternally]
  );

  const onKeyDown: (index: number) => KeyboardEventHandler = useCallback(
    (index: number) => (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (!variablesByPath || !variablesByName) {
        return;
      }
      if (e.key === "Enter") {
        e.currentTarget.blur();
        return;
      }

      if (
        (!e.shiftKey && e.key === "Tab" && index === tempValue.length - 1) ||
        (e.shiftKey && e.key === "Tab" && index === 0)
      ) {
        setIsFocused(false);
        const extractValue = extractValueForExternal(tempValue, {
          variablesByPath,
          variablesByName
        });
        if (extractValue !== value) {
          onChange?.(extractValue);
        }
      }

      // Undo and redo
      if (e.key === "z" && (e.ctrlKey || e.metaKey)) {
        e.stopPropagation();
        e.preventDefault();
        if (e.shiftKey) {
          // REDO
          if (history.future.length > 0) {
            const nextValue = history.future[0];
            setHistory({
              past: [...history.past, history.present],
              present: nextValue,
              future: history.future.slice(1)
            });
            handleChangeInternally({
              value: nextValue,
              skipHistoryUpdate: true
            });
          }
        } else if (history.past.length > 0) {
          const previousValue = history.past[history.past.length - 1];
          setHistory({
            past: history.past.slice(0, -1),
            present: previousValue,
            future: [history.present, ...history.future]
          });
          handleChangeInternally({
            value: previousValue,
            skipHistoryUpdate: true
          });
        }
        return;
      }

      // Select all
      if (e.key === "a" && (e.ctrlKey || e.metaKey)) {
        e.stopPropagation();
        e.preventDefault();
        setHighlightAll(true);
        return;
      }

      // When all selected
      if (highlightAll) {
        // Copy
        if (e.key === "c" && (e.ctrlKey || e.metaKey)) {
          e.stopPropagation();
          e.preventDefault();
          const selectedText = extractValueForExternal(tempValue, {
            variablesByPath,
            variablesByName
          });
          if (selectedText) {
            navigator.clipboard.writeText(selectedText);
          }
          return;
        }

        // Remove all
        if (e.key === "Backspace") {
          handleClear();
          e.stopPropagation();
          e.preventDefault();
          setHighlightAll(false);
          return;
        }

        // Paste
        if (e.key === "v" && (e.ctrlKey || e.metaKey)) {
          e.stopPropagation();
          e.preventDefault();
          navigator.clipboard.readText().then(text => {
            handleChangeInternally({
              value: text
            });
          });
          setHighlightAll(false);
          return;
        }

        // Starting to variant
        if (
          !e.metaKey &&
          !e.ctrlKey &&
          e.key.length === 1 &&
          e.key.match(/^[0-9a-zA-Z]+$/)
        ) {
          e.stopPropagation();
          e.preventDefault();
          handleClear(e.key);
          setHighlightAll(false);
          return;
        }

        if (e.metaKey) {
          return;
        }
      }

      setHighlightAll(false);

      if (e.key === "c" && (e.ctrlKey || e.metaKey)) {
        const currentPart = tempValue[index];
        // If current part is variable and everything is selected
        if (
          currentPart.variant === VariableFieldPartVariant.VARIABLE &&
          e.currentTarget.selectionStart === 0 &&
          e.currentTarget.selectionEnd === e.currentTarget.value.length
        ) {
          const selectedText = `{{${currentPart.value}}}`;
          navigator.clipboard.writeText(selectedText);
          e.stopPropagation();
          e.preventDefault();
        }

        return;
      }

      // Focus on previous input
      if (e.key === "ArrowLeft" && e.currentTarget.selectionStart === 0) {
        e.stopPropagation();
        e.preventDefault();
        handleFocusOnPart({
          index: index - 1,
          focusLocation: "end"
        });
      }

      // Focus on next input
      if (
        e.key === "ArrowRight" &&
        e.currentTarget.selectionEnd === e.currentTarget.value.length
      ) {
        e.stopPropagation();
        e.preventDefault();
        handleFocusOnPart({
          index: Math.min(tempValue.length, index + 1),
          focusLocation: "start"
        });
      }

      // Backspace and current input value is empty - focus on previous input
      if (e.key === "Backspace" && index !== 0) {
        if (e.currentTarget.value.length === 0) {
          e.stopPropagation();
          e.preventDefault();
          // If current part is empty and it's a variable variant -> remove it
          const currentPart = tempValue[index];
          if (currentPart?.variant === VariableFieldPartVariant.VARIABLE) {
            handleChangeInternally({
              value: undefined,
              index
            });
            return;
          }
          handleFocusOnPart({
            index: index - 1,
            focusLocation: "end"
          });
        } else if (
          e.currentTarget.selectionStart === 0 &&
          e.currentTarget.selectionEnd === 0
        ) {
          // If the cursor is at the start of the input and the input is empty, focus on previous input
          handleFocusOnPart({
            index: index - 1,
            focusLocation: "end"
          });
        }
      }
    },
    [
      handleChangeInternally,
      handleClear,
      handleFocusOnPart,
      highlightAll,
      history.future,
      history.past,
      history.present,
      onChange,
      tempValue,
      value,
      variablesByName,
      variablesByPath
    ]
  );

  useOnClickOutside(variableFieldRef, () => {
    if (!variablesByPath || !variablesByName) {
      return;
    }
    const updatedValue = extractValueForExternal(tempValue, {
      variablesByPath,
      variablesByName
    });
    if (
      updatedValue !== value &&
      variableFieldRef.current?.contains(document.activeElement)
    ) {
      (document.activeElement as HTMLInputElement)?.blur();
      handleChangeInternally({
        value: updatedValue,
        notifyExternalChange: true,
        skipFocusOnPart: true
      });
    }
  });

  const schemaTypeIconDisplay = useMemo(() => {
    if (
      !schema?.type ||
      [
        "unknown",
        `${QuestionTypes.SELECT}`,
        `${QuestionTypes.MULTISELECT}`
      ].includes(schema?.type)
    ) {
      return <></>;
    }

    return (
      <Box alignment="center" padding="025" className="variable-field__type">
        <QuestionType iconOnly type={schema.type} color={ColorText.SECONDARY} />
      </Box>
    );
  }, [schema?.type]);

  if (hidden) {
    return null;
  }

  return (
    <Box
      ref={variableFieldRef}
      classNames={["variable-field", className]}
      width={width}
      onClick={e => {
        if (!variableFieldRef.current?.contains(document.activeElement)) {
          handleFocusOnPart({
            index: isExpression
              ? tempValue.length - 1
              : Math.max(0, tempValue.length - 2),
            focusLocation: "end"
          });
          setIsFocused(true);
        }
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      <Label
        label={label}
        required={required}
        hidden={hidden}
        disabled={disabled}
        textPosition={LabelTextPosition.TOP}
        description={description}
        error={error}
      >
        <Inline className="variable-field__value" width="100" height="100">
          <Inline
            padding="050"
            alignment="left"
            width="100"
            gap="050"
            spaceBetween
          >
            <Inline
              wrap
              style={{
                maxWidth: "100%"
              }}
              alignment="left"
              position="relative"
              width="100"
            >
              {tempValue.map((part, index) => {
                if (!isFocused && !isExpression && tempValue.length === 3) {
                  if (
                    (index === 0 || index === tempValue.length - 1) &&
                    !part.value
                  ) {
                    return <></>;
                  }
                }
                const onChange: ChangeEventHandler<HTMLTextAreaElement> = e =>
                  handleChangeInternally({
                    value: e.target.value ?? "",
                    index
                  });

                const onBlur: FocusEventHandler = () => {
                  setTimeout(() => {
                    if (
                      !variableFieldRef.current?.contains(
                        document.activeElement
                      )
                    ) {
                      handleChangeInternally({
                        value: part.value,
                        index,
                        notifyExternalChange: true
                      });
                      setHighlightAll(false);
                      setIsFocused(false);
                    }
                  }, 100);
                };

                const onFocus: FocusEventHandler<HTMLTextAreaElement> = () => {
                  setHighlightAll(false);
                  setIsFocused(true);
                };

                const partId = `${variableFieldId}-input-${index}`;

                if (part.variant === VariableFieldPartVariant.TEXT) {
                  return (
                    <VariableFieldPartText
                      id={partId}
                      key={partId}
                      value={part.value}
                      onChange={onChange}
                      onBlur={onBlur}
                      onKeyDown={onKeyDown(index)}
                      onFocus={onFocus}
                    />
                  );
                }

                return (
                  <VariableFieldPartVariable
                    id={partId}
                    key={partId}
                    value={part.value}
                    handleFocusOnThisPart={(
                      focusLocation: HandleFocusOnPartFocusLocation
                    ) => {
                      handleFocusOnPart({
                        index,
                        focusLocation
                      });
                    }}
                    onChange={onChange}
                    onBlur={onBlur}
                    onKeyDown={onKeyDown(index)}
                    onFocus={onFocus}
                    schema={isExpression ? undefined : schema}
                  />
                );
              })}
              {highlightAll && (
                <Box
                  position="absolute"
                  className="variable-field__mock-highlight"
                  width="100"
                  height="100"
                  onClick={() => {
                    setHighlightAll(false);
                  }}
                />
              )}
              <Inline>
                {!isCorrectType && (
                  <Box padding="025" className="variable-field__incorrect-type">
                    <Tooltip content={errorMessage} position="top-right">
                      <Icon
                        name="warning"
                        fillStyle="filled"
                        color="traffic-warning"
                        size="m"
                      />
                    </Tooltip>
                  </Box>
                )}
              </Inline>
            </Inline>
          </Inline>

          {((tempValue ?? "")?.length > 1 ||
            (tempValue?.[0]?.value ?? "")?.length > 0) && (
            <IconButton
              className="variable-field__clear-button"
              name="clear"
              onClick={() => handleClear()}
              color="text-secondary"
              size="s"
              skipFocus
            />
          )}
          {schemaTypeIconDisplay}
        </Inline>
      </Label>
      {/* TODO: suggestions for user when typing */}
      {/* {isFocused && (
        <FloatingWithParent
          className="variable-field__suggestions"
          parentRef={variableFieldRef}
        >
          <Card>suggestions</Card>
        </FloatingWithParent>
      )} */}
    </Box>
  );
};
