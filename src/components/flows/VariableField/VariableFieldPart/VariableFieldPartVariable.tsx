import React, {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  FocusEvent<PERSON><PERSON>ler,
  KeyboardEventHandler,
  use<PERSON><PERSON>back,
  useEffect,
  useMemo
} from "react";

import {
  Box,
  Inline,
  getClassNames,
  returnStringIfTrue
} from "@oneteam/onetheme";

import { QuestionType } from "@components/forms/QuestionType/QuestionType";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";

import { ExtendedQuestionTypes } from "@src/types/Question";

import {
  HandleFocusOnPartFocusLocation,
  VariableSchema
} from "../VariableFieldTypes";
import {
  getMatchingVariable,
  getQuestionTypeDetail
} from "../variableFieldHelpers";
import "./VariableFieldPart.scss";

export const VariableFieldPartVariable = ({
  id,
  value: rawValue,
  // variable,
  onChange,
  onBlur,
  onKeyDown,
  handleFocusOnThisPart,
  onFocus,
  schema
}: {
  id: string;
  value: string;
  // variable?: VariableTypeDefinition;
  onChange?: ChangeEventHandler<HTMLTextAreaElement>;
  onBlur?: FocusEventHandler;
  onKeyDown?: KeyboardEventHandler<HTMLTextAreaElement>;
  handleFocusOnThisPart?: (
    focusLocation: HandleFocusOnPartFocusLocation
  ) => void;
  onFocus?: FocusEventHandler<HTMLTextAreaElement>;
  schema?: VariableSchema;
}) => {
  // Show the user the user-friendly name of the variable
  const { variablesByName, variablesByPath, settings } =
    useConfigurationFlowContext();

  const variable = useMemo(
    () => variablesByName?.[rawValue] ?? variablesByPath?.[rawValue],
    [rawValue, variablesByName, variablesByPath]
  );

  const isValid = useMemo(() => {
    if (variable) {
      return true;
    }

    // Check if there is a next closed variable in the path
    const variableParts = rawValue.split(".");
    if (variableParts.length <= 1) {
      return false;
    }
    // Work backwards removing the last part until we find a variable
    let foundVariable;
    for (let i = variableParts.length - 1; i > 0 && !foundVariable; i--) {
      const variablePath = variableParts.slice(0, i).join(".");
      foundVariable =
        variablesByName?.[variablePath] ?? variablesByPath?.[variablePath];
    }

    // If the next closest variable is JSON and the json has no items (a.k.a no no structure)
    if (
      foundVariable &&
      foundVariable?.__type === "json" &&
      !foundVariable?.__configuration?.properties?.items
    ) {
      return true;
    }

    return false;
  }, [rawValue, variable, variablesByName, variablesByPath]);

  const value = useMemo(() => {
    if (settings?.debugMode || !variable?.__name) {
      return rawValue;
    }
    return String(variable.__name);
  }, [rawValue, settings?.debugMode, variable?.__name]);

  const variableIconRef = React.useRef<HTMLDivElement>(null);
  const inputRef = React.useRef<HTMLTextAreaElement>(null);

  const updateHeight = useCallback(() => {
    if (inputRef.current) {
      inputRef.current.style.overflow = "auto";
      inputRef.current.style.height = "inherit";
      const newHeight = `${inputRef.current?.scrollHeight}px`;
      inputRef.current.style.height = newHeight;
      inputRef.current.style.maxHeight = newHeight;
      inputRef.current.style.overflow = "hidden";
    }
  }, []);

  useEffect(() => {
    updateHeight();
  }, [updateHeight, value]);

  const variableIcon = useMemo(() => {
    const defaultIcon = (
      <QuestionType
        iconOnly
        type={variable?.__type ?? "unknown"}
        size="small"
      />
    );

    const { type: schemaType } = getQuestionTypeDetail(schema?.type);
    if (
      !(Object.values(ExtendedQuestionTypes) as string[]).includes(schemaType)
    ) {
      return defaultIcon;
    }
    const variableParts = rawValue.split(".");
    if (variableParts.length <= 1) {
      return defaultIcon;
    }
    // Use the first part of the variable as the parent variable
    const { matchingVariable, pathToMatchingVariable } = getMatchingVariable({
      value: rawValue,
      variablesByName,
      variablesByPath
    });

    if (
      !matchingVariable?.__type ||
      rawValue !== `${pathToMatchingVariable}.id`
    ) {
      return defaultIcon;
    }

    return (
      <QuestionType iconOnly type={matchingVariable.__type} size="small" />
    );
  }, [
    rawValue,
    schema?.type,
    variable?.__type,
    variablesByName,
    variablesByPath
  ]);

  return (
    <Box
      className={getClassNames([
        "variable-field-part",
        "variable-field-part-variable",
        returnStringIfTrue(!isValid, "variable-field-part-variable--invalid")
      ])}
      alignment="left"
    >
      <Inline alignment="left" gap="025">
        <Inline
          ref={variableIconRef}
          onClick={() => {
            handleFocusOnThisPart?.("all");
          }}
          style={{
            cursor: "default"
          }}
        >
          {variableIcon}
        </Inline>
        <textarea
          id={id}
          ref={inputRef}
          rows={1}
          value={value}
          onChange={
            onChange
              ? (e: ChangeEvent<HTMLTextAreaElement>) => {
                  // Take out characters that are not allowed in variable names
                  const value = e.target.value.replace(/[^a-zA-Z0-9_.*-]/g, "");
                  e.target.value = value;
                  onChange?.(e);
                }
              : undefined
          }
          disabled={!onChange}
          onBlur={onBlur}
          onDrag={e => {
            e.stopPropagation();
            e.preventDefault();
          }}
          onKeyDown={onKeyDown}
          onFocus={onFocus}
        />
      </Inline>
    </Box>
  );
};
