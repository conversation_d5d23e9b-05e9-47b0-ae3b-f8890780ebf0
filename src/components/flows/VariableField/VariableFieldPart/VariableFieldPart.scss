@mixin variable-text-area-styling() {
  field-sizing: content;
  outline: none;
  border: none;
  margin: 0px;
  padding: 0px;
  word-break: break-word;
  line-break: anywhere;
  max-width: 100%;
  resize: none;
  background-color: transparent;
  color: inherit;
  font-weight: inherit;
  font-family:
    var(--font-family-body, "Noto Sans"), Inter, system-ui, Avenir, Helvetica,
    Arial, sans-serif;
  line-height: calc(var(--font-size-body-m, 16px) * 1.3);
}

.variable-field-part {
  &-text {
    @include variable-text-area-styling();
    min-height: var(--components-inputs-height, 32px);
    max-height: 300px;
  }
  &-variable {
    background: var(
      --components-pill-prominent-colored-color-background,
      #ecf2f6
    );
    color: var(--components-pill-prominent-colored-color-text, #021429);
    border: 1px solid
      var(--components-pill-prominent-colored-color-border, #ecf2f6);

    font-weight: var(--font-weight-semi-bold, 600);
    max-width: 100%;
    position: relative;
    padding: 0 var(--spacing-025);
    border-radius: var(--spacing-075);
    overflow: visible;

    &:focus-within {
      border: 1px solid
        var(--components-pill-prominent-colored-color-text, #021429);
    }

    &--invalid {
      background: var(
        --components-pill-prominent-danger-color-background,
        #fde8e8
      );
      color: var(--components-pill-prominent-danger-color-text, #d0021b);
      border: 1px solid
        var(--components-pill-prominent-danger-color-border, #fde8e8);

      &:focus-within {
        border: 1px solid
          var(--components-pill-prominent-danger-color-text, #d0021b);
      }
    }

    &--warning {
      background: var(
        --components-pill-prominent-warning-color-background,
        #fde8e8
      );
      color: var(--components-pill-prominent-warning-color-text, #d0021b);
      border: 1px solid
        var(--components-pill-prominent-warning-color-border, #fde8e8);

      &:focus-within {
        border: 1px solid
          var(--components-pill-prominent-warning-color-text, #d0021b);
      }
    }

    & textarea {
      @include variable-text-area-styling();
      min-height: var(--font-size-body-m, 16px);
      overflow: hidden;
      min-width: 1px;
    }
  }
}
