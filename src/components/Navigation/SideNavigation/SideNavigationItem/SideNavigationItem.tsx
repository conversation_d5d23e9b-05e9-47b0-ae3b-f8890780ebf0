import React, {
  <PERSON><PERSON><PERSON><PERSON>,
  LegacyRef,
  PropsWithChildren,
  SyntheticEvent,
  forwardRef,
  useCallback
} from "react";

import { Box } from "../../../../fermions/index.ts";
import {
  getClassNames,
  returnStringIfTrue
} from "../../../../helpers/componentHelpers.ts";
import { Icon } from "../../../Icon/Icon.tsx";
import { IconType } from "../../../Icon/IconTypes.ts";
import "./SideNavigationItem.scss";

interface SideNavigationItemProps {
  icon?: IconType;
  href?: string;
  onClick?: EventHandler<SyntheticEvent>;
  isActive?: boolean;
  isHighlight?: boolean;
  rightElement?: React.ReactNode;
}

export const SideNavigationItem = forwardRef(
  (
    {
      icon,
      href,
      onClick,
      isActive,
      isHighlight,
      rightElement = <></>,
      children
    }: PropsWithChildren<SideNavigationItemProps>,
    ref: LegacyRef<HTMLLIElement>
  ) => {
    const handleOnClick = useCallback(
      (e: SyntheticEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (onClick) {
          onClick?.(e);
        } else if (href) {
          window.location.pathname = href;
        }
      },
      [href, onClick]
    );
    return (
      <li
        ref={ref}
        className={getClassNames([
          "side-navigation-item",
          returnStringIfTrue(isActive, "side-navigation-item--selected"),
          returnStringIfTrue(isHighlight, "side-navigation-item--highlight")
        ])}
      >
        <div className="side-navigation-item__background">
          <div className="side-navigation-item__background__content" />
        </div>
        <button
          className="side-navigation-item__anchor"
          onClick={handleOnClick}
          onMouseDown={e => e.preventDefault()}
          type="button"
        >
          {icon && <Icon {...icon} />}
          <Box className="side-navigation-item__text">{children}</Box>
          {rightElement}
        </button>
      </li>
    );
  }
);
