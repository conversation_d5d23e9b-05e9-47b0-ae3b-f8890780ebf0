import React, { useCallback, useEffect } from "react";

import { Box, FermionProps, Inline, Stack } from "../../fermions/index.ts";
import {
  CommonInputProps,
  returnStringIfTrue
} from "../../helpers/componentHelpers.ts";
import { Label } from "../Label/Label.tsx";
import { LabelTextPosition } from "../Label/LabelTypes.ts";
import "./TableField.scss";
import { useTableFieldContext } from "./TableFieldContext.ts";
import { TableFieldHeader } from "./TableFieldHeader.tsx";
import { TableFieldProvider } from "./TableFieldProvider.tsx";
import { TableFieldRow } from "./TableFieldRow.tsx";
import {
  TableFieldColumnId,
  TableFieldColumnWidths,
  TableFieldColumns,
  TableFieldOnChangeColumnWidth,
  TableFieldRemoveRow,
  TableFieldReorderRow,
  TableFieldSetCell,
  TableFieldSetColumn,
  TableFieldSetRow,
  TableFieldSetTable,
  TableFieldTranslations,
  TableFieldValue,
  TableFieldVariant
} from "./TableFieldTypes.ts";
import { useTableFieldCellHandlers } from "./hooks/useTableFieldCellHandlers.ts";
import { useTableFieldCopyHandler } from "./hooks/useTableFieldCopyHandler.ts";
import { useTableFieldHighlightHandler } from "./hooks/useTableFieldHighlightHandler.ts";
import { useTableFieldRowActions } from "./hooks/useTableFieldRowActions.tsx";
import { useTableFieldStyling } from "./hooks/useTableFieldStyling.ts";

// Upcoming enhancements:
// - Don't render a column when it is out of view
// - Add a prop to set the column width and column width controller

// TODO:
// - Per cell error handling
export interface TableFieldProps extends Omit<CommonInputProps, "autoFocus"> {
  id: string;
  columns: TableFieldColumns;
  columnWidths?: TableFieldColumnWidths;
  onChangeColumnWidth?: TableFieldOnChangeColumnWidth;
  value?: TableFieldValue;
  setTable?: TableFieldSetTable;
  setRow?: TableFieldSetRow;
  removeRow?: TableFieldRemoveRow;
  reorderRow?: TableFieldReorderRow;
  setColumn?: TableFieldSetColumn;
  setCell?: TableFieldSetCell;
  variant?: `${TableFieldVariant}`;

  stickyColumnId?: TableFieldColumnId;
  onChangeStickyColumnId?: (columnId: TableFieldColumnId) => void;

  showRowCounter?: boolean;

  maxHeight?: string;
  onlyRenderVisibleRows?: boolean;
  width?: FermionProps["width"];
  className?: string;
  style?: React.CSSProperties;

  onlyTriggerChangeWhenBlurCell?: boolean;
  showMockRow?: boolean;
  disableAddRow?: boolean;
  disableAddRowExceptEnd?: boolean;
  hideHeader?: boolean;

  translations?: TableFieldTranslations;
}

export const TableField = ({
  id,
  columns: providedColumns = [],
  disabled,
  stickyColumnId,
  translations = {
    addRowAbove: "Add row above",
    addRowBelow: "Add row below",
    moveUp: "Move up",
    moveDown: "Move down",
    clearCells: "Clear cells",
    deleteRow: "Delete row",
    booleanField: {
      trueLabel: "Yes",
      falseLabel: "No"
    }
  },
  ...props
}: TableFieldProps) => {
  return (
    <TableFieldProvider
      id={id}
      columns={providedColumns}
      disabled={disabled}
      stickyColumnId={stickyColumnId}
      translations={translations}
    >
      <TableFieldContent {...props} />
    </TableFieldProvider>
  );
};

interface TableFieldContentProps
  extends Omit<TableFieldProps, "columns" | "id" | "disabled"> {}

export const TableFieldContent = ({
  value = [],
  setTable,
  setRow,
  removeRow,
  reorderRow,
  setColumn,
  setCell,
  columnWidths,
  onChangeColumnWidth,
  label,
  required,
  hidden,
  error,
  description,
  tooltip,
  variant = TableFieldVariant.DEFAULT,
  onlyTriggerChangeWhenBlurCell = true,
  onChangeStickyColumnId,
  showMockRow = true,
  disableAddRow = false,
  disableAddRowExceptEnd = false,
  hideHeader = false,
  width = "100",
  maxHeight,
  onlyRenderVisibleRows = !!maxHeight,
  className,
  style
}: TableFieldContentProps) => {
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);
  const tableRef = React.useRef<HTMLTableElement>(null);

  const { tableFieldId, columns, disabled } = useTableFieldContext();

  // Support cell highlighting
  const { setStartDragCell, handleDragOverCell } =
    useTableFieldHighlightHandler();

  // Support copy highlighted cells
  useTableFieldCopyHandler({ value });

  // Column width calculations
  const { tableMetadata, columnStyling, refreshTableStylingCalculations } =
    useTableFieldStyling({
      tableRef,
      value,
      columnWidths
    });

  const { handleCellOnPaste, handleCellOnKeyDown } = useTableFieldCellHandlers({
    setCell,
    setColumn,
    setRow,
    setTable,
    refreshTableStylingCalculations,
    value
  });

  const { addRow, renderRowActions, setKebabMenuOpenIndex, canAddRow } =
    useTableFieldRowActions({
      removeRow,
      setRow,
      reorderRow,
      disableAddRow,
      disableAddRowExceptEnd,
      value
    });

  const [rowIndexesToRender, setRowIndexesToRender] = React.useState<
    | {
        from: number;
        to: number;
      }
    | undefined
  >(undefined);

  const getRowIndexesToRender = useCallback(() => {
    // Find rows that are in view in the current scroll container ref
    if (!onlyRenderVisibleRows || !tableRef.current) {
      setRowIndexesToRender(undefined);
      return;
    }

    // Find the first row in view and the last row in view (can use the tableMetadata row heights)
    const table = tableRef.current;
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) {
      setRowIndexesToRender(undefined);
      return;
    }

    const tableRect = table.getBoundingClientRect();
    const scrollContainerRect = scrollContainer.getBoundingClientRect();

    const scrollContainerTop = scrollContainerRect.top - tableRect.top;
    const scrollContainerBottom = scrollContainerRect.bottom - tableRect.top;

    // Using the row heights to find the first and last row in view
    let firstRowInView = undefined;
    let lastRowInView = value.length - 1; // Default to last row
    let currentRowTop = 0;
    let currentRowBottom = 0;

    // Check if we're at the bottom of the scroll
    const scrollTop = scrollContainer.scrollTop;
    const scrollHeight = scrollContainer.scrollHeight;
    const clientHeight = scrollContainer.clientHeight;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1; // -1 for rounding errors

    console.log("Scroll info:", {
      scrollContainerTop,
      scrollContainerBottom,
      scrollTop,
      scrollHeight,
      clientHeight,
      isAtBottom
    });

    for (let i = 0; i < value.length; i++) {
      const rowHeight =
        tableMetadata.rowHeights[i + 1] || tableMetadata.rowHeights[i] || 0;
      currentRowTop = currentRowBottom;
      currentRowBottom = currentRowTop + rowHeight;

      // Find first row that's visible (even partially)
      if (
        firstRowInView === undefined &&
        currentRowBottom > scrollContainerTop
      ) {
        firstRowInView = i;
      }

      // Find last row that's visible (even partially)
      if (currentRowTop < scrollContainerBottom) {
        lastRowInView = i;
      }
    }

    // If we're at the bottom, make sure we render all remaining rows
    if (isAtBottom) {
      lastRowInView = value.length - 1;
      console.log("At bottom - forcing lastRowInView to:", lastRowInView);
    }

    // Ensure we have valid values
    if (firstRowInView === undefined) {
      firstRowInView = 0;
    }

    console.log(
      "firstRowInView",
      firstRowInView,
      "lastRowInView",
      lastRowInView
    );

    // Set the rows to render with buffer
    const bufferSize = 5;
    const fromIndex = Math.max(0, firstRowInView - bufferSize);
    const toIndex = Math.min(value.length - 1, lastRowInView + bufferSize);

    setRowIndexesToRender({
      from: fromIndex,
      to: toIndex + 1 // +1 because 'to' is exclusive in the rendering logic
    });
    return;
  }, [onlyRenderVisibleRows, tableMetadata.rowHeights, value.length]);

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!onlyRenderVisibleRows || !scrollContainer) {
      return;
    }

    scrollContainer.addEventListener("scroll", getRowIndexesToRender);

    return () => {
      scrollContainer.removeEventListener("scroll", getRowIndexesToRender);
    };
  }, [onlyRenderVisibleRows, getRowIndexesToRender]);

  return (
    <Stack
      id={tableFieldId}
      classNames={[
        className,
        "table-field",
        `table-field--variant-${variant}`,
        returnStringIfTrue(disabled, "table-field--disabled")
      ]}
      width={width}
      style={style}
      gap="025"
      position="relative"
    >
      <Label
        label={label}
        required={required}
        disabled={disabled}
        hidden={hidden}
        textPosition={LabelTextPosition.TOP}
        description={description}
        tooltip={tooltip}
        error={error}
      />
      <Box position="relative">
        <Box
          ref={scrollContainerRef}
          className="table-field__scroll-container"
          overflow="auto"
          style={{
            maxHeight
          }}
        >
          <Inline>
            <table
              ref={tableRef}
              className="table-field__table"
              id={`${tableFieldId}-table`}
            >
              {!hideHeader && (
                <thead>
                  <TableFieldHeader
                    onChangeStickyColumnId={onChangeStickyColumnId}
                    columnStyling={columnStyling}
                    onChangeColumnWidth={onChangeColumnWidth}
                  />
                </thead>
              )}
              <tbody>
                {value.map((row, rowIndex) => (
                  <TableFieldRow
                    key={rowIndex}
                    rowValues={row}
                    rowIndex={rowIndex}
                    onChange={(cellValue, columnId) => {
                      setCell?.(cellValue, rowIndex, columnId);
                    }}
                    onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlurCell}
                    handleOnKeyDown={handleCellOnKeyDown}
                    onPaste={handleCellOnPaste}
                    columnStyling={columnStyling}
                    rowActions={
                      disabled ? undefined : renderRowActions(rowIndex)
                    }
                    setStartDragCell={setStartDragCell}
                    handleDragOverCell={handleDragOverCell}
                    shouldRenderRow={
                      rowIndexesToRender === undefined ||
                      (rowIndexesToRender &&
                        rowIndex >= rowIndexesToRender.from &&
                        rowIndex <= rowIndexesToRender.to)
                    }
                  />
                ))}
                {/* Mock row so user always has 1 extra */}
                {showMockRow && canAddRow && (
                  <TableFieldRow
                    rowIndex={value.length}
                    onChange={(cellValue, columnId) => {
                      if (cellValue === undefined) {
                        return;
                      }
                      addRow(
                        {
                          [columnId]: cellValue
                        },
                        value.length,
                        columns.findIndex(column => column.id === columnId)
                      );
                      setKebabMenuOpenIndex(undefined);
                    }}
                    onlyTriggerChangeWhenBlur={false}
                    handleOnKeyDown={handleCellOnKeyDown}
                    onPaste={handleCellOnPaste}
                    columnStyling={columnStyling}
                    rowActions={
                      disabled ? undefined : renderRowActions(value.length)
                    }
                    isMockRow
                    setStartDragCell={setStartDragCell}
                    handleDragOverCell={handleDragOverCell}
                    shouldRenderRow={
                      rowIndexesToRender === undefined ||
                      (rowIndexesToRender &&
                        value.length + 3 >= rowIndexesToRender.from &&
                        value.length - 3 <= rowIndexesToRender.to)
                    }
                  />
                )}
              </tbody>
            </table>
          </Inline>
        </Box>
      </Box>
    </Stack>
  );
};

TableField.displayName = "TableField";
