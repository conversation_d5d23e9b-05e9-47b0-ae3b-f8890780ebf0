@use "../../tokens/tokens" as *;

@mixin text_size($size) {
  font-size: var(--font-size-body-#{$size}, 18px);
}

.text {
  line-height: 1.25;
  height: fit-content;
  @include color_all();
  &--size {
    &-m {
      @include text_size("m");
    }
    &-xs {
      @include text_size("xs");
    }
    &-s {
      @include text_size("s");
    }
    &-l {
      @include text_size("l");
    }
  }
  &--weight {
    &-regular {
      font-weight: var(--font-weight-regular);
    }
    &-medium {
      font-weight: var(--font-weight-medium);
    }
    &-semi-bold {
      font-weight: var(--font-weight-semi-bold);
    }
    &-bold {
      font-weight: var(--font-weight-bold);
    }
  }
  &--alignment {
    &-left {
      text-align: left;
    }
    &-center {
      text-align: center;
    }
    &-right {
      text-align: right;
    }
  }

  & span {
    &:not(.text) {
      font-size: inherit;
    }
  }
}
