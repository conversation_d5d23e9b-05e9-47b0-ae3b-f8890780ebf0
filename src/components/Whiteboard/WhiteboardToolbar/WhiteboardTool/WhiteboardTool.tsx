import React from "react";

import {
  getClassNames,
  returnStringIfTrue
} from "../../../../helpers/componentHelpers.ts";
import { Icon } from "../../../Icon/Icon.tsx";
import { IconFillStyle, IconSize, IconType } from "../../../Icon/IconTypes.ts";
import { Text } from "../../../Text/Text.tsx";
import { FontWeight, TextSize } from "../../../Text/TextTypes.ts";
import "./WhiteboardTool.scss";
import { WhiteboardToolVariant } from "./WhiteboardToolTypes.ts";

interface WhiteboardToolProps {
  icon?: IconType; // if WhiteboardToolVariant.DEFAULT or WhiteboardToolVariant.ROUNDED
  text?: string; // if WhiteboardToolVariant.TEXT
  variant?: `${WhiteboardToolVariant}`;
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
  isSelected?: boolean;
  isHighlighted?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const WhiteboardTool = ({
  icon,
  text = "",
  variant = WhiteboardToolVariant.DEFAULT,
  onClick,
  isSelected,
  isHighlighted,
  className = "",
  style
}: WhiteboardToolProps) => {
  return (
    <button
      onClick={onClick}
      className={getClassNames([
        "whiteboard-tool",
        `whiteboard-tool--variant-${variant}`,
        returnStringIfTrue(isHighlighted, "whiteboard-tool--highlighted"),
        returnStringIfTrue(isSelected, "whiteboard-tool--selected"),
        className
      ])}
      style={style}
      onMouseDown={e => e.preventDefault()}
      type="button"
    >
      {variant === WhiteboardToolVariant.TEXT || !icon ? (
        <Text size={TextSize.S} weight={FontWeight.MEDIUM}>
          {text}
        </Text>
      ) : (
        <Icon
          {...icon}
          fillStyle={isSelected ? IconFillStyle.FILLED : icon.fillStyle}
          size={IconSize.LARGE}
          className="whiteboard-tool__icon"
        />
      )}
    </button>
  );
};
