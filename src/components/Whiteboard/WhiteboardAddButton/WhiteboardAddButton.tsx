import React, { useCallback } from "react";

import {
  getClassNames,
  returnStringIfTrue
} from "../../../helpers/componentHelpers.ts";
import {
  CustomDropdownMenuTrigger,
  DropdownMenu
} from "../../DropdownMenu/DropdownMenu.tsx";
import { FloatingWithParentPosition } from "../../FloatingWithParent/FloatingWithParentTypes.ts";
import { Icon } from "../../Icon/Icon.tsx";
import "./WhiteboardAddButton.scss";

interface WhiteboardAddButtonProps {
  onClick?: () => void;
  isHighlighted?: boolean;
  isOpen?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  className?: string;
  dropdownMenuContent?: React.ReactNode;
  style?: React.CSSProperties;
}

export const WhiteboardAddButton = ({
  onClick,
  isHighlighted,
  isOpen = false,
  onOpenChange,
  className = "",
  dropdownMenuContent,
  style
}: WhiteboardAddButtonProps) => {
  const addButton: CustomDropdownMenuTrigger = useCallback(
    ({ isOpen: isAddButtonOpen, onClick: addButtonClick }) => (
      <button
        className={getClassNames([
          "whiteboard-add-button",
          returnStringIfTrue(isAddButtonOpen, "whiteboard-add-button--open"),
          returnStringIfTrue(
            isHighlighted,
            "whiteboard-add-button--highlighted"
          ),
          className
        ])}
        style={style}
        onClick={e => {
          e.preventDefault();
          e.stopPropagation();
          addButtonClick?.(e);
        }}
        onMouseDown={e => e.preventDefault()}
        type="button"
      >
        <Icon name="add" className="whiteboard-add-button__icon" />
      </button>
    ),
    [isHighlighted, className, style]
  );

  return (
    <DropdownMenu
      isOpen={isOpen}
      onOpenChange={isOpen => {
        onClick?.();
        onOpenChange?.(isOpen);
      }}
      trigger={addButton}
      className="whiteboard-add-button__dropdown-menu"
      position={FloatingWithParentPosition.RIGHT}
    >
      {dropdownMenuContent}
    </DropdownMenu>
  );
};
