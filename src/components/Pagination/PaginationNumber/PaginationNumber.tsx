import React from "react";

import { Box } from "../../../fermions/index.ts";
import { returnStringIfTrue } from "../../../helpers/componentHelpers.ts";
import "./PaginationNumber.scss";

interface PaginationNumberProps {
  page: number;
  onClick: () => void;
  isActive?: boolean;
}

export const PaginationNumber = ({
  page,
  onClick,
  isActive = false
}: PaginationNumberProps) => {
  return (
    <Box
      classNames={[
        "pagination-number",
        returnStringIfTrue(isActive, "pagination-number--active")
      ]}
    >
      <button
        className="pagination-number__button"
        onClick={onClick}
        onMouseDown={e => {
          e.preventDefault();
        }}
        onKeyDown={e => {
          if (e.key === "Enter") {
            e.stopPropagation();
            e.preventDefault();
            onClick();
          }
        }}
        type="button"
      >
        {page}
      </button>
    </Box>
  );
};
