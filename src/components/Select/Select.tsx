import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import useOnClickOutside from "use-onclickoutside";

import { Box, Inline } from "../../fermions/index.ts";
import { returnStringIfTrue } from "../../helpers/componentHelpers.ts";
import { useWarningCheckForInputs } from "../../hooks/useWarningCheckForInputs.tsx";
import { ColorText } from "../../tokens/IonsInterface.ts";
import { CloseIconButton } from "../Button/IconButton/CloseIconButton/CloseIconButton.tsx";
import { FloatingWithParent } from "../FloatingWithParent/FloatingWithParent.tsx";
import { IconSize } from "../Icon/IconTypes.ts";
import { OpenCloseIcon } from "../Icon/index.ts";
import { Label } from "../Label/Label.tsx";
import { LabelTextPosition } from "../Label/LabelTypes.ts";
import { Text } from "../Text/Text.tsx";
import "./Select.scss";
import { SelectOptionProps } from "./SelectOption/SelectOption.tsx";
import { SelectOptions } from "./SelectOptions/SelectOptions.tsx";
import {
  CommonSelectProps,
  SelectOptionType,
  SelectValue,
  noOptionsLabelDefault
} from "./SelectTypes.ts";
import { useSelect } from "./useSelect.tsx";

export type SelectRenderElement = ({
  selectedOption
}: {
  selectedOption?: SelectOptionType;
}) => React.ReactNode;

export interface SelectProps
  extends Omit<
    CommonSelectProps,
    "renderLeftElement" | "renderRightElement" | "isMultiSelect"
  > {
  renderLeftElement?: SelectRenderElement;
  renderRightElement?: SelectRenderElement;
  renderOptionLeftElement?: SelectOptionProps["renderLeftElement"];
  renderOptionRightElement?: SelectOptionProps["renderRightElement"];
}

export const Select = ({
  id,
  value,
  options = [],
  onChange: handleChange,
  label = "",
  required,
  placeholder = "",
  description,
  tooltip,
  noOptionsLabel = noOptionsLabelDefault,
  width,
  disabled,
  withClear = true,
  customSelector,
  className = "",
  position,
  error,
  labelTextPosition = LabelTextPosition.TOP,
  renderLeftElement,
  renderRightElement,
  renderOptionLeftElement,
  renderOptionRightElement,
  handleSearchExternally,
  onChangeSearch,
  clearIfOptionMissing = true,
  valueSequenceId,

  controlFocus,
  autoFocus,
  autoOpenOnFocus = true,

  // since select is closed every time a selection is made, it's always change on blur
  // onlyTriggerChangeWhenBlur = false,
  onClick,
  onFocus,
  onBlur,
  onKeyDown,
  onKeyUp,
  onPaste,
  ...props
}: SelectProps) => {
  useWarningCheckForInputs({
    value: value as string,
    defaultValue: props.defaultValue as string,
    onChange: handleChange
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLUListElement>(null);
  const selectRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLInputElement>(null);

  const [searchValue, setSearchValue] = useState<string>("");
  const [tempValue, setTempValue] = useState(
    value !== undefined ? value : props.defaultValue
  );

  useEffect(() => {
    if (props.isLoading) {
      return;
    }
    setTempValue(value !== undefined ? value : props.defaultValue);
  }, [props.defaultValue, props.isLoading, value, valueSequenceId]);

  useOnClickOutside(containerRef, () => {
    if (isOpen) {
      handleChangeIsOpen(false);
    }
  });
  const {
    highlightedOptionIndex,
    setHighlightedOptionIndex,
    filteredOptions,
    handleChangeIsOpen,
    searchInput,
    isOpen
  } = useSelect({
    value: tempValue,
    options,
    searchRef,
    handleClickItem: handleChange,
    handleValueChange: setTempValue,
    searchValue,
    setSearchValue,
    scrollContainerRef,
    disabled,
    handleSearchExternally,
    onChangeSearch,

    controlFocus,
    autoFocus,
    autoOpenOnFocus,

    onClick,
    onFocus,
    onBlur,
    onKeyDown,
    onKeyUp,
    onPaste
  });

  const handleChangeInternally = useCallback(
    (updatedValue: SelectValue | undefined = undefined) => {
      if (updatedValue !== value) {
        handleChange?.(updatedValue);
      }
      setTempValue(updatedValue === undefined ? "" : updatedValue);
      handleChangeIsOpen(false);
    },
    [handleChange, handleChangeIsOpen, value]
  );

  useEffect(() => {
    if (props.isLoading || !tempValue) {
      // if isLoading=true, options are still being fetched (for dynamicOptions from api)
      // so don't check if the value exists in the options
      return;
    }

    if (
      typeof tempValue === "object" ||
      !clearIfOptionMissing ||
      handleSearchExternally
    ) {
      return;
    }

    // if the value doesn't exist in the option, remove it and notify others cos its actually a change
    // We only emit on changes because we don't want to emit on every render and cause an infinite loop
    const optionValueSet = new Set(options.map(option => option.value));
    const hasMissingOption = !optionValueSet.has(tempValue);
    if (hasMissingOption) {
      handleChangeInternally();
    }
  }, [
    options,
    handleChangeInternally,
    tempValue,
    props.isLoading,
    handleSearchExternally,
    clearIfOptionMissing
  ]);

  const selectedOption = useMemo(
    () => options?.find(option => option.value === tempValue),
    [options, tempValue]
  );

  useOnClickOutside(containerRef, () => {
    if (isOpen) {
      handleChangeIsOpen(undefined);
    }
  });

  const selector = useMemo(() => {
    if (customSelector) {
      return customSelector({
        handleChange: handleChangeInternally,
        handleChangeIsOpen,
        isOpen,
        selectRef,
        value: tempValue,
        placeholder,
        searchInput,
        searchValue,
        selectedOption
      });
    }
    return (
      <Inline
        ref={selectRef}
        alignment="left"
        classNames={[
          "select",
          returnStringIfTrue(disabled, "select--disabled")
        ]}
        onClick={e => {
          e.stopPropagation();
          e.preventDefault();
          handleChangeIsOpen(true);
        }}
        width={width}
        gap="050"
      >
        {renderLeftElement?.({
          selectedOption
        })}
        <Inline
          width="100"
          alignment="left"
          className="select__input-container"
        >
          {searchInput}
          <Text
            className="select__value--absolute"
            maxLines={1}
            truncate={true}
            style={
              (!isOpen || !searchValue.length) && tempValue
                ? { opacity: 1 }
                : { opacity: 0 }
            }
          >
            {String(
              selectedOption?.label ?? selectedOption?.value ?? value ?? " "
            )}
          </Text>
          {!searchValue?.length && !tempValue && (
            <Text className="select__placeholder" color={ColorText.TERTIARY}>
              {String(placeholder)}
            </Text>
          )}
        </Inline>
        <Inline className="select__right" gap="025" alignment="right">
          {withClear && !disabled && tempValue && !props.isLoading && (
            <CloseIconButton
              className="select__clear"
              onClick={e => {
                handleChangeInternally();
                searchRef.current?.blur();
                e.stopPropagation();
              }}
              skipFocus
              color={ColorText.PRIMARY}
              size={IconSize.SMALL}
            />
          )}
        </Inline>
        {renderRightElement?.({
          selectedOption
        })}
        <OpenCloseIcon
          className="select__icon"
          isOpen={isOpen}
          disabled={disabled}
        />
      </Inline>
    );
  }, [
    customSelector,
    disabled,
    width,
    renderLeftElement,
    selectedOption,
    searchInput,
    isOpen,
    searchValue,
    tempValue,
    value,
    placeholder,
    withClear,
    props.isLoading,
    renderRightElement,
    handleChangeInternally,
    handleChangeIsOpen
  ]);

  return (
    <Box
      ref={containerRef}
      position="relative"
      classNames={[className, "select-input"]}
      width={width}
    >
      <Label
        htmlFor={id}
        label={label}
        description={description}
        tooltip={tooltip}
        error={error}
        required={required}
        textPosition={labelTextPosition}
        disabled={disabled}
      >
        {selector}
      </Label>
      {isOpen && (
        <FloatingWithParent
          parentRef={selectRef}
          onOpenChange={handleChangeIsOpen}
          minWidthAsParent
          position={position}
        >
          <SelectOptions
            {...props}
            id={id}
            options={filteredOptions}
            value={tempValue}
            onChange={handleChangeInternally}
            highlightedOptionIndex={highlightedOptionIndex}
            setHighlightedOptionIndex={setHighlightedOptionIndex}
            scrollContainerRef={scrollContainerRef}
            isMultiSelect={false}
            noOptionsLabel={noOptionsLabel}
            renderLeftElement={renderOptionLeftElement}
            renderRightElement={renderOptionRightElement}
          />
        </FloatingWithParent>
      )}
    </Box>
  );
};
