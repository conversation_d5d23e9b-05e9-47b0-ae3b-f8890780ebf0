import {
  <PERSON><PERSON><PERSON>,
  KeyboardEvent<PERSON>and<PERSON>,
  LegacyRef,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState
} from "react";
import React from "react";

import {
  MultiSelectValue,
  SelectOptionType,
  SelectValue,
  SingleSelectValue,
  selectAllValue
} from "./SelectTypes.ts";

function findHighlightedOptionIndex(
  value?: SelectValue,
  options: SelectOptionType[] = []
) {
  if (value === undefined || (typeof value !== "number" && !value?.length)) {
    return;
  }
  if (typeof value === "string" || typeof value === "number") {
    return options.findIndex(option => option.value === value);
  }
  return undefined;
}

export const useSelect = ({
  value,
  options,
  searchRef,
  isMultiSelect = false,
  withSelectAll = false,
  handleClickItem,
  handleValueChange,
  searchValue,
  setSearchValue,
  closeOnChange = true,
  scrollContainerRef,
  disabled,
  handleSearchExternally,

  autoFocus = false,
  controlFocus,
  autoOpenOnFocus = true,

  onChangeSearch,
  handleChange,
  onClick,
  onFocus,
  onBlur,
  onKeyDown,
  onKeyUp,
  onPaste
}: {
  value?: SelectValue;
  options: SelectOptionType[];
  searchRef: React.RefObject<HTMLInputElement>;
  isMultiSelect?: boolean;
  withSelectAll?: boolean;
  handleClickItem?: (newValue: SingleSelectValue) => void;
  handleValueChange?: (newValue: SingleSelectValue) => void;
  searchValue: string;
  setSearchValue: Dispatch<SetStateAction<string>>;
  closeOnChange?: boolean;
  autoFocus?: boolean;
  scrollContainerRef: LegacyRef<HTMLUListElement>;
  disabled?: boolean;

  handleSearchExternally?: boolean;
  onChangeSearch?: (value: string) => void;

  controlFocus?: boolean;
  autoOpenOnFocus?: boolean;

  // multiselect
  handleChange?: (newValue: MultiSelectValue) => void;

  onClick?: React.MouseEventHandler;
  onFocus?: React.FocusEventHandler;
  onBlur?: React.FocusEventHandler;
  onKeyDown?: React.KeyboardEventHandler;
  onKeyUp?: React.KeyboardEventHandler;
  onPaste?: React.ClipboardEventHandler;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(autoFocus ?? false);
  const [highlightedOptionIndex, setHighlightedOptionIndex] = useState<
    number | undefined
  >(findHighlightedOptionIndex(value, options));

  // For multi-select pills
  const [cursorIndex, setCursorIndex] = useState<number | undefined>(undefined);
  const [highlightedSelectedItems, setHighlightedSelectedItems] = useState<
    SingleSelectValue[]
  >([]);
  const [prevUserKey, setPrevUserKey] = useState<string>();
  const [prevValueLength, setPrevValueLength] = useState<number>(
    typeof value === "object" ? value?.length : 0
  );

  const resetHighlightedOptionIndex = useMemo(
    () => (isMultiSelect && withSelectAll ? -1 : 0),
    [isMultiSelect, withSelectAll]
  );

  useEffect(() => {
    onChangeSearch?.(searchValue);
  }, [onChangeSearch, searchValue]);

  useEffect(() => {
    if (controlFocus === true && document.activeElement !== searchRef.current) {
      searchRef.current?.focus();
    } else if (
      controlFocus === false &&
      document.activeElement === searchRef.current
    ) {
      searchRef.current?.blur();
    }
  }, [controlFocus, searchRef]);

  useEffect(() => {
    if (autoFocus) {
      setHighlightedOptionIndex(current =>
        current === undefined ? resetHighlightedOptionIndex : current
      );
    }
  }, [autoFocus, resetHighlightedOptionIndex]);

  const filteredOptions = useMemo(() => {
    if (!searchValue?.length || handleSearchExternally) {
      return options;
    }

    const searchValueKey = searchValue.toLowerCase();
    return options.filter(
      (option: SelectOptionType) =>
        option.label.toLowerCase().includes(searchValueKey) ||
        option.description?.toLowerCase().includes(searchValueKey)
    );
  }, [handleSearchExternally, options, searchValue]);

  useEffect(() => {
    if (isMultiSelect) {
      return;
    }

    try {
      if (
        scrollContainerRef &&
        typeof scrollContainerRef === "object" &&
        scrollContainerRef.current
      ) {
        const item = scrollContainerRef.current.querySelector?.(
          `li[data-value="${value}"]`
        );

        if (item) {
          const scrollContainerPosition =
            scrollContainerRef.current.getBoundingClientRect();
          const position = item.getBoundingClientRect();

          if (
            // If not everything is visible
            Math.round(scrollContainerPosition.height) <
            Math.round(scrollContainerRef.current.scrollHeight)
          ) {
            scrollContainerRef.current.scrollTo({
              top: Math.max(0, position.top - scrollContainerPosition.top - 20),
              left: Math.max(
                0,
                position.left - scrollContainerPosition.left - 20
              ),
              behavior: "instant"
            });
          }
          setHighlightedOptionIndex(findHighlightedOptionIndex(value, options));
        }
      }
    } catch (err) {
      console.log("Error scrolling to selected item", err);
    }
  }, [isMultiSelect, isOpen, options, scrollContainerRef, value]);

  useEffect(() => {
    if (!isOpen || !isMultiSelect || cursorIndex === undefined) {
      return;
    }

    if (
      searchValue?.length > 0 ||
      cursorIndex > ((value as MultiSelectValue)?.length ?? 0)
    ) {
      setCursorIndex(undefined);
      setHighlightedSelectedItems([]);
    }
  }, [cursorIndex, isMultiSelect, isOpen, searchValue, value]);

  useEffect(() => {
    if (!isMultiSelect || !highlightedSelectedItems.length) {
      return;
    }

    if (!isOpen) {
      setHighlightedSelectedItems([]);
    }
  }, [highlightedSelectedItems.length, isMultiSelect, isOpen]);

  const scrollToIndex = useCallback(
    (index: number) => {
      if (index === undefined) {
        return;
      }

      let value = filteredOptions[index]?.value;
      if (isMultiSelect && !value && withSelectAll && index === -1) {
        value = selectAllValue;
      }
      if (value && typeof scrollContainerRef === "object") {
        try {
          const item = scrollContainerRef?.current?.querySelector(
            `li[data-value="${value}"]`
          );
          if (item) {
            item.scrollIntoView({ behavior: "instant", block: "center" });
          }
        } catch (err) {
          console.log("Error scrolling to selected item", err);
        }
      }
    },
    [filteredOptions, isMultiSelect, scrollContainerRef, withSelectAll]
  );

  const handleChangeIsOpen = useCallback(
    (updatedIsOpen?: boolean) => {
      if (disabled) {
        return;
      }

      setIsOpen(current => {
        const updatedValue =
          updatedIsOpen !== undefined ? updatedIsOpen : !current;
        setHighlightedOptionIndex(resetHighlightedOptionIndex);
        setCursorIndex(undefined);
        if (updatedValue) {
          searchRef.current?.focus();
        } else {
          searchRef.current?.blur();
          setSearchValue("");
        }
        return updatedValue;
      });
    },
    [disabled, resetHighlightedOptionIndex, searchRef, setSearchValue]
  );

  useEffect(() => {
    if (typeof value === "object") {
      setHighlightedSelectedItems(current => {
        if (
          // If user selects more items, unhighlight all
          (value as MultiSelectValue)?.length > prevValueLength
        ) {
          setCursorIndex(value.length);
          return [];
        } else if (current.length === 1 && value.includes(current[0])) {
          return current;
        }
        // If value changes when all selections are highlighted or if the current item is not in values
        setCursorIndex(value?.length);
        return [];
      });
    } else if (value === undefined) {
      setHighlightedSelectedItems([]);
    }

    setPrevValueLength((value as MultiSelectValue)?.length ?? 0);
  }, [prevValueLength, value]);

  const handleOnKeyDown: KeyboardEventHandler<HTMLInputElement> = useCallback(
    e => {
      onKeyDown?.(e);

      if (disabled) {
        return;
      }

      if (["Tab", "Escape"].includes(e.key)) {
        handleChangeIsOpen(false);
      }

      if (e.key === "Enter") {
        if (highlightedOptionIndex === undefined) {
          handleChangeIsOpen();
          return;
        }

        if (isOpen) {
          if (
            isMultiSelect &&
            selectAllValue &&
            withSelectAll &&
            highlightedOptionIndex === -1
          ) {
            handleClickItem?.(selectAllValue);
            handleValueChange?.(selectAllValue);
            setSearchValue("");
          } else {
            const valueToClick = filteredOptions?.filter(
              option => !option.disabled
            )?.[highlightedOptionIndex]?.value;
            if (valueToClick) {
              handleClickItem?.(valueToClick);
              handleValueChange?.(valueToClick);
              setSearchValue("");
            }
          }
          e.preventDefault();
          e.stopPropagation();
          if (closeOnChange) {
            handleChangeIsOpen(false);
          }
        }
      }

      if (!isOpen) {
        return;
      }

      if (e.key === "ArrowDown") {
        setHighlightedOptionIndex(current => {
          let updated = resetHighlightedOptionIndex;
          if (current !== undefined && current >= resetHighlightedOptionIndex) {
            const nextIndex = current + 1;
            if (
              nextIndex <
              (filteredOptions?.length ?? resetHighlightedOptionIndex)
            ) {
              updated = nextIndex;
            }
          }

          scrollToIndex(updated);
          return updated;
        });
      } else if (e.key === "ArrowUp") {
        setHighlightedOptionIndex(current => {
          let updated = filteredOptions ? filteredOptions?.length - 1 : 0;
          if (current !== undefined) {
            const prevIndex = current - 1;
            if (prevIndex >= resetHighlightedOptionIndex && filteredOptions) {
              updated = prevIndex;
            }
          }

          scrollToIndex(updated);
          return updated;
        });
      }

      // Multi-select key down behavior
      if (
        isMultiSelect &&
        typeof value !== "number" &&
        value?.length &&
        !searchValue.length
      ) {
        if (e.key === "Backspace") {
          if (
            // Remove the highlighted item(s)
            highlightedSelectedItems.length > 0
          ) {
            if (
              // Should remove the highlighted item
              highlightedSelectedItems.length === 1
            ) {
              const highlightedItem = highlightedSelectedItems[0];
              const highlightedOptionIndex = (
                value as MultiSelectValue
              ).indexOf(highlightedItem);
              // Set the highlighted item to be the item to the left
              setHighlightedSelectedItems([value[highlightedOptionIndex - 1]]);
              setCursorIndex(highlightedOptionIndex);
              handleClickItem?.(highlightedItem);
              handleValueChange?.(highlightedItem);
            } else if (
              // Should remove everything
              highlightedSelectedItems.length >=
              ((value as MultiSelectValue)?.length ?? 0)
            ) {
              handleChange?.([]);
            }
          } else {
            // Highlight an item
            setHighlightedSelectedItems([
              value[(cursorIndex ?? value.length) - 1]
            ]);
          }

          // Update the cursor index to the highlighted item
          if (cursorIndex !== undefined) {
            setCursorIndex(current => Math.max(0, current ?? value.length));
          }

          if (closeOnChange) {
            handleChangeIsOpen(false);
          }
        } else if (
          // Move the cursor to the left, highlight the item to the left of the cursor
          e.key === "ArrowLeft"
        ) {
          setCursorIndex(current => {
            if (highlightedSelectedItems.length === value.length) {
              // If all are selected and arrow left, highlight the first item
              setHighlightedSelectedItems([value[0]]);
              return 1;
            }
            if (highlightedSelectedItems.length === 0) {
              // Highlight the last item
              setHighlightedSelectedItems([
                value[Math.max((current ?? value.length) - 1, 0)]
              ]);
              return current;
            }

            // Cursor moves left
            const updatedCursor = Math.max(1, (current ?? value.length) - 1);
            setHighlightedSelectedItems([
              value[Math.max(updatedCursor - 1, 0)]
            ]);
            return updatedCursor;
          });
        } else if (
          // Move the cursor to the right, highlight the item to the left of the cursor
          e.key === "ArrowRight"
        ) {
          setCursorIndex(current => {
            if (highlightedSelectedItems.length === value.length) {
              // If all are selected and arrow right, move cursor to the end and highlight nothing (initial state)
              setHighlightedSelectedItems([]);
              return value.length;
            }

            // Cursor moves right
            const updatedCursor = Math.min(
              value?.length + 1,
              (current ?? value.length) + 1
            );
            if (updatedCursor > value.length) {
              setHighlightedSelectedItems([]);
            } else {
              setHighlightedSelectedItems([
                value[Math.min(updatedCursor - 1, value.length)]
              ]);
            }
            return updatedCursor;
          });
        }

        // Command + a to highlight all
        if (prevUserKey === "Meta" && e.key === "a") {
          setHighlightedSelectedItems(value as MultiSelectValue);
          setCursorIndex(value.length);
        }
      }

      setPrevUserKey(e.key);
    },
    [
      onKeyDown,
      disabled,
      isMultiSelect,
      value,
      searchValue.length,
      handleChangeIsOpen,
      highlightedOptionIndex,
      isOpen,
      withSelectAll,
      setSearchValue,
      closeOnChange,
      handleClickItem,
      handleValueChange,
      filteredOptions,
      resetHighlightedOptionIndex,
      scrollToIndex,
      prevUserKey,
      highlightedSelectedItems,
      cursorIndex,
      handleChange
    ]
  );

  const searchInput = useMemo(() => {
    return (
      <input
        className="select__input"
        ref={searchRef}
        type="text"
        value={searchValue}
        onChange={e => {
          if (!isOpen) {
            setIsOpen(true);
          }
          setSearchValue(e.target.value);
          setHighlightedOptionIndex(0);
          e.preventDefault();
          e.stopPropagation();
        }}
        onKeyDown={handleOnKeyDown}
        disabled={disabled}
        autoFocus={autoFocus}
        onFocus={e => {
          onFocus?.(e);
          if (autoOpenOnFocus && !isOpen) {
            setIsOpen(true);
          }
          e.stopPropagation();
          e.preventDefault();
          setHighlightedOptionIndex(current =>
            current === undefined ? resetHighlightedOptionIndex : current
          );
        }}
        onBlur={e => {
          onBlur?.(e);
        }}
        onKeyUp={onKeyUp}
        onPaste={onPaste}
        onClick={e => {
          onClick?.(e);
          handleChangeIsOpen(true);
          e.preventDefault();
          e.stopPropagation();
        }}
      />
    );
  }, [
    searchRef,
    searchValue,
    handleOnKeyDown,
    disabled,
    autoFocus,
    onKeyUp,
    onPaste,
    isOpen,
    setSearchValue,
    onFocus,
    autoOpenOnFocus,
    resetHighlightedOptionIndex,
    onBlur,
    onClick,
    handleChangeIsOpen
  ]);

  return {
    isOpen,
    filteredOptions,
    handleChangeIsOpen,
    highlightedOptionIndex,
    setHighlightedOptionIndex,
    searchInput,
    cursorIndex,
    highlightedSelectedItems,
    setIsOpen
  };
};
