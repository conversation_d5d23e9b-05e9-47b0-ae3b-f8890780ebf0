import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import useOnClickOutside from "use-onclickoutside";

import { Box, Inline } from "../../../fermions/index.ts";
import { returnStringIfTrue } from "../../../helpers/componentHelpers.ts";
import { useWarningCheckForInputs } from "../../../hooks/useWarningCheckForInputs.tsx";
import { ColorText } from "../../../tokens/IonsInterface.ts";
import { CloseIconButton } from "../../Button/IconButton/CloseIconButton/CloseIconButton.tsx";
import { FloatingWithParent } from "../../FloatingWithParent/FloatingWithParent.tsx";
import { IconSize } from "../../Icon/IconTypes.ts";
import { OpenCloseIcon } from "../../Icon/index.ts";
import { Label } from "../../Label/Label.tsx";
import { LabelTextPosition } from "../../Label/LabelTypes.ts";
import { Pill } from "../../Pill/Pill.tsx";
import { PillBackground, PillVariant } from "../../Pill/PillTypes.ts";
import { Text } from "../../Text/Text.tsx";
import "../Select.scss";
import { SelectOptionProps } from "../SelectOption/SelectOption.tsx";
import { SelectOptions } from "../SelectOptions/SelectOptions.tsx";
import {
  CommonSelectProps,
  MultiSelectValue,
  SelectOptionType,
  SelectValue,
  SingleSelectValue,
  noOptionsLabelDefault,
  selectAllLabelDefault,
  selectAllValue
} from "../SelectTypes.ts";
import { useSelect } from "../useSelect.tsx";
import { Chip } from "./Chip/Chip.tsx";
import "./MultiSelect.scss";

export type MultiSelectRenderElement = ({
  selectedOptions
}: {
  selectedOptions?: SelectOptionType[];
}) => React.ReactNode;
export interface MultiSelectProps
  extends Omit<
    CommonSelectProps,
    "handleSearchExternally" | "renderLeftElement" | "renderRightElement"
  > {
  withSelectAll?: boolean;
  selectAllLabel?: string;
  renderLeftElement?: MultiSelectRenderElement;
  renderRightElement?: MultiSelectRenderElement;
  renderOptionLeftElement?: SelectOptionProps["renderLeftElement"];
  renderOptionRightElement?: SelectOptionProps["renderRightElement"];
  hideCount?: boolean;
}

export const MultiSelect = ({
  value,
  options = [],
  label = "",
  placeholder = "",
  description,
  tooltip,
  required,
  error,
  labelTextPosition = LabelTextPosition.TOP,
  noOptionsLabel = noOptionsLabelDefault,
  withSelectAll = true,
  selectAllLabel = selectAllLabelDefault,
  width,
  disabled,
  withClear = true,
  customSelector,
  className = "",
  position,
  renderLeftElement,
  renderRightElement,
  renderOptionLeftElement,
  renderOptionRightElement,
  onChange: handleChange,
  onlyTriggerChangeWhenBlur = false,

  controlFocus,
  autoFocus,
  autoOpenOnFocus = true,

  onClick,
  onFocus,
  onBlur,
  onKeyDown,
  onKeyUp,
  onPaste,
  hideCount,

  // handleSearchExternally, // not yet supported for multi-select as it does not show the selected the items if they are not in the list
  onChangeSearch,
  clearIfOptionMissing = true,
  valueSequenceId,
  ...props
}: MultiSelectProps) => {
  useWarningCheckForInputs({
    value: value as string,
    defaultValue: props.defaultValue as string,
    onChange: handleChange
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLUListElement>(null);
  const selectRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLInputElement>(null);

  const [searchValue, setSearchValue] = useState<string>("");
  const [tempValue, setTempValue] = useState(
    value && (value as MultiSelectValue)?.length
      ? value
      : (props.defaultValue ?? [])
  );

  useEffect(() => {
    if (props.isLoading) {
      return;
    }
    setTempValue(value ? value : (props.defaultValue ?? []));
  }, [props.defaultValue, props.isLoading, value, valueSequenceId]);

  const selectedOptions = useMemo(() => {
    if (!Array.isArray(tempValue)) {
      return [];
    }

    const selection = (() => {
      const valueToIdx = tempValue?.reduce((acc, optionValue, idx) => {
        acc.set(optionValue, idx);
        return acc;
      }, new Map<SingleSelectValue, number>());
      return {
        has: (optionValue: SingleSelectValue) => valueToIdx.has(optionValue),
        indexOf: (optionValue: SingleSelectValue) =>
          valueToIdx.get(optionValue) ?? -1
      };
    })();

    return (options ?? [])
      .filter(option => selection.has(option.value) && !option.disabled)
      .sort((a, b) => selection.indexOf(a.value) - selection.indexOf(b.value));
  }, [options, tempValue]);

  const handleChangeInternally = useCallback(
    (
      updatedValue?: SelectValue,
      clearSearch = false,
      notifyExternalChange = !onlyTriggerChangeWhenBlur
    ) => {
      if (notifyExternalChange && updatedValue !== value) {
        handleChange?.(updatedValue);
      }
      setTempValue(updatedValue ?? []);
      if (clearSearch) {
        setSearchValue("");
      }
      searchRef.current?.focus();
    },
    [onlyTriggerChangeWhenBlur, value, handleChange]
  );

  useEffect(() => {
    if (props.isLoading || !tempValue) {
      // if isLoading=true, options are still being fetched (for dynamicOptions from api)
      // so don't check if the value exists in the options
      return;
    }
    if (typeof tempValue !== "object" || !clearIfOptionMissing) {
      return;
    }

    // handleSearchExternally not yet supported for multi-select
    // if (handleSearchExternally) {
    //   return;
    // }

    // in the value, if the item doesn't exist in the option, remove it and notify others cos its actually a change
    // We only emit on changes because we don't want to emit on every render and cause an infinite loop
    const multiselectValues = typeof tempValue === "object" ? tempValue : [];
    const optionValueSet = new Set(options.map(option => option.value));

    const hasMissingOption = multiselectValues.some(
      v => !optionValueSet.has(v)
    );
    if (hasMissingOption) {
      const result = multiselectValues.filter(v =>
        options.some(option => option.value === v)
      );
      handleChange?.(result);
      setTempValue(result ?? []);
    }
  }, [clearIfOptionMissing, handleChange, options, props, tempValue]);

  const handleClickItem = useCallback(
    (newValue: SingleSelectValue) => {
      let updatedValue = typeof tempValue === "object" ? tempValue : [];
      if (newValue === selectAllValue) {
        if (updatedValue.length === options?.length) {
          updatedValue = [];
        } else {
          updatedValue = options?.map(option => option.value) ?? [];
        }
      } else {
        if (updatedValue?.includes(newValue)) {
          updatedValue = updatedValue?.filter(v => v !== newValue) ?? [];
        } else {
          updatedValue = [...(updatedValue ?? []), newValue];
        }
      }
      handleChangeInternally?.(updatedValue);
    },
    [handleChangeInternally, options, tempValue]
  );

  // Copy paste list of values
  useEffect(() => {
    if (searchValue?.includes(",")) {
      const items = searchValue.split(",");
      const notFoundValues = [];
      const newValues = [];

      for (const item of items) {
        const trimmedValue = item.trim().toLowerCase();
        if (trimmedValue.length) {
          const foundOption = options.find(
            o => o.label.toLowerCase() === trimmedValue
          );
          if (foundOption) {
            newValues.push(foundOption.value);
          } else {
            notFoundValues.push(item);
          }
        }
      }
      if (newValues.length) {
        handleChangeInternally?.([
          ...((tempValue as MultiSelectValue) ?? []),
          ...newValues
        ]);
        setSearchValue(notFoundValues.join(","));
      }
    }
  }, [
    handleChangeInternally,
    handleClickItem,
    options,
    searchValue,
    selectedOptions,
    tempValue
  ]);

  const {
    isOpen,
    highlightedOptionIndex,
    setHighlightedOptionIndex,
    filteredOptions,
    handleChangeIsOpen,
    searchInput,
    cursorIndex,
    highlightedSelectedItems
  } = useSelect({
    value: tempValue,
    options,
    isMultiSelect: true,
    withSelectAll,
    closeOnChange: false,
    searchRef,
    handleClickItem,
    handleChange: handleChangeInternally,

    searchValue,
    setSearchValue,
    scrollContainerRef,
    disabled,

    // handleSearchExternally not yet supported for multi-select
    handleSearchExternally: false,
    onChangeSearch,

    controlFocus,
    autoFocus,
    autoOpenOnFocus,

    onClick,
    onFocus,
    onBlur,
    onKeyDown,
    onKeyUp,
    onPaste
  });

  useOnClickOutside(containerRef, () => {
    if (isOpen) {
      handleChangeIsOpen(undefined);
      if (onlyTriggerChangeWhenBlur) {
        handleChange?.(tempValue);
      }
    }
  });

  const selectedOptionsAsPills = useMemo(() => {
    if (customSelector || !tempValue) {
      return [];
    }
    return selectedOptions?.map(option => (
      <Chip
        key={option.value}
        label={option.label}
        onRemove={() => {
          handleChangeInternally?.(
            (tempValue as MultiSelectValue)?.filter(v => v !== option.value),
            false,
            !isOpen
          );
        }}
        isHighlighted={highlightedSelectedItems.includes(option.value)}
      />
    ));
  }, [
    customSelector,
    handleChangeInternally,
    highlightedSelectedItems,
    isOpen,
    selectedOptions,
    tempValue
  ]);

  const selector = useMemo(() => {
    if (customSelector) {
      return customSelector({
        handleChange: handleChangeInternally,
        handleChangeIsOpen,
        isOpen,
        selectRef,
        value: tempValue,
        placeholder,
        searchInput,
        searchValue,
        selectedOption: selectedOptions
      });
    }
    return (
      <Inline
        ref={selectRef}
        alignment="left"
        classNames={[
          "multi-select",
          "select",
          returnStringIfTrue(disabled, "select--disabled")
        ]}
        onClick={e => {
          e.stopPropagation();
          e.preventDefault();
          handleChangeIsOpen();
        }}
        gap="050"
      >
        {renderLeftElement?.({
          selectedOptions
        })}
        <Inline
          width="100"
          height="100"
          onMouseEnter={() => setHighlightedOptionIndex(undefined)}
        >
          <Inline
            className="select__value"
            gap="050"
            width="fit"
            alignment="left"
          >
            {selectedOptions.length > 0 && [
              ...selectedOptionsAsPills.slice(0, cursorIndex)
            ]}
            {searchInput}
            {selectedOptions.length > 0 &&
              cursorIndex !== undefined &&
              selectedOptionsAsPills.slice(
                cursorIndex,
                (tempValue as MultiSelectValue)?.length
              )}
          </Inline>
          {!searchValue.length &&
            typeof selectedOptions !== "number" &&
            !selectedOptions?.length && (
              <Text className="select__placeholder" color={ColorText.TERTIARY}>
                {String(placeholder)}
              </Text>
            )}
        </Inline>
        <Inline className="select__right" gap="025" alignment="right">
          {withClear && !disabled && selectedOptions?.length > 0 && (
            <CloseIconButton
              className="select__clear"
              onClick={e => {
                handleChangeInternally?.([], true, true);
                e.stopPropagation();
                e.preventDefault();
              }}
              skipFocus
              color={ColorText.PRIMARY}
              size={IconSize.SMALL}
            />
          )}
        </Inline>
        {renderRightElement?.({
          selectedOptions
        })}
        {!hideCount && selectedOptions.length > 0 && (
          <Pill
            className="select__count"
            label={`${selectedOptions.length}`}
            background={PillBackground.STRONG}
            variant={disabled ? undefined : PillVariant.COLORED}
          />
        )}

        <OpenCloseIcon
          className="select__icon"
          isOpen={isOpen}
          disabled={disabled}
        />
      </Inline>
    );
  }, [
    cursorIndex,
    customSelector,
    disabled,
    handleChangeInternally,
    handleChangeIsOpen,
    hideCount,
    isOpen,
    placeholder,
    renderLeftElement,
    renderRightElement,
    searchInput,
    searchValue,
    selectedOptions,
    selectedOptionsAsPills,
    setHighlightedOptionIndex,
    tempValue,
    withClear
  ]);

  return (
    <Box
      ref={containerRef}
      position="relative"
      classNames={[className, "multi-select-input"]}
      width={width}
    >
      <Label
        label={label}
        error={error}
        description={description}
        tooltip={tooltip}
        required={required}
        textPosition={labelTextPosition}
        disabled={disabled}
      >
        {selector}
      </Label>
      {isOpen && (
        <FloatingWithParent
          parentRef={selectRef}
          onOpenChange={handleChangeIsOpen}
          minWidthAsParent
          position={position}
        >
          <SelectOptions
            {...props}
            options={filteredOptions}
            value={tempValue}
            onChange={handleChangeInternally}
            noOptionsLabel={noOptionsLabel}
            isMultiSelect
            withSelectAll={withSelectAll}
            selectAllLabel={selectAllLabel}
            highlightedOptionIndex={highlightedOptionIndex}
            setHighlightedOptionIndex={setHighlightedOptionIndex}
            scrollContainerRef={scrollContainerRef}
            renderLeftElement={renderOptionLeftElement}
            renderRightElement={renderOptionRightElement}
          />
        </FloatingWithParent>
      )}
    </Box>
  );
};
