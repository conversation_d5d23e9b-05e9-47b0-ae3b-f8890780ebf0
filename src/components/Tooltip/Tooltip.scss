@use "../../tokens/tokens" as *;

@mixin tooltip_size($size) {
  font-size: var(--font-size-body-#{$size}, 18px);
}

.tooltip {
  width: inherit;
  &-content {
    z-index: 10;
    position: relative;
    text-align: left;
    width: fit-content;
    max-width: 200px !important;
    padding: var(--components-tooltip-padding-vertical, 2px)
      var(--components-tooltip-padding-horizontal, 4px);
    border-radius: var(--components-tooltip-border-radius, 4px);
    background: var(
      --components-tooltip-color-background,
      rgba(2, 20, 41, 0.8)
    );
    gap: var(--components-tooltip-gap, 2px);
    & .text {
      color: var(--components-tooltip-color-text, #fff);
      font-size: var(--components-tooltip-font-size, 14px);
      font-weight: var(--components-tooltip-font-weight, 400);
    }
    color: var(--components-tooltip-color-text, #fff);
    font-size: var(--components-tooltip-font-size, 14px);
    font-weight: var(--components-tooltip-font-weight, 400);
    @include shadow_raised_slightly();

    &--custom {
      padding: 0;
      background-color: transparent;
      color: inherit;
      font-size: inherit;
      font-weight: inherit;
    }
  }
}
