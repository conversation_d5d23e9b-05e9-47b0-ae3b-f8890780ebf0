import { useCallback, useMemo } from "react";
import React from "react";

import {
  composeComponentClassNames,
  returnStringIfTrue
} from "../../helpers/componentHelpers.ts";
import { CloseIconButton } from "../Button/IconButton/CloseIconButton/CloseIconButton.tsx";
import { IconSize } from "../Icon/IconTypes.ts";
import { OpenCloseIcon } from "../Icon/index.ts";
import { Label } from "../Label/Label.tsx";
import { Select, SelectProps } from "../Select/Select.tsx";
import {
  CustomSelector,
  SelectOptionType,
  SelectValue
} from "../Select/SelectTypes.ts";
import { Text } from "../Text/Text.tsx";
import "./PillSelect.scss";
import { PillSelectVariant } from "./PillSelectTypes.ts";

export interface PillSelectProps {
  name?: string;
  variant?: `${PillSelectVariant}`;
  options?: SelectOptionType[];
  value?: SelectValue;
  defaultValue?: SelectValue;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  handleChange?: (value?: SelectValue) => void;
  label?: string;
  leftElement?: React.ReactNode;
  rightElement?: React.ReactNode;
  withClear?: boolean;
  autoFocus?: boolean;
  renderOptionLeftElement?: SelectProps["renderOptionLeftElement"];
  renderOptionRightElement?: SelectProps["renderOptionRightElement"];
  fitOptionText?: SelectProps["fitOptionText"];
  maxHeight?: React.CSSProperties["maxHeight"];
  className?: string;
  onlyTriggerChangeWhenBlur?: boolean;
}

export const PillSelect = ({
  name,
  variant = PillSelectVariant.DEFAULT,
  options = [],
  disabled,
  value,
  defaultValue,
  required,
  error,
  handleChange,
  label,
  leftElement = <></>,
  rightElement = <></>,
  withClear = !required,
  autoFocus,
  renderOptionLeftElement,
  renderOptionRightElement,
  fitOptionText,
  onlyTriggerChangeWhenBlur,
  className,
  maxHeight
}: PillSelectProps) => {
  const selectedOption = useMemo(
    () => options.find(item => item.value === value),
    [options, value]
  );

  const customSelector: CustomSelector = useCallback(
    ({ handleChange, isOpen, handleChangeIsOpen, selectRef }) => {
      return (
        <button
          ref={selectRef as unknown as React.RefObject<HTMLButtonElement>}
          className={composeComponentClassNames("pill-select", { variant })}
          onClick={() => handleChangeIsOpen()}
          disabled={disabled}
          autoFocus={autoFocus}
          type="button"
        >
          {(label || error) && (
            <Label
              label={
                label
                  ? `${label}${returnStringIfTrue(selectedOption, ":")}`
                  : ""
              }
              required={label ? required : false}
              error={error}
            />
          )}
          {leftElement}
          {selectedOption && <Text>{selectedOption.label}</Text>}
          {rightElement}
          {selectedOption && withClear && !disabled && (
            <CloseIconButton
              className="pill-select__clear"
              size={IconSize.SMALL}
              onClick={e => {
                handleChange?.();
                handleChangeIsOpen(false);
                e.preventDefault();
                e.stopPropagation();
              }}
            />
          )}
          {!disabled && <OpenCloseIcon isOpen={isOpen} />}
        </button>
      );
    },
    [
      variant,
      disabled,
      autoFocus,
      label,
      error,
      selectedOption,
      required,
      leftElement,
      rightElement,
      withClear
    ]
  );

  return (
    <Select
      className={className}
      name={name}
      id="pill-select"
      disabled={disabled}
      options={options}
      customSelector={customSelector}
      value={value}
      onChange={handleChange}
      onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
      defaultValue={defaultValue}
      autoFocus={autoFocus}
      renderOptionLeftElement={renderOptionLeftElement}
      renderOptionRightElement={renderOptionRightElement}
      fitOptionText={fitOptionText}
      width="fit"
      maxHeight={maxHeight}
    />
  );
};
