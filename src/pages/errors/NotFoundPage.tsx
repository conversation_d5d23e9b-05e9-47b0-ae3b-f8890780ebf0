import React from "react";

import { Inline, PageTemplate, Stack } from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { ErrorElement } from "@pages/errors/ErrorElement";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { buildShortPageTitle, usePageTitle } from "@src/hooks/usePageTitle.ts";
import { Workspace } from "@src/types/workspace.ts";

// Temporary 'not found' page, not yet vetted by UX
export const NotFoundPage = () => {
  return (
    <PageTemplate topNavigation={undefined} sideNavigation={undefined}>
      <NotFoundPageBody />
    </PageTemplate>
  );
};

export const NotFoundPageBody = () => {
  const outletContext = useOutletContext<{
    workspace: Workspace;
  }>();
  const d = useDictionary();

  usePageTitle(
    buildShortPageTitle({
      section: d("errors.common.pageNotFound"),
      context: outletContext?.workspace?.key ?? d("ui.terminology.appName")
    }),
    true
  );

  return (
    <Stack width="fill" height="fill" alignment="center" gap="200">
      <Inline>
        <ErrorElement />
      </Inline>
    </Stack>
  );
};
