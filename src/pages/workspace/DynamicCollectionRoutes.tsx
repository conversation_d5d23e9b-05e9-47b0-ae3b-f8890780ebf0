import React from "react";

import range from "lodash/range";
import { Route, Routes, useOutletContext, useParams } from "react-router-dom";

import { CollectionFormPage } from "@pages/collection/forms/CollectionFormPage";
import { CollectionFormProvider } from "@pages/collection/forms/CollectionFormProvider";
import { CollectionHomeProvider } from "@pages/collection/home/<USER>";
import { Home } from "@pages/collection/home/<USER>";
import { ErrorElement } from "@pages/errors/ErrorElement";

import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace";

const FormRouteWrapper = ({ level }: { level: number }) => {
  const { formIdentifier } = useParams<{ formIdentifier: string }>();
  // we need the formIdentifier as a key to ensure that the form is reloaded when the identifier changes
  // previously, we can see a flash of the previously loaded form when navigating between forms
  return (
    <CollectionHomeProvider level={level}>
      <CollectionFormProvider key={formIdentifier}>
        <CollectionFormPage key={formIdentifier} />
      </CollectionFormProvider>
    </CollectionHomeProvider>
  );
};

export const DynamicCollectionRoutes = () => {
  const { workspace, document } = useOutletContext<{
    workspace: Workspace;
    document: WorkspaceDocument;
  }>();

  if (!workspace?.key || !document?.foundations.order) {
    return [];
  }

  // NB: this is a Dependant Router, so path here are relative to where this component is used
  return (
    <Routes>
      {document?.foundations.order.map((_, foundationLevel: number) => {
        const levelPaths = range(1, foundationLevel + 1)
          .map(i => `/:level${i}Key`)
          .join("");
        const path = `/${levelPaths}`;
        const formPath = `${path}/form/:formIdentifier`;
        return (
          <React.Fragment key={`foundationLevel-${foundationLevel}`}>
            <Route
              key={path}
              path={path}
              element={
                <CollectionHomeProvider level={foundationLevel}>
                  <Home />
                </CollectionHomeProvider>
              }
            />
            <Route
              key={formPath}
              path={formPath}
              element={<FormRouteWrapper level={foundationLevel} />}
            />
            <Route
              key={`${path}/*`}
              path={`${path}/*`}
              element={<ErrorElement />}
            />
          </React.Fragment>
        );
      })}
    </Routes>
  );
};
