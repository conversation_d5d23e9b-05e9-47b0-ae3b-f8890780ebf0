import React, {
  ReactN<PERSON>,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import { AnyDocumentId } from "@automerge/automerge-repo";
import {
  useDocHandle,
  useDocument
} from "@automerge/automerge-repo-react-hooks";
import { ChangeFn, ChangeOptions } from "@automerge/automerge/next";
import {
  Box,
  Button,
  Loading,
  PageTemplate,
  ToastNotificationVariant,
  ToastStack,
  ToastStackHandle
} from "@oneteam/onetheme";
import { useQuery } from "@tanstack/react-query";
import {
  Outlet,
  generatePath,
  useLocation,
  useNavigate,
  useParams
} from "react-router-dom";

import { DocumentErrorHelper } from "@helpers/DocumentErrorHelper.ts";
import { getLinkToFEDDebugPage } from "@helpers/debug.ts";
import { getData } from "@helpers/getData.ts";
import { useGetWorkspaceUsersByWorkspaceIdAndUserId } from "@helpers/userHelper.ts";

import { SideNavigation } from "@components/shared/Navigation/SideNavigation.tsx";
import { TopNavigation } from "@components/shared/Navigation/TopNavigation.tsx";
import { useSideNavigationContext } from "@components/shared/Navigation/useSideNavigationContext.ts";

import { ErrorElement } from "@pages/errors/ErrorElement.tsx";
import { useWorkspaceAccessLevel } from "@pages/settings/permissions/WorkspaceAccessLevelContext/WorkspaceAccessLevelContext.tsx";
import { WorkspaceUsersProvider } from "@pages/settings/permissions/WorkspaceUsersContext/WorkspaceUsersProvider.tsx";
import { PubSubComponent } from "@pages/workspace/PubSubComponent.tsx";

import { routeConstants } from "@src/constants/routeConstants.ts";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  WORKSPACES_QUERY_KEY,
  useGetWorkspaces
} from "@src/hooks/useGetWorkspaces.tsx";
import { useLogout } from "@src/hooks/useLogout.js";
import { useMatchPath } from "@src/hooks/useMatchPath.tsx";
import { useOneTeam } from "@src/hooks/useOneTeam.js";
import {
  Action,
  ActionType,
  ViewExecutionDocument
} from "@src/hooks/usePubSub.tsx";
import {
  WorkspaceAccessLevel,
  WorkspaceUserSearch
} from "@src/types/WorkspaceUser.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { useAuth } from "../../hooks/useAuth.tsx";

interface LayoutProps {
  readonly pageError?: ReactNode;
}

export type DocChange = (
  changeFn: ChangeFn<WorkspaceDocument>,
  options?: ChangeOptions<WorkspaceDocument> | undefined
) => void;

export type PushToastNotification = (
  heading: string,
  description: string,
  variant: ToastNotificationVariant,
  durationMs?: number,
  action?: Action
) => void;

export const WorkspaceLayout = ({ pageError = null }: LayoutProps) => {
  useLogout();
  useOneTeam();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { workspaceKey } = useParams();
  const { pathname } = useLocation();
  const { matchPath } = useMatchPath();
  const [isChangingWorkspace, setIsChangingWorkspace] = useState(false);
  const [currentWorkspaceKey, setCurrentWorkspaceKey] = useState(workspaceKey);
  const { hasAccessToLevel, setCurrentLevel } = useWorkspaceAccessLevel();

  const { data: workspace, isLoading } = useQuery({
    queryKey: [WORKSPACES_QUERY_KEY, workspaceKey],
    queryFn: () => getData(`/workspaces/key/${workspaceKey}`),
    enabled: !!workspaceKey,
    retry: 2
  });

  const { data: workspaceUserInfo } =
    useGetWorkspaceUsersByWorkspaceIdAndUserId(workspace?.id, user?.id) as {
      data: WorkspaceUserSearch;
    };

  const [document, docChange] = useDocument<WorkspaceDocument>(
    workspace?.documentId
  );

  const {
    data: workspaces,
    isFetched: workspacesFetched,
    isLoading: isLoadingAllWorkspaces
  } = useGetWorkspaces();

  const docHandle = useDocHandle<WorkspaceDocument>(
    workspace?.documentId as AnyDocumentId | undefined
  );

  useEffect(() => {
    if (workspaceKey !== currentWorkspaceKey) {
      setCurrentWorkspaceKey(workspaceKey);
      setIsChangingWorkspace(true);
      setTimeout(() => {
        setIsChangingWorkspace(false);
      }, 0);
    }
    setCurrentLevel(workspaceUserInfo?.accessLevel as WorkspaceAccessLevel[]);
  }, [workspaceKey, currentWorkspaceKey, workspaceUserInfo, setCurrentLevel]);

  // this effect is used to redirect the user to the correct page based on the current path and workspaces
  useEffect(() => {
    if (
      matchPath("/", { exact: true }) &&
      workspacesFetched &&
      workspaces?.length > 0
    ) {
      navigate(
        generatePath(routeConstants.collectionHome, {
          workspaceKey: workspaces[0]?.key
        }),
        { replace: true }
      );
    }

    if (matchPath(routeConstants.home, { exact: true })) {
      navigate(generatePath(routeConstants.collectionHome, { workspaceKey }), {
        replace: true
      });
      return;
    }

    if (!workspacesFetched || !workspaces) {
      return;
    }

    if (workspaces.length === 0) {
      navigate(routeConstants.creationWorkspace);
      return;
    }

    if (!workspaceKey) {
      navigate(
        generatePath(routeConstants.home, { workspaceKey: workspaces[0]?.key }),
        { replace: true }
      );
    }
  }, [
    matchPath,
    navigate,
    pathname,
    workspaceKey,
    workspaces,
    workspacesFetched
  ]);

  const d = useDictionary();
  const docErrorHelper: DocumentErrorHelper = useMemo(
    () => new DocumentErrorHelper(document, d),
    [d, document]
  );

  const flowRunnerRefresh = useRef(() => {});
  const toastStackRef = useRef<ToastStackHandle>(null);
  const pushToastNotification = useCallback<PushToastNotification>(
    (
      heading: string,
      description: string,
      variant: ToastNotificationVariant,
      durationMs: number = 15000,
      action?: Action
    ) => {
      toastStackRef.current?.pushToast({
        heading,
        description,
        variant,
        duration: durationMs,
        actions: (() => {
          switch (action?.actionType) {
            case ActionType.VIEW_IN_FLOW_RUNNER: {
              return (
                <Button
                  label={d("ui.notifications.actionLabels.viewInFlowRunner")}
                  size="small"
                  variant="text"
                  onClick={() => {
                    navigate(
                      generatePath(routeConstants.flowRunner, {
                        workspaceKey
                      })
                    );
                  }}
                />
              );
            }

            case ActionType.VIEW_EXECUTION_DOCUMENT: {
              const flowExecutionId = action
                ? (action as ViewExecutionDocument).flowExecutionId
                : null;
              const workspaceId = action
                ? (action as ViewExecutionDocument).workspaceId
                : null;

              return (
                flowExecutionId &&
                workspaceId && (
                  <Button
                    label={d(
                      "ui.notifications.actionLabels.viewExecutionDocument"
                    )}
                    size="small"
                    variant="text"
                    onClick={() => {
                      window.open(
                        getLinkToFEDDebugPage(workspaceId, flowExecutionId),
                        "_blank"
                      );
                    }}
                  />
                )
              );
            }

            default: {
              return <></>;
            }
          }
        })()
      });
    },
    [d, navigate, workspaceKey]
  );

  const { context } = useSideNavigationContext();
  const content = useCallback(() => {
    if (isLoading || isLoadingAllWorkspaces) {
      return (
        <Box height="100" contentsHeight="fill">
          <Loading size={24} />
        </Box>
      );
    }

    if (!workspace && !isLoading && !isChangingWorkspace) {
      return <ErrorElement />;
    }

    if (
      !workspace ||
      !document ||
      !docHandle?.isReady() ||
      isLoading ||
      isChangingWorkspace
    ) {
      return (
        <Box height="100" contentsHeight="fill">
          <Loading size={24} />
        </Box>
      );
    } else {
      return (
        <WorkspaceUsersProvider workspace={workspace}>
          <Outlet
            context={{
              user,
              workspace,
              document,
              docChange,
              docErrorHelper,
              flowRunnerRefresh,
              pushToastNotification
            }}
          />
        </WorkspaceUsersProvider>
      );
    }
  }, [
    isLoading,
    isLoadingAllWorkspaces,
    workspace,
    isChangingWorkspace,
    document,
    docHandle,
    user,
    docChange,
    docErrorHelper,
    pushToastNotification
  ]);

  return (
    <PageTemplate
      topNavigation={<TopNavigation user={user} workspace={workspace} />}
      sideNavigation={
        context && hasAccessToLevel ? (
          <SideNavigation
            context={context}
            workspace={workspace}
            docErrorHelper={docErrorHelper}
            pushToastNotification={pushToastNotification}
          />
        ) : undefined
      }
    >
      <Box
        position="fixed"
        style={{
          top: "var(--components-page-body-template-padding-vertical, 16px)",
          right:
            "var(--components-page-body-template-padding-horizontal, 24px)",
          zIndex: 1000
        }}
      >
        <ToastStack
          ref={toastStackRef}
          position="top-right"
          handleClearAll={toasts => {
            return toasts.map(toast => ({ ...toast, visible: false }));
          }}
        />
      </Box>
      {pageError ?? content()}
      {workspace && !isLoading && (
        <PubSubComponent
          workspaceId={workspace.id}
          flowRunnerRefresh={flowRunnerRefresh}
          pushToastNotification={pushToastNotification}
        />
      )}
    </PageTemplate>
  );
};
