import React, { useState } from "react";

import {
  Floating,
  FloatingPosition,
  Form,
  Heading,
  HeadingSize,
  IconButton,
  IconSize,
  Inline,
  Modal,
  Stack
} from "@oneteam/onetheme";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFormContext } from "react-hook-form";
import { generatePath, useNavigate } from "react-router-dom";
import z from "zod";

import { ApiValidationError, CustomError } from "@helpers/errorHelper.ts";
import { postData } from "@helpers/postData.ts";

import { NameKeyFormField } from "@components/shared/NameKeyFormField/NameKeyFormField";
import { autoGenerateKeyFromName } from "@components/shared/NameKeyFormField/autoGenerateKeyFromName";

import { SplashScreen } from "@pages/workspace/SplashScreen/SplashScreen.tsx";

import { routeConstants } from "@src/constants/routeConstants.js";
import { useAuth } from "@src/hooks/useAuth";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { WORKSPACES_QUERY_KEY } from "@src/hooks/useGetWorkspaces.tsx";
import { keySchema } from "@src/types/Key";
import {
  Workspace,
  WorkspaceForCreateOrUpdate,
  getWorkspaceSchema
} from "@src/types/workspace.ts";

import "./CreateWorkspace.scss";

export const CreateWorkspace = () => {
  const [serverError, setServerError] = useState<CustomError | undefined>();
  const navigate = useNavigate();
  const d = useDictionary();
  const workspaceSchema = getWorkspaceSchema(d);
  const { updateLastActiveTabs } = useAuth();

  const [keyError, setKeyError] = useState<string | undefined>();
  const queryClient = useQueryClient();
  const { mutate: createWorkspace } = useMutation<
    Workspace,
    CustomError,
    Workspace
  >({
    mutationFn: (workspaceForCreate: WorkspaceForCreateOrUpdate) =>
      postData(`/workspaces`, workspaceForCreate),
    onSuccess: createdWorkspace => {
      navigate(
        generatePath(routeConstants.configuration, {
          workspaceKey: createdWorkspace.key
        })
      );
      updateLastActiveTabs({});
      queryClient.invalidateQueries({ queryKey: [WORKSPACES_QUERY_KEY] });
    },
    onError: (error: CustomError) => {
      setServerError(error);
    }
  });

  const workspaceForCreate = {
    name: "",
    key: "",
    description: ""
  } as WorkspaceForCreateOrUpdate;

  return (
    <SplashScreen>
      <Floating position={FloatingPosition.CENTER}>
        <Modal
          className="create-workspace__modal"
          headingOverride={
            <Inline gap="025">
              {/* TODO: only show if there are existing workspaces */}
              <IconButton
                name="chevron_left"
                onClick={() => navigate(-1)}
                size={IconSize.LARGE}
              />
              <Heading size={HeadingSize.M}>
                {d("ui.workspace.create.title")}
              </Heading>
            </Inline>
          }
        >
          <Form
            d={useDictionary()}
            handleSubmit={(data: Workspace) => {
              setServerError(undefined);
              let key = data.key.trim().toUpperCase();

              if (!key) {
                // If the key is not set, we can set it to the uppercase version of the name
                key = autoGenerateKeyFromName(data.name) ?? "";
              }

              try {
                const keyParsed = keySchema(d).parse(key);
                createWorkspace({ ...data, key: keyParsed });
              } catch (error) {
                setKeyError((error as z.ZodError).errors[0].message);
              }
            }}
            handleCancel={() => {
              navigate(-1);
            }}
            schema={workspaceSchema}
            defaultValues={workspaceForCreate}
            submitLabel={d("ui.workspace.create.createButton")}
          >
            <CreateWorkspaceFields
              errors={serverError?.errors}
              keyError={keyError}
            />
          </Form>
        </Modal>
      </Floating>
    </SplashScreen>
  );
};

interface CreateWorkspaceFieldsProps {
  errors: ApiValidationError[] | undefined;
  keyError: string | undefined;
}

const CreateWorkspaceFields = ({
  errors,
  keyError
}: CreateWorkspaceFieldsProps) => {
  const d = useDictionary();
  const formContext = useFormContext();

  if (errors?.length) {
    formContext.clearErrors();
    errors.forEach(e => {
      formContext.setError(e.field, { message: d(e.key) });
    });
  }
  return (
    <Stack
      gap="150"
      width="100"
      contentsWidth="100"
      style={{
        padding: "var(--spacing-050) 0"
      }}
    >
      <NameKeyFormField
        nameField={{ label: d("ui.workspace.fields.name.label") }}
        keyField={{
          label: d("ui.workspace.fields.key.label"),
          tooltip: d("ui.workspace.fields.key.tooltip"),
          error: keyError
        }}
      />
      <Form.TextAreaField
        name="description"
        label={d("ui.workspace.fields.description.label")}
      />
    </Stack>
  );
};
