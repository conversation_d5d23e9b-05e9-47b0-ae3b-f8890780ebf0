import React, { useState } from "react";

import {
  Floating,
  FloatingPosition,
  Form,
  Heading,
  HeadingSize,
  IconButton,
  IconSize,
  Inline,
  Modal,
  Stack
} from "@oneteam/onetheme";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFormContext } from "react-hook-form";
import { generatePath, useNavigate } from "react-router-dom";

import { ApiValidationError, CustomError } from "@helpers/errorHelper.ts";
import { postData } from "@helpers/postData.ts";

import { SplashScreen } from "@pages/workspace/SplashScreen/SplashScreen.tsx";

import { routeConstants } from "@src/constants/routeConstants.js";
import { useAuth } from "@src/hooks/useAuth";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { WORKSPACES_QUERY_KEY } from "@src/hooks/useGetWorkspaces.tsx";
import {
  Workspace,
  WorkspaceForCreateOrUpdate,
  getWorkspaceSchema
} from "@src/types/workspace.ts";

import "./CreateWorkspace.scss";

export const CreateWorkspace = () => {
  const [serverError, setServerError] = useState<CustomError | undefined>();
  const navigate = useNavigate();
  const d = useDictionary();
  const workspaceSchema = getWorkspaceSchema(d);
  const { updateLastActiveTabs } = useAuth();

  const queryClient = useQueryClient();
  const { mutate: createWorkspace } = useMutation<
    Workspace,
    CustomError,
    Workspace
  >({
    mutationFn: (workspaceForCreate: WorkspaceForCreateOrUpdate) =>
      postData(`/workspaces`, workspaceForCreate),
    onSuccess: createdWorkspace => {
      navigate(
        generatePath(routeConstants.configuration, {
          workspaceKey: createdWorkspace.key
        })
      );
      updateLastActiveTabs({});
      queryClient.invalidateQueries({ queryKey: [WORKSPACES_QUERY_KEY] });
    },
    onError: (error: CustomError) => {
      setServerError(error);
    }
  });

  const workspaceForCreate = {
    name: "",
    key: "",
    description: ""
  } as WorkspaceForCreateOrUpdate;

  return (
    <SplashScreen>
      <Floating position={FloatingPosition.CENTER}>
        <Modal
          className="create-workspace__modal"
          headingOverride={
            <Inline gap="025">
              <IconButton
                name="chevron_left"
                onClick={() => navigate(-1)}
                size={IconSize.LARGE}
              />
              <Heading size={HeadingSize.M}>
                {d("ui.workspace.create.title")}
              </Heading>
            </Inline>
          }
        >
          <Form
            d={useDictionary()}
            handleSubmit={(data: Workspace) => {
              setServerError(undefined);
              createWorkspace(data);
            }}
            handleCancel={() => {
              navigate(-1);
            }}
            schema={workspaceSchema}
            defaultValues={workspaceForCreate}
            submitLabel={d("ui.workspace.create.createButton")}
          >
            <CreateWorkspaceFields errors={serverError?.errors} />
          </Form>
        </Modal>
      </Floating>
    </SplashScreen>
  );
};

interface CreateWorkspaceFieldsProps {
  errors: ApiValidationError[] | undefined;
}

const CreateWorkspaceFields = ({ errors }: CreateWorkspaceFieldsProps) => {
  const d = useDictionary();
  const formContext = useFormContext();
  if (errors?.length) {
    formContext.clearErrors();
    errors.forEach(e => {
      formContext.setError(e.field, { message: d(e.key) });
    });
  }
  return (
    <Stack gap="100" width="100" contentsWidth="100">
      <Form.TextField
        name="name"
        label={d("ui.workspace.fields.name.label")}
        required
        autoFocus
      />
      <Form.TextAreaField
        name="description"
        label={d("ui.workspace.fields.description.label")}
      />
      <Form.TextField
        name="key"
        label={d("ui.workspace.fields.key.label")}
        description={d("ui.workspace.fields.key.description")}
        required
        onChange={input => {
          formContext.setValue("key", input.toUpperCase());
          formContext.trigger("key"); // update validation after changing the value
        }}
      />
    </Stack>
  );
};
