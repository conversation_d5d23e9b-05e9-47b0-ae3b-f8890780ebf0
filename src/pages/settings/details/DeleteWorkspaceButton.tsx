import React, { useMemo } from "react";

import {
  Button,
  ButtonTraffic,
  ConfirmationModal,
  ModalDialog,
  Overlay
} from "@oneteam/onetheme";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  generatePath,
  useLocation,
  useNavigate,
  useParams
} from "react-router-dom";

import { deleteData } from "@helpers/deleteData";

import { commonIcons } from "@src/constants/iconConstants";
import { routeConstants } from "@src/constants/routeConstants";
import { useAuth } from "@src/hooks/useAuth";
import { useDictionary } from "@src/hooks/useDictionary";
import {
  WORKSPACES_QUERY_KEY,
  useGetWorkspaces
} from "@src/hooks/useGetWorkspaces";
import { Workspace } from "@src/types/workspace.ts";

export interface DeleteWorkSpaceButtonProps {
  workspace: Workspace;
}

export const DeleteWorkspaceButton = ({
  workspace
}: DeleteWorkSpaceButtonProps) => {
  const d = useDictionary();
  const { pathname } = useLocation();
  const { workspaceKey } = useParams();
  const { data: workspaces } = useGetWorkspaces();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { updateLastActiveTabs } = useAuth();
  const [isDeleting, setIsDeleting] = React.useState(false);

  const versionApiPath = useMemo(() => {
    return workspace ? `/workspaces/${workspace.id}` : "";
  }, [workspace]);

  const { mutate: softDeleteWorkspace } = useMutation({
    mutationFn: () => deleteData(versionApiPath, {}),
    onSuccess: () => {
      if (workspaces && workspaces.length === 1) {
        navigate(routeConstants.creationWorkspace);
      } else {
        navigate(
          generatePath(routeConstants.home, {
            workspaceKey: workspaces.find(
              (x: Workspace) => x.key != workspace.key
            )?.key
          })
        );
        queryClient.invalidateQueries({ queryKey: [WORKSPACES_QUERY_KEY] });
      }
      updateLastActiveTabs({});
    },
    onError: () => {
      console.error("Failed to delete WorkSpace");
    }
  });

  if (
    !pathname.startsWith(
      generatePath(routeConstants.settings, { workspaceKey })
    ) ||
    !workspace
  ) {
    return <></>;
  }

  return (
    <>
      <Button
        variant="text"
        leftIcon={commonIcons.delete}
        label={d("ui.workspace.delete.button")}
        traffic={ButtonTraffic.DANGER}
        onClick={() => {
          setIsDeleting(true);
        }}
      />
      {isDeleting && (
        <ConfirmDeleteWorkspaceModal
          workspace={workspace}
          onConfirm={() => {
            setIsDeleting(false);
            softDeleteWorkspace();
          }}
          onCancel={() => setIsDeleting(false)}
        />
      )}
    </>
  );
};

const ConfirmDeleteWorkspaceModal = ({
  workspace,
  onConfirm,
  onCancel = () => {}
}: {
  workspace: Workspace;
  onConfirm: () => void;
  onCancel?: () => void;
}) => {
  const d = useDictionary();

  return (
    <ModalDialog isOpen>
      <Overlay isOpen />
      <ConfirmationModal
        heading={d("ui.workspace.delete.modal.title")}
        message={d("ui.workspace.delete.modal.message", {
          workspaceName: workspace.name
        })}
        confirmLabel={d("ui.workspace.delete.modal.confirmLabel")}
        onConfirm={onConfirm}
        onCancel={() => onCancel?.()}
        variant="danger"
      />
    </ModalDialog>
  );
};
