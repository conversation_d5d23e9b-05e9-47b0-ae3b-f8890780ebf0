import React, { useMemo } from "react";

import { But<PERSON>, <PERSON><PERSON><PERSON>ra<PERSON><PERSON> } from "@oneteam/onetheme";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  generatePath,
  useLocation,
  useNavigate,
  useParams
} from "react-router-dom";

import { deleteData } from "@helpers/deleteData";

import { routeConstants } from "@src/constants/routeConstants";
import { useAuth } from "@src/hooks/useAuth";
import { useDictionary } from "@src/hooks/useDictionary";
import {
  WORKSPACES_QUERY_KEY,
  useGetWorkspaces
} from "@src/hooks/useGetWorkspaces";
import { Workspace } from "@src/types/workspace.ts";

export interface DeleteWorkSpaceButtonProps {
  workspace: Workspace;
}

export const DeleteWorkspaceButton = ({
  workspace
}: DeleteWorkSpaceButtonProps) => {
  const d = useDictionary();
  const { pathname } = useLocation();
  const { workspaceKey } = useParams();
  const { data: workspaces } = useGetWorkspaces();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { updateLastActiveTabs } = useAuth();

  const versionApiPath = useMemo(() => {
    return workspace ? `/workspaces/${workspace.id}` : "";
  }, [workspace]);

  const { mutate: softDeleteWorkspace } = useMutation({
    mutationFn: () => deleteData(versionApiPath, {}),
    onSuccess: () => {
      if (workspaces && workspaces.length === 1) {
        navigate(routeConstants.creationWorkspace);
      } else {
        navigate(
          generatePath(routeConstants.home, {
            workspaceKey: workspaces.find(
              (x: Workspace) => x.key != workspace.key
            )?.key
          })
        );
        queryClient.invalidateQueries({ queryKey: [WORKSPACES_QUERY_KEY] });
      }
      updateLastActiveTabs({});
    },
    onError: () => {
      console.error("Failed to delete WorkSpace");
    }
  });

  if (
    !pathname.startsWith(
      generatePath(routeConstants.settings, { workspaceKey })
    ) ||
    !workspace
  ) {
    return <></>;
  }

  return (
    <Button
      variant="text"
      leftIcon={{ name: "delete" }}
      label={d("ui.workspace.delete.button")}
      traffic={ButtonTraffic.DANGER}
      onClick={() => {
        const check = confirm(d("ui.workspace.delete.confirm"));
        if (check) {
          softDeleteWorkspace();
        }
      }}
    />
  );
};
