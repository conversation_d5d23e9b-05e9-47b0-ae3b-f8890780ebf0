import React, { useState } from "react";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  ButtonGroup,
  Form,
  Inline,
  PageBodyTemplate,
  Stack
} from "@oneteam/onetheme";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFormContext } from "react-hook-form";
import { useOutletContext } from "react-router-dom";

import { CustomError } from "@helpers/errorHelper";
import { patchData } from "@helpers/patchData";

import { NameKeyFormField } from "@components/shared/NameKeyFormField/NameKeyFormField";

import { DeleteWorkspaceButton } from "@pages/settings/details/DeleteWorkspaceButton";
import { DocChange } from "@pages/workspace/WorkspaceLayout";

import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs.tsx";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { WORKSPACES_QUERY_KEY } from "@src/hooks/useGetWorkspaces.tsx";
import { buildShortPageTitle, usePageTitle } from "@src/hooks/usePageTitle";
import { WorkspaceDocument } from "@src/types/documentTypes";
import {
  Workspace,
  WorkspaceForCreateOrUpdate,
  getWorkspaceSchema
} from "@src/types/workspace";

export const SettingsDetails = () => {
  const d = useDictionary();
  const breadcrumbs = useBreadcrumbs();
  const workspaceSchema = getWorkspaceSchema(d);
  const { workspace } = useOutletContext<{
    workspace: Workspace;
  }>();

  const workspaceForUpdate = {
    name: workspace.name,
    key: workspace.key,
    description: workspace.description
  } as WorkspaceForCreateOrUpdate;

  usePageTitle(
    buildShortPageTitle({
      section: d("ui.settings.details.title"),
      context: workspace?.key ?? ""
    }),
    !!workspace?.key
  );

  return (
    <PageBodyTemplate
      heading={d("ui.settings.details.title")}
      breadcrumbs={breadcrumbs}
    >
      <Stack
        gap="100"
        width="maxContent"
        contentsWidth="100"
        style={{ width: "350px", maxWidth: "100%" }}
      >
        <Form
          hideFormButtons
          d={useDictionary()}
          handleSubmit={() => {}}
          schema={workspaceSchema}
          submitLabel={d("ui.common.save")}
          defaultValues={workspaceForUpdate}
        >
          <UpdateWorkspaceFields workspace={workspace} />
        </Form>
      </Stack>
    </PageBodyTemplate>
  );
};

interface UpdateWorkspaceFieldsProps {
  workspace: Workspace;
}

const UpdateWorkspaceFields = ({ workspace }: UpdateWorkspaceFieldsProps) => {
  const d = useDictionary();
  const formContext = useFormContext();
  const [serverError, setServerError] = useState<CustomError | undefined>();
  const [updateSuccessful, setUpdateSuccessful] = useState(false);

  const { docChange } = useOutletContext<{
    document: WorkspaceDocument;
    docChange: DocChange;
  }>();

  const { watch } = useFormContext();

  const newWorkspaceName = watch("name");
  const newWorkspaceDescription = watch("description");

  const clearAlerts = () => {
    setUpdateSuccessful(false);
  };

  const queryClient = useQueryClient();

  const { mutate: updateWorkspace } = useMutation({
    mutationFn: async () => {
      clearAlerts();
      if (
        newWorkspaceName === workspace.name &&
        newWorkspaceDescription === workspace.description
      ) {
        setUpdateSuccessful(true);
        return Promise.resolve();
      }
      return patchData(`/workspaces/${workspace.id}`, {
        id: workspace.id,
        name: newWorkspaceName ?? workspace.name,
        key: workspace.key,
        description: newWorkspaceDescription ?? workspace.description
      });
    },
    onSuccess: data => {
      // user didn't make any change
      if (
        newWorkspaceName === workspace.name &&
        newWorkspaceDescription === workspace.description
      ) {
        return;
      }
      setUpdateSuccessful(true);
      docChange((doc: WorkspaceDocument) => {
        doc.name = data.name;
        doc.description = data.description;
        workspace.name = data.name;
        workspace.description = data.description;
        workspace.key = data.key;
      });
      queryClient.invalidateQueries({ queryKey: [WORKSPACES_QUERY_KEY] });
    },
    onError: (error: CustomError) => {
      setServerError(error);
    }
  });

  if (serverError?.errors?.length) {
    formContext?.clearErrors();
    serverError.errors.forEach(e => {
      formContext?.setError(e.field, { message: d(e.key) });
    });
  }

  return (
    <Stack gap="150" width="100" contentsWidth="100">
      <NameKeyFormField
        nameField={{ label: d("ui.workspace.fields.name.label") }}
        keyField={{
          label: d("ui.workspace.fields.key.label"),
          tooltip: d("ui.workspace.fields.key.tooltip"),
          disabled: true // Key is not editable in settings
        }}
      />
      <Form.TextAreaField
        name="description"
        label={d("ui.workspace.fields.description.label")}
      />
      <Inline spaceBetween gap="100">
        <DeleteWorkspaceButton workspace={workspace} />
        <ButtonGroup>
          <Button
            type="submit"
            label={d("ui.common.save")}
            onClick={() => {
              setServerError(undefined);
              updateWorkspace();
            }}
          />
        </ButtonGroup>
      </Inline>
      {updateSuccessful && (
        <Alert variant={AlertVariant.SUCCESS}>
          {d("ui.workspace.update.success")}
        </Alert>
      )}
    </Stack>
  );
};
