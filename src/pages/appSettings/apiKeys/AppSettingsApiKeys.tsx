import React, { useCallback } from "react";

import {
  Button,
  ButtonGroup,
  ConfirmationModal,
  DropdownItem,
  DropdownItemGroup,
  FloatingWithParentPosition,
  Icon,
  Inline,
  KebabMenu,
  ModalDialog,
  Overlay,
  PageBodyTemplate,
  SearchBar,
  TableColumn,
  TableKebabMenuFormatter,
  TableWithPagination,
  Text,
  TextAlignment,
  Tooltip
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { buildQueryParams } from "@helpers/pagination";

import { formatDate } from "@pages/collection/flows/FlowExecutionHelper";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import { buildShortPageTitle, usePageTitle } from "@src/hooks/usePageTitle.ts";
import { usePaginatedSearchParams } from "@src/hooks/usePaginatedSearchParams";
import { ApiKey } from "@src/types/ApiKeys";

import { PushToastNotification } from "../AppSettingsLayout";
import { ApiKeyGenerateModal } from "./ApiKeyGenerateModal";
import "./appSettingsApiKeys.scss";
import {
  buildParamsForApiKeysSearch,
  useApiKeysSearch,
  useDeleteApiKey
} from "./helpers/apiKeysService";

export const AppSettingsApiKeys = () => {
  const d = useDictionary();
  const [apiKeyForDeletion, setApiKeyForDeletion] = React.useState<
    ApiKey | undefined
  >(undefined);
  const { pushToastNotification } = useOutletContext<{
    pushToastNotification: PushToastNotification;
  }>();
  const [createApiKeyModalOpen, setCreateApiKeyModalOpen] =
    React.useState<boolean>(false);

  usePageTitle(
    buildShortPageTitle({
      section: d("ui.appSettings.apiKeys.title"),
      context: d("ui.appSettings.title")
    }),
    true
  );

  const columns = columnDefinitions(d);
  const {
    sort,
    searchTerm,
    page,
    pageSize,
    asc,
    dsc,
    onChangeSorting,
    onChangePage,
    onChangeSearchTerm
  } = usePaginatedSearchParams<ApiKey>({
    columns
  });

  const { data: apiKeys, isLoading } = useApiKeysSearch(
    buildParamsForApiKeysSearch(
      searchTerm,
      buildQueryParams(
        page ? Number(page) : undefined,
        pageSize ? Number(pageSize) : undefined,
        asc,
        dsc
      )
    )
  );

  const { mutateAsync: deleteApiKey, isPending: isDeleting } = useDeleteApiKey({
    d,
    pushToastNotification
  });

  const kebabMenu: TableKebabMenuFormatter<ApiKey> = useCallback(
    ({ row, props, closeMenu }) => {
      return (
        <KebabMenu {...props}>
          <DropdownItemGroup>
            <DropdownItem
              id="delete"
              leftElement={<Icon name="delete" color="traffic-danger" />}
              onClick={() => {
                setApiKeyForDeletion(row);
                closeMenu();
              }}
            >
              <Text color="traffic-onDanger">{d("ui.common.delete")}</Text>
            </DropdownItem>
          </DropdownItemGroup>
        </KebabMenu>
      );
    },
    [d]
  );

  const renderConfirmDeleteModal = useCallback(() => {
    if (!apiKeyForDeletion) {
      return <></>;
    }

    return (
      <ConfirmDeletionModal
        apiKey={apiKeyForDeletion}
        onConfirm={() => {
          if (isDeleting) {
            return;
          }
          deleteApiKey(apiKeyForDeletion).finally(() => {
            setApiKeyForDeletion(undefined);
          });
        }}
        onCancel={() => setApiKeyForDeletion(undefined)}
      />
    );
  }, [apiKeyForDeletion, deleteApiKey, isDeleting]);

  const renderGenerateModal = useCallback(() => {
    if (!createApiKeyModalOpen) {
      return <></>;
    }

    return (
      <ApiKeyGenerateModal
        onOpenChange={() => setCreateApiKeyModalOpen(false)}
      />
    );
  }, [createApiKeyModalOpen]);

  const startCreateWizard = useCallback(() => {
    setCreateApiKeyModalOpen(true);
  }, []);

  return (
    <PageBodyTemplate heading={d("ui.appSettings.apiKeys.title")} withoutScroll>
      <Inline gap="100" spaceBetween>
        <Inline gap="100" alignment="left">
          <SearchBar
            withDebounce
            handleChange={onChangeSearchTerm}
            placeholder={d("ui.common.search")}
            value={searchTerm ?? ""}
            autoFocus
          />
        </Inline>
        <ButtonGroup alignment="right">
          <Button
            label={d("ui.appSettings.apiKeys.create.actionButton")}
            onClick={startCreateWizard}
          ></Button>
        </ButtonGroup>
      </Inline>

      <TableWithPagination
        isLoading={isLoading}
        fillContainer
        columns={columns}
        data={apiKeys?.items ?? []}
        itemsPerPage={apiKeys?.page?.pageSize ?? 10}
        startingPage={page ? Number(page) : 1}
        totalCount={apiKeys?.total ?? 0}
        noDataText={d("ui.common.noData")}
        isControlledOutside={true}
        onChangePage={onChangePage}
        handleSort={onChangeSorting}
        sort={sort}
        kebabMenu={kebabMenu}
        rowKeyAccessor="id"
      />
      {renderGenerateModal()}
      {renderConfirmDeleteModal()}
    </PageBodyTemplate>
  );
};

function columnDefinitions(d: Dictionary): TableColumn<ApiKey>[] {
  return [
    {
      header: d("ui.appSettings.apiKeys.fields.name.label"),
      key: "name",
      canSort: true
    },
    {
      header: d("ui.appSettings.apiKeys.fields.description.label"),
      key: "description",
      canSort: true
    },
    {
      header: d("ui.appSettings.apiKeys.fields.createdAt.label"),
      key: "metadata.createdAt",
      canSort: true,
      options: {
        alignment: TextAlignment.RIGHT
      },
      formatter: (data: ApiKey) => (
        <Inline alignment="left" gap="100">
          {data.metadata && (
            <Tooltip
              content={data.metadata.updatedAt}
              delay={500}
              position={FloatingWithParentPosition.BOTTOM_RIGHT}
            >
              <Text size="s" color="text-tertiary">
                {formatDate(data.metadata.updatedAt)}
              </Text>
            </Tooltip>
          )}
        </Inline>
      )
    }
  ];
}

const ConfirmDeletionModal = ({
  apiKey,
  onConfirm,
  onCancel = () => {}
}: {
  apiKey: ApiKey;
  onConfirm: () => void;
  onCancel?: () => void;
}) => {
  const d = useDictionary();

  return (
    <ModalDialog isOpen>
      <Overlay isOpen />
      <ConfirmationModal
        heading={d("ui.appSettings.apiKeys.delete.confirmation.title")}
        message={d("ui.appSettings.apiKeys.delete.confirmation.message", {
          apiKeyName: apiKey.name
        })}
        confirmLabel={d(
          "ui.appSettings.apiKeys.delete.confirmation.confirmButton"
        )}
        onConfirm={onConfirm}
        onCancel={() => onCancel?.()}
        variant="danger"
      />
    </ModalDialog>
  );
};
