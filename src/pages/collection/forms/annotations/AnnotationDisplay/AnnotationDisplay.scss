.annotation-display {
  overflow: hidden;
  &__message {
    user-select: text;
  }
  & .text.annotation-display__location {
    font-size: var(--font-size-body-s);
  }
  & .annotation-display__kebab-menu,
  & .annotation-display__resolve-button {
    opacity: 0;
    &--open {
      opacity: 1;
    }
  }
  & .annotation-display__resolve-button--resolved {
    opacity: 1;
  }
  &:hover {
    .annotation-display__kebab-menu,
    .annotation-display__resolve-button {
      opacity: 1;
    }
  }

  & .alert-avatar-display {
    max-width: var(--components-avatar-s-size, 24px);
    max-height: var(--components-avatar-s-size, 24px);
    background: transparent;
    border: 0px solid transparent;
  }
  & textarea {
    word-break: break-all;
  }

  & span.annotation-display__edited {
    padding-left: var(--spacing-050);
    font-size: var(--font-size-body-s);
    color: var(--color-text-tertiary);
  }
}
