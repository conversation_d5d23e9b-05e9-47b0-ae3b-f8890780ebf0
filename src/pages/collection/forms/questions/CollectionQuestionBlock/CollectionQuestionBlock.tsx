import React, {
  LegacyRef,
  RefObject,
  useCallback,
  useMemo,
  useRef
} from "react";

import {
  Box,
  Inline,
  Stack,
  StatusLine,
  StatusLineVariant,
  getClassNames,
  returnStringIfTrue
} from "@oneteam/onetheme";

import { isQuestionAnswered } from "@helpers/forms/formHelper";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";
import { QuestionAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionAnswer";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { TableCellLocation } from "@src/types/AnnotationLocation";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  JSONQuestionProperties,
  ListQuestionProperties
} from "@src/types/QuestionProperties";
import {
  CollectionFormSummaryModalTab,
  FormAnswer,
  JsonAnswer,
  ListAnswer
} from "@src/types/collection/CollectionForm";

import "../../../../../components/shared/OTAIForm/OTAIFormField/OTAIFormFieldTable.scss";
import { HighlightAnnotationLocation } from "../../HighlightAnnotationLocation/HighlightAnnotationLocation";
import { LabelWithHighlight } from "../../LabelWithHighlight";
import "./CollectionQuestionBlock.scss";
import { CollectionQuestionBlockAlertFloating } from "./CollectionQuestionBlockAlert/CollectionQuestionBlockAlertFloating/CollectionQuestionBlockAlertFloating";
import { CollectionQuestionBlockCommentFloating } from "./CollectionQuestionBlockComment/CollectionQuestionBlockCommentFloating/CollectionQuestionBlockCommentFloating";
import { QuestionJsonAnswer } from "./QuestionJsonAnswer";
import { QuestionListAnswer } from "./QuestionListAnswer";

export const CollectionQuestionBlock = ({
  d,
  question
}: {
  d: Dictionary;
  question?: Question;
}) => {
  const answerAccessor = useMemo(() => `answers.${question?.id}`, [question]);

  const { formData, getFieldState, summaryModal } = useCollectionFormContext();
  const answer = question ? formData?.answers?.[question.id] : undefined;

  const answerFieldState = getFieldState?.(answerAccessor);

  const questionRef = useRef<HTMLElement>();
  const leftRef = useRef<HTMLTableCellElement>(null);

  const statusLineVariant: StatusLineVariant = useMemo(() => {
    if (answerFieldState?.error) {
      return StatusLineVariant.DANGER;
    }
    if (isQuestionAnswered(question, answer)) {
      return StatusLineVariant.COMPLETED;
    }
    return StatusLineVariant.NOT_STARTED;
  }, [answer, answerFieldState?.error, question]);

  const statusLine = useCallback(
    (showStatusCircle: boolean = true) => (
      <td
        style={{
          width:
            "calc(var(--components-status-line-width, 6px) + var(--spacing-100))",
          minWidth:
            "calc(var(--components-status-line-width, 6px) + var(--spacing-100))"
        }}
        ref={showStatusCircle ? leftRef : undefined}
      >
        {/* Question alerts */}
        {question && showStatusCircle && (
          <CollectionQuestionBlockAlertFloating
            location={{
              variant: "question",
              questionId: question.id
            }}
            parentRef={leftRef as RefObject<HTMLElement>}
          />
        )}
        <Box>
          <StatusLine
            variant={statusLineVariant}
            className="collection-question-block__status"
          />
        </Box>
      </td>
    ),
    [question, statusLineVariant]
  );

  const isSelected = useMemo(() => {
    if (
      summaryModal.location?.variant === "question" &&
      summaryModal.location?.questionId === question?.id &&
      !(summaryModal.location as TableCellLocation)?.rowId &&
      !(summaryModal.location as TableCellLocation)?.columnId
    ) {
      return true;
    }
    return false;
  }, [question, summaryModal.location]);

  const openQuestion = useCallback(() => {
    if (!question) {
      return;
    }
    if (isSelected) {
      summaryModal.setTab("none");
      summaryModal.setLocation(undefined);
      return;
    }
    if (summaryModal.tab === "none") {
      summaryModal.setTab(CollectionFormSummaryModalTab.ALL);
    }
    summaryModal.setLocation({
      variant: "question",
      questionId: question?.id
    });
  }, [summaryModal, question, isSelected]);

  if (!question) {
    return <></>;
  }

  return (
    <>
      <tr
        ref={questionRef as LegacyRef<HTMLTableRowElement>}
        key={question.id}
        className={getClassNames([
          "collection-question-block",
          `collection-question-block--type-${question.type}`,
          returnStringIfTrue(isSelected, "collection-question-block--selected")
        ])}
        onClick={() => openQuestion()}
      >
        {statusLine()}
        <td
          style={{ width: "50%" }}
          className="collection-question-block__data__cell"
        >
          <Stack gap="025">
            {question.type !== QuestionTypes.JSON && (
              <HighlightAnnotationLocation
                location={{
                  variant: "question",
                  questionId: question.id
                }}
              >
                <LabelWithHighlight
                  label={question.text}
                  description={question.description}
                  required={question.properties?.required}
                  tooltip={question?.tooltip}
                  className="collection-question-block__label"
                />
              </HighlightAnnotationLocation>
            )}
          </Stack>
        </td>
        <td
          style={{ width: "250px", maxWidth: "250px", position: "relative" }}
          className="collection-question-block__data__cell"
        >
          <Inline
            className="collection-question-block__answer"
            alignment="right"
            gap="050"
            contentsWidth="100"
            onClick={e => e.stopPropagation()}
          >
            {question.type === QuestionTypes.LIST ? (
              <QuestionListAnswer
                d={d}
                question={question as Question<ListQuestionProperties>}
                answer={answer as FormAnswer<ListAnswer>}
                answerAccessor={`${answerAccessor}.value`}
                disableAddRow={question.properties?.disabled}
              />
            ) : (
              question.type !== QuestionTypes.TABLE && (
                <QuestionAnswer
                  question={question}
                  answer={answer?.value}
                  answerAccessor={`${answerAccessor}.value`}
                  disabled={question.properties?.disabled}
                  location={{
                    variant: "answer",
                    questionId: question.id
                  }}
                />
              )
            )}
          </Inline>
          {/* Question comments */}
          <CollectionQuestionBlockCommentFloating
            location={{
              variant: "question",
              questionId: question.id
            }}
            parentRef={questionRef as RefObject<HTMLElement>}
          />
        </td>
      </tr>
      {question.type === QuestionTypes.TABLE && (
        <tr
          className={getClassNames([
            "collection-question-block__data__cell collection-question-block__table",
            returnStringIfTrue(
              isSelected,
              "collection-question-block--selected"
            )
          ])}
          style={{
            position: "relative"
          }}
        >
          {statusLine(false)}
          <td className="collection-question-block__td" colSpan={3}>
            <QuestionAnswer
              question={question}
              answer={answer?.value}
              answerAccessor={`${answerAccessor}.value`}
              disabled={question.properties?.disabled}
              location={{
                variant: "answer",
                questionId: question.id
              }}
            />
          </td>
        </tr>
      )}
      {question.type === QuestionTypes.JSON && (
        <tr
          className={getClassNames([
            "collection-question-block__data__cell collection-question-block__json",
            returnStringIfTrue(
              isSelected,
              "collection-question-block--selected"
            )
          ])}
          style={{
            position: "relative"
          }}
        >
          <QuestionJsonAnswer
            d={d}
            statusLine={statusLine()}
            question={question as Question<JSONQuestionProperties>}
            answer={(answer as FormAnswer<JsonAnswer>)?.value}
            answerAccessor={`${answerAccessor}.value`}
          />
        </tr>
      )}
    </>
  );
};

CollectionQuestionBlock.displayName = "CollectionQuestionBlock";
