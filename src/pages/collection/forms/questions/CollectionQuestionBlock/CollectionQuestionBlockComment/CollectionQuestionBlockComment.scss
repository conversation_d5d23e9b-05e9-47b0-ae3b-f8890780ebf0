.collection-question-block-comment {
  transition: none;

  &__container {
    &--cellIndicator {
      position: absolute !important;
      top: 0;
      right: 0;
    }
    &:has(.collection-question-block-comment--open) {
      z-index: 30 !important;
    }
  }
  &__icon {
    border: 1px solid var(--components-status-circle-color-border);
    background: var(--components-status-circle-color-background, #fff);
    border-radius: var(--components-status-circle-size, 32px);
    width: var(--components-status-circle-size, 32px);
    height: var(--components-status-circle-size, 32px);
    cursor: pointer;

    &--with-annotations {
      background: var(--color-color);
    }
  }

  &__cell-indicator {
    width: var(--spacing-050);
    height: var(--spacing-050);
    cursor: pointer;
    &::before {
      /* we need this to create the pseudo-element */
      content: "";
      display: block;

      /* position the triangle in the top right corner */
      position: absolute;
      z-index: 0;
      top: 0;
      right: 0;

      /* create the triangle */
      width: 0;
      height: 0;
      border: var(--spacing-075) solid transparent;
      border-top-color: var(--components-status-circle-comment-color-icon);
      border-right-color: var(--components-status-circle-comment-color-icon);
      opacity: 0.5;
    }
    &--with-annotations {
      &::before {
        opacity: 1;
      }
    }
  }

  &--open {
    & .collection-question-block-comment__icon,
    & .status-circle-group__item {
      box-shadow: 0px 3px 6px rgba(39, 52, 75, 0.2);
    }
  }
}

.table-question__table__header__cell,
.table-question__table__data__cell {
  transition: none;
  .collection-question-block-comment {
    visibility: hidden;
    opacity: 0;
    &--with-annotations,
    &--open {
      opacity: 1;
      visibility: visible;
    }
  }
  &.table-question__table__data__cell--actions,
  &:hover,
  &:focus-within {
    .collection-question-block-comment {
      visibility: visible;
      opacity: 1;
    }
  }
}
