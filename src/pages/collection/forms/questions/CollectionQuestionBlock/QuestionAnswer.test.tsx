import React from "react";

import { Form } from "@oneteam/onetheme";
import { act, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it } from "vitest";

import { QuestionTypes } from "../../../../../types/Question.ts";
import { QuestionAnswer } from "./QuestionAnswer";

const placeholderText = "Enter a number";

describe("QuestionAnswer", () => {
  // 2 tests should be good to catch any flicker issue
  for (let i = 0; i < 2; i++) {
    // This test emulates a scenario where the answer is updated successfully in the document (mock),
    // and it takes a few milliseconds for the document to reflect that change.
    // The test checks that the input value does not flicker back to the initial value
    // during this update period.
    it(`should not flicker input value when answerFromDoc updates (run ${i + 1})`, async () => {
      const question = {
        id: "q1",
        identifier: "Number",
        type: QuestionTypes.NUMBER,
        properties: {
          allowReuseAcrossForms: false,
          max: 12,
          min: 2,
          placeholder: placeholderText,
          required: false
        },
        text: "Number"
      };
      let answer = "123";
      const initialAnswer = answer;
      const newValue = "88888";
      const answerAccessor = "q1";
      const inputValues: string[] = [];

      const { rerender } = render(
        <Form d={(str: string) => str}>
          <QuestionAnswer
            question={question}
            answer={answer}
            answerAccessor={answerAccessor}
          />
        </Form>
      );

      const input = screen.getByPlaceholderText(
        placeholderText
      ) as HTMLInputElement;

      // Poll input value every 10ms during the test
      const poller = setInterval(() => {
        inputValues.push(input.value);
      }, 6);

      // Simulate user change
      await act(async () => {
        await userEvent.clear(input);
        await userEvent.type(input, newValue);
      });

      // Record after user change
      inputValues.push(input.value);

      await waitFor(async () => {
        await userEvent.tab();
      });

      // delay for update from doc
      await new Promise(resolve => setTimeout(resolve, 9));

      // Simulate parent updating answerFromDoc after async update
      answer = newValue;
      rerender(
        <Form d={(str: string) => str}>
          <QuestionAnswer
            question={question}
            answer={answer}
            answerAccessor={answerAccessor}
          />
        </Form>
      );

      // Wait a bit for rerender and polling. 3ms is enough but we use 10ms to be safe in pipelines
      await new Promise(resolve => setTimeout(resolve, 10));
      await waitFor(() => inputValues[inputValues.length - 1] == newValue);
      clearInterval(poller);

      // Assert that input value never flickers back to initialAnswer
      expect(inputValues).not.toContain(initialAnswer);
    });
  }
});
