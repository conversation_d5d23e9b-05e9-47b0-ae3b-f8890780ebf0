import React, { useCallback, useMemo } from "react";

import {
  ColorText,
  FontWeight,
  Icon,
  Inline,
  Pill,
  TableWithPagination,
  Text,
  TextAlignment,
  TextSize,
  Tooltip,
  useQueryParams,
  useTableSort
} from "@oneteam/onetheme";
import { get } from "lodash";
import { useNavigate, useSearchParams } from "react-router-dom";

import { getFormUrl } from "@helpers/forms/formHelper";
import {
  buildQueryParamsFromUrl,
  mergeQueryParams
} from "@helpers/pagination.ts";
import { WorkspaceConfigurationHelper } from "@helpers/workspace/WorkspaceConfigurationHelper.ts";

import { CollectionsFoundationsTabs } from "@pages/collection/home/<USER>";
import {
  HomeHelper,
  useFormSearch
} from "@pages/collection/home/<USER>";

import { commonIcons } from "@src/constants/iconConstants";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { ExistingForm } from "@src/types/Form.ts";
import { Foundation } from "@src/types/Foundation.ts";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";
import { Page } from "@src/types/Page";
import { Workspace } from "@src/types/workspace.ts";

import { formatDate } from "../flows/FlowExecutionHelper";
import { PropertyValueRenderer } from "./PropertyValueRenderer/PropertyValueRenderer";

interface CollectionsFormListProps {
  workspace: Workspace;
  foundation: Foundation;
  foundationConfiguration?: FoundationConfiguration | undefined | null;
  tab: string;
  workspaceVersion: WorkspaceConfigurationHelper;
  homeHelper?: HomeHelper;
}

export const CollectionsFormList = ({
  workspace,
  foundation,
  foundationConfiguration,
  tab,
  workspaceVersion,
  homeHelper
}: CollectionsFormListProps) => {
  const d = useDictionary();
  const navigate = useNavigate();

  const { queryParams, updateQueryParams } = useQueryParams({
    initialDefaultIfNone: { page: "1" },
    useSearchParams
  });

  const doUpdateQueryParams = useCallback(
    (newParams: { [key: string]: string | undefined }) => {
      updateQueryParams(mergeQueryParams(queryParams, newParams));
    },
    [updateQueryParams, queryParams]
  );

  const { textFilter, page } = useMemo(
    () => ({
      textFilter: queryParams.get("search") ?? undefined,
      page: queryParams.get("page") ?? undefined,
      asc: queryParams.get("asc") ?? undefined,
      dsc: queryParams.get("dsc") ?? undefined
    }),
    [queryParams]
  );

  // load forms for the selected foundation
  const { data: forms } = useFormSearch(
    workspace.id,
    [`forms-${tab}`, queryParams.toString(), workspace.id],
    CollectionsFoundationsTabs.getUrlParamsForFormSearch(
      tab,
      foundation.id,
      textFilter,
      buildQueryParamsFromUrl(queryParams)
    )
  ) as { data: Page<ExistingForm> };

  const additionalPropertiesColumns = useMemo(() => {
    const keys =
      forms?.items?.flatMap(form => Object.keys(form.properties ?? {})) ?? [];
    const uniqueKeys = Array.from(new Set(keys)) ?? [];
    return uniqueKeys.map(key => ({
      header: key.split("_").join(" "),
      key: `properties.${key}`,
      canSort: true,
      formatter: (form: ExistingForm) => {
        const value = get(form, `properties.${key}`, undefined);
        return <PropertyValueRenderer value={value} />;
      }
    }));
  }, [forms?.items]);

  const getFormColumns = useCallback(() => {
    return [
      {
        header: "",
        key: "type",
        canSort: false,
        formatter: () => <Icon {...commonIcons.forms} color="text-secondary" />,
        options: {
          width: "var(--icon-size-m)"
        }
      },
      {
        header: d("ui.terminology.foundation"),
        key: "name",
        canSort: true,
        formatter: (form: ExistingForm) => (
          <Inline alignment="left" gap="050" wrap>
            <Text weight={FontWeight.MEDIUM}>{form.foundation.name}</Text>
            <Pill label={form.foundation.key} />
          </Inline>
        )
      },
      {
        header: d("ui.terminology.foundationConfiguration"),
        key: "level",
        canSort: false,
        formatter: () => {
          if (!foundationConfiguration) {
            return <></>;
          }
          return <Pill label={foundationConfiguration?.name} />;
        }
      },
      {
        header: d("ui.terminology.form"),
        key: "form",
        canSort: false,
        formatter: (form: ExistingForm) => {
          const formConfiguration = workspaceVersion.getForm(
            form.formConfigurationId
          );
          const interval = form.intervalId
            ? workspaceVersion.getIntervalById(form.intervalId)
            : undefined;
          return (
            <Inline alignment="left" gap="050" wrap>
              <Text>{formConfiguration?.name ?? ""}</Text>
              <Pill label={formConfiguration?.key ?? ""} />
              {interval && <Pill label={interval?.name ?? ""} />}
            </Inline>
          );
        }
      },
      ...additionalPropertiesColumns,
      // {
      //   header: d("ui.forms.progress"),
      //   key: "progress",
      //   formatter: (data: Forms) => (
      //     <ProgressBar
      //       message={`${data.progress.completed}/${data.progress.total} answered`}
      //       percentage={(data.progress.completed / data.progress.total) * 100}
      //     />
      //   )
      // },
      // {
      //   header: d("ui.forms.fields.status.label"),
      //   key: "status",
      //   canSort: true,
      //   formatter: (data: Forms) => (
      //     <Pill
      //       label={data.status}
      //       background={PillBackground.PROMINENT}
      //       variant={variants[data.status]}
      //     />
      //   )
      // },
      {
        header: d("ui.common.lastModified"),
        key: "metadata.updatedAt",
        formatter: (data: ExistingForm) => (
          <Inline alignment="left" gap="100">
            {data.metadata && (
              <Tooltip content={data.metadata.updatedAt} delay={500}>
                <Text size="s" color="text-tertiary" alignment="right">
                  {formatDate(data.metadata.updatedAt)}
                </Text>
              </Tooltip>
            )}
          </Inline>
        ),
        canSort: true,
        options: {
          alignment: TextAlignment.RIGHT,
          color: ColorText.TERTIARY,
          size: TextSize.S
        }
      }
    ];
  }, [
    d,
    foundationConfiguration,
    additionalPropertiesColumns,
    workspaceVersion
  ]);

  const formColumns = getFormColumns();

  const { sort, handleSetSort } = useTableSort<ExistingForm>({
    columns: formColumns,
    queryParams,
    updateQueryParams: (newParams: { [key: string]: string | undefined }) => {
      doUpdateQueryParams({
        page: newParams.page,
        search: textFilter,
        asc: newParams.asc,
        dsc: newParams.dsc
      });
    }
  });

  const onChangePage = useCallback(
    (newPage: number) => {
      doUpdateQueryParams({ page: String(newPage) });
    },
    [doUpdateQueryParams]
  );

  return (
    <>
      <TableWithPagination
        fillContainer
        columns={formColumns}
        data={forms?.items ?? []}
        itemsPerPage={forms?.page.pageSize}
        startingPage={page ? parseInt(page) : 1}
        noDataText={d("ui.common.noData")}
        totalCount={forms?.total ?? 0}
        isControlledOutside={true}
        onChangePage={onChangePage}
        handleSort={handleSetSort}
        sort={sort}
        rowKeyAccessor="id"
        onRowClick={(form: ExistingForm) => {
          const { basePath, formIdentifier } = getFormUrl({
            form,
            workspaceVersion,
            tab,
            homeHelper
          });
          navigate(basePath + "/form" + `/${formIdentifier}`);
        }}
      />
    </>
  );
};

CollectionsFormList.displayName = "CollectionsFormList";
