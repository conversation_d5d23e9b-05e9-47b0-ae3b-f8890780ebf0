import React, { useCallback, useMemo } from "react";

import {
  ColorText,
  DropdownItem,
  DropdownItemGroup,
  FontWeight,
  Icon,
  Inline,
  KebabMenu,
  Pill,
  TableKebabMenuFormatter,
  TableWithPagination,
  Text,
  TextAlignment,
  TextSize,
  Tooltip,
  useQueryParams,
  useTableSort
} from "@oneteam/onetheme";
import { get } from "lodash";
import { useNavigate, useSearchParams } from "react-router-dom";

import {
  buildQueryParamsFromUrl,
  mergeQueryParams
} from "@helpers/pagination.ts";

import { CollectionsFoundationsTabs } from "@pages/collection/home/<USER>";
import { SELECTED_FOUNDATIONS_KEY } from "@pages/collection/home/<USER>";
import {
  HomeHelper,
  useFoundationsSearch
} from "@pages/collection/home/<USER>";

import { commonIcons } from "@src/constants/iconConstants";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { Foundation } from "@src/types/Foundation.ts";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";
import { Workspace } from "@src/types/workspace.ts";

import { formatDate } from "../flows/FlowExecutionHelper";
import { PropertyValueRenderer } from "./PropertyValueRenderer/PropertyValueRenderer";

interface CollectionsFoundationListProps {
  foundation: Foundation;
  workspace: Workspace;
  foundationConfiguration: FoundationConfiguration;
  onNew: () => void;
  onEdit: (
    selectedFoundationId: number,
    currentFoundationName: string,
    currentFoundationKey: string
  ) => void;
  homeHelper?: HomeHelper;
}

export const CollectionsFoundationList = ({
  foundation,
  workspace,
  foundationConfiguration,
  onEdit,
  homeHelper
}: CollectionsFoundationListProps) => {
  const d = useDictionary();
  const navigate = useNavigate();

  const { queryParams, updateQueryParams } = useQueryParams({
    initialDefaultIfNone: { page: "1" },
    useSearchParams
  });

  const doUpdateQueryParams = useCallback(
    (newParams: { [key: string]: string | undefined }) => {
      updateQueryParams(mergeQueryParams(queryParams, newParams));
    },
    [updateQueryParams, queryParams]
  );

  const { data: foundations, isLoading } = useFoundationsSearch(
    workspace.id,
    [
      SELECTED_FOUNDATIONS_KEY,
      window.location.toString(),
      foundationConfiguration.name,
      workspace.name
    ],
    CollectionsFoundationsTabs.buildQueryParamsForFoundationSearch(
      foundation.key,
      foundation.foundationConfigurationId,
      undefined,
      buildQueryParamsFromUrl(queryParams)
    )
  );

  const additionalPropertiesColumns = useMemo(() => {
    const keys =
      foundations?.items?.flatMap(foundation =>
        Object.keys(foundation.properties ?? {})
      ) ?? [];
    const uniqueKeys = Array.from(new Set(keys)) ?? [];
    return uniqueKeys.map(key => ({
      header: key.split("_").join(" "),
      key: `properties.${key}`,
      canSort: true,
      formatter: (foundation: Foundation) => {
        const value = get(foundation, `properties.${key}`, undefined);
        return <PropertyValueRenderer value={value} />;
      }
    }));
  }, [foundations?.items]);

  const getFoundationColumns = useCallback(() => {
    return [
      {
        header: "",
        key: "type",
        canSort: false,
        formatter: () => (
          <Icon {...commonIcons.foundations} color="text-secondary" />
        ),
        options: {
          width: "var(--icon-size-m)"
        }
      },
      {
        header: d("ui.terminology.foundation"),
        key: "name",
        canSort: true,
        formatter: (data: Foundation) => (
          <Inline alignment="left" gap="050" wrap>
            <Text weight={FontWeight.MEDIUM}>{data.name}</Text>
            <Pill key={data.key} label={data.key} />
          </Inline>
        )
      },
      {
        header: d("ui.terminology.foundationConfiguration"),
        key: "level",
        canSort: false,
        formatter: () => <Pill label={foundationConfiguration.name} />
      },
      ...additionalPropertiesColumns,
      {
        header: d("ui.common.lastModified"),
        key: "metadata.updatedAt",
        formatter: (data: Foundation) => (
          <Inline alignment="left" gap="100">
            {data.metadata && (
              <Tooltip content={data.metadata.updatedAt} delay={500}>
                <Text size="s" color="text-tertiary" alignment="right">
                  {formatDate(data.metadata.updatedAt)}
                </Text>
              </Tooltip>
            )}
          </Inline>
        ),
        canSort: true,
        options: {
          alignment: TextAlignment.RIGHT,
          color: ColorText.TERTIARY,
          size: TextSize.S
        }
      }
    ];
  }, [additionalPropertiesColumns, d, foundationConfiguration]);

  const foundationColumns = getFoundationColumns();

  const { page } = useMemo(
    () => ({
      page: queryParams.get("page") ?? undefined,
      asc: queryParams.get("asc") ?? undefined,
      dsc: queryParams.get("dsc") ?? undefined
    }),
    [queryParams]
  );

  const { sort, handleSetSort } = useTableSort<Foundation>({
    columns: foundationColumns,
    queryParams,
    updateQueryParams: (newParams: { [key: string]: string | undefined }) => {
      doUpdateQueryParams({
        page: newParams.page,
        search: undefined,
        asc: newParams.asc,
        dsc: newParams.dsc
      });
    }
  });

  const onChangePage = useCallback(
    (newPage: number) => {
      doUpdateQueryParams({ page: String(newPage) });
    },
    [doUpdateQueryParams]
  );

  const rowClick = (foundation: Foundation) => {
    if (homeHelper && foundationConfiguration) {
      navigate(homeHelper.nextPath(foundation.key));
    }
  };

  const kebabMenu: TableKebabMenuFormatter<Foundation> = useCallback(
    ({ row, props, closeMenu }) => {
      return (
        <KebabMenu {...props}>
          <DropdownItemGroup>
            <DropdownItem
              id="edit"
              onClick={() => {
                onEdit(row.id, row.name, row.key);
                closeMenu();
              }}
              leftElement={<Icon name="edit" fillStyle="filled" />}
            >
              {d("ui.common.update")}
            </DropdownItem>
          </DropdownItemGroup>
        </KebabMenu>
      );
    },
    [d, onEdit]
  );

  return (
    <TableWithPagination
      isLoading={isLoading}
      fillContainer
      columns={foundationColumns}
      data={foundations?.items ?? []}
      itemsPerPage={foundations?.page.pageSize}
      startingPage={page ? parseInt(page) : 1}
      noDataText={d("ui.common.noData")}
      totalCount={foundations?.total ?? 0}
      isControlledOutside={true}
      onChangePage={onChangePage}
      handleSort={handleSetSort}
      sort={sort}
      rowKeyAccessor="id"
      onRowClick={rowClick}
      kebabMenu={kebabMenu}
    />
  );
};

CollectionsFoundationList.displayName = "CollectionsFoundationList";
