import React, { useCallback, useEffect, useMemo, useState } from "react";

import {
  Box,
  BreadcrumbsItemType,
  Button,
  ButtonGroup,
  ButtonVariant,
  FontWeight,
  Heading,
  HeadingSize,
  Inline,
  PageBodyTemplate,
  Pill,
  Renamable,
  SearchBar,
  Stack,
  TabGroup,
  Tooltip,
  useQueryParams
} from "@oneteam/onetheme";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate, useSearchParams } from "react-router-dom";

import { putData } from "@helpers/putData";
import { WorkspaceConfigurationHelper } from "@helpers/workspace/WorkspaceConfigurationHelper.ts";

import { CollectionsFormList } from "@pages/collection/home/<USER>";
import { CollectionsFoundationList } from "@pages/collection/home/<USER>";
import {
  CollectionFoundationsTabTotals,
  CollectionsFoundationsTabs
} from "@pages/collection/home/<USER>";
import { FormModal } from "@pages/collection/home/<USER>/FormModal";
import { FoundationModal } from "@pages/collection/home/<USER>/FoundationModal";
import { HomeHelper, ModalState } from "@pages/collection/home/<USER>";

import {
  GlobalSearchResultType,
  getGlobalSearchResult
} from "@src/helpers/searchHelper.ts";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { buildShortPageTitle, usePageTitle } from "@src/hooks/usePageTitle.ts";
import { ExistingForm } from "@src/types/Form.ts";
import { Foundation, FoundationForUpdate } from "@src/types/Foundation.ts";
import { FlowButtonLocation } from "@src/types/collection/FlowExecution";
import { Workspace } from "@src/types/workspace";

import CollectionsFoundationHelper from "../foundations/foundationsHelper";
import { FlowButtons } from "../interactions/FlowButtons";
import { GlobalSearchTable } from "./GlobalSearchTable";
import { HomeNewButton } from "./HomeNewButton";

export const FOUNDATION_TOTAL_KEY = "foundation-total";
export const SELECTED_FORM_TOTAL_KEY = "selected-form-total";
export const CHILD_FORM_TOTAL_KEY = "child-form-total";
export const ALL_SELECTED_FORM_KEY = "all-selected-form";
export const ALL_CHILD_FORM_KEY = "all-child-form";

export const SELECTED_FOUNDATIONS_KEY = "collections-selected-foundations";

interface HomeContentProps {
  workspaceVersion: WorkspaceConfigurationHelper;
  workspace: Workspace;
  foundation: Foundation;
  homeHelper?: HomeHelper;
}

export const HomeContent = ({
  workspaceVersion,
  workspace,
  foundation,
  homeHelper
}: HomeContentProps) => {
  const d = useDictionary();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [initialSearchTermSetup, setInitialSearchTermSetup] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const { queryParams, updateQueryParams } = useQueryParams({
    initialDefaultIfNone: { page: "1" },
    useSearchParams
  });

  const [searchParams, setSearchParams] = useSearchParams();

  const [searchResults, setSearchResults] = useState<GlobalSearchResultType>({
    foundations: [],
    forms: []
  });

  useEffect(() => {
    const tab = searchParams.get("tab");
    const page = searchParams.get("page");
    if (!page && !tab) {
      // replace:true otherwise it adds to the stack as two pages in the history when it's really just the page loading and presetting the search params
      setSearchParams({ page: "1" }, { replace: true });
    }
  }, [setSearchParams, searchParams]);

  useEffect(() => {
    if (searchTerm && workspace?.id) {
      getGlobalSearchResult(searchTerm, workspace).then(result => {
        if (result?.foundations.length || result?.forms.length) {
          setSearchResults(result);
        } else {
          setSearchResults({
            foundations: [],
            forms: []
          });
        }
      });
    }
  }, [workspace, searchTerm]);

  const handleUpdateSearch = useCallback(
    (updatedSearch: string) => {
      updateQueryParams({ search: updatedSearch }, false);
      setSearchTerm(updatedSearch);
    },
    [updateQueryParams]
  );

  useEffect(() => {
    if (initialSearchTermSetup) {
      return;
    }
    const queryParamsSearch = queryParams.get("search");
    if (queryParamsSearch && queryParamsSearch !== searchTerm) {
      setSearchTerm(queryParamsSearch);
    }
    setInitialSearchTermSetup(true);
  }, [initialSearchTermSetup, queryParams, searchTerm]);

  const FoundationHelper = new CollectionsFoundationHelper(
    foundation,
    workspace
  );
  const foundationsSearch = FoundationHelper.getFoundationsSearch();
  const selectedFormSearch = FoundationHelper.getSelectedFormSearch();
  const childFormSearch = FoundationHelper.getChildFormSearch();
  const allSelectedFormSearch = FoundationHelper.getAllSelectedFormSearch();
  const allChildFormSearch = FoundationHelper.getAllChildFormSearch();

  const [newFoundationModalState, setNewFoundationModalState] = useState(
    ModalState.CLOSED
  );
  const [editFoundationModalState, setEditFoundationModalState] = useState(
    ModalState.CLOSED
  );
  const [newFormModalState, setNewFormModalState] = useState(ModalState.CLOSED);
  const [newFormParentModalState, setNewFormParentModalState] = useState(
    ModalState.CLOSED
  );
  const [currentFoundationModalName, setCurrentFoundationModalName] =
    useState("");
  const [currentFoundationModalKey, setCurrentFoundationModalKey] =
    useState("");
  const [selectedFoundationId, setSelectedFoundationId] = useState<number>(-1);

  const dynamicBreadcrumbs: BreadcrumbsItemType[] = useMemo(() => {
    if (searchTerm) {
      return [
        {
          text: workspace.key,
          onClick: () => {
            handleUpdateSearch("");
          },
          href: ""
        },
        {
          text: d("ui.collection.browse.search.breadcrumb"),
          isActive: true
        }
      ] as BreadcrumbsItemType[];
    }
    if (homeHelper) {
      return homeHelper.getBreadcrumbs().map(breadcrumb => ({
        ...breadcrumb,
        onClick: breadcrumb.href
          ? () => {
              navigate(breadcrumb.href);
              queryClient.invalidateQueries({
                queryKey: [SELECTED_FOUNDATIONS_KEY]
              });
              queryClient.invalidateQueries({
                queryKey: [FOUNDATION_TOTAL_KEY]
              });
            }
          : undefined
      }));
    } else {
      return [];
    }
  }, [
    d,
    handleUpdateSearch,
    homeHelper,
    navigate,
    queryClient,
    searchTerm,
    workspace.key
  ]);

  const currentFoundationConfiguration = useMemo(() => {
    return workspaceVersion.getFoundationConfiguration(
      foundation.foundationConfigurationId
    );
  }, [foundation, workspaceVersion]);

  const nextFoundationConfiguration = useMemo(() => {
    return workspaceVersion.getNextFoundationConfiguration(
      foundation.foundationConfigurationId
    );
  }, [foundation, workspaceVersion]);

  const totals: CollectionFoundationsTabTotals = useMemo(() => {
    return {
      childFoundations: foundationsSearch.data?.total ?? 0,
      childForms: childFormSearch.data?.total ?? 0,
      selectedForms: selectedFormSearch.data?.total ?? 0
    };
  }, [
    foundationsSearch.data?.total,
    childFormSearch.data?.total,
    selectedFormSearch.data?.total
  ]);

  const tabsHelper = useMemo(() => {
    return new CollectionsFoundationsTabs(
      d,
      totals,
      currentFoundationConfiguration,
      nextFoundationConfiguration
    );
  }, [currentFoundationConfiguration, d, nextFoundationConfiguration, totals]);

  const tab = tabsHelper?.currentTab(queryParams.get("tab") ?? "");
  const queryKey = `forms-${tab}`;

  const getFoundation = useCallback(
    (modalState: ModalState) => {
      if (modalState === ModalState.CREATE) {
        return {
          id: -1,
          name: "",
          key: "",
          workspaceId: workspace.id,
          foundationConfigurationId: nextFoundationConfiguration?.id,
          parentId: foundation.id
        } as Foundation;
      } else {
        return {
          id: selectedFoundationId,
          name: currentFoundationModalName,
          key: currentFoundationModalKey,
          workspaceId: workspace.id,
          foundationConfigurationId: nextFoundationConfiguration?.id,
          parentId: foundation.id
        } as Foundation;
      }
    },
    [
      workspace.id,
      nextFoundationConfiguration?.id,
      foundation.id,
      selectedFoundationId,
      currentFoundationModalName,
      currentFoundationModalKey
    ]
  );

  const formForCreate = useCallback(() => {
    return {} as ExistingForm;
  }, []);

  const formParentForCreate = useCallback(() => {
    return {
      foundationId: foundation.id,
      formConfigurationId: "",
      intervalId: ""
    } as ExistingForm;
  }, [foundation.id]);

  const modals = useMemo(() => {
    if (
      newFoundationModalState !== ModalState.CLOSED &&
      nextFoundationConfiguration
    ) {
      return (
        <FoundationModal
          onOpenChange={() => {
            setNewFoundationModalState(ModalState.CLOSED);
          }}
          fetchQueryKey={SELECTED_FOUNDATIONS_KEY}
          foundation={getFoundation(ModalState.CREATE)}
          foundationConfiguration={nextFoundationConfiguration}
          modalState={ModalState.CREATE}
        />
      );
    }

    if (
      newFormModalState !== ModalState.CLOSED &&
      nextFoundationConfiguration
    ) {
      return (
        <FormModal
          onOpenChange={() => {
            setNewFormModalState(ModalState.CLOSED);
          }}
          fetchQueryKey={queryKey}
          workspace={workspace}
          form={formForCreate()}
          currentFoundation={foundation}
          foundationConfiguration={nextFoundationConfiguration}
          parentFoundationConfiguration={workspaceVersion.getPrevFoundationConfiguration(
            nextFoundationConfiguration.id
          )}
          workspaceConfigurationHelper={workspaceVersion}
          invalidateKey={CHILD_FORM_TOTAL_KEY}
          isWorkspaceFormModal={false}
          existingForms={allChildFormSearch.data}
        />
      );
    }

    if (
      editFoundationModalState !== ModalState.CLOSED &&
      nextFoundationConfiguration
    ) {
      return (
        <FoundationModal
          onOpenChange={() => {
            setEditFoundationModalState(ModalState.CLOSED);
          }}
          fetchQueryKey={SELECTED_FOUNDATIONS_KEY}
          foundation={getFoundation(ModalState.EDIT)}
          foundationConfiguration={nextFoundationConfiguration}
          modalState={ModalState.EDIT}
        />
      );
    }

    if (newFormParentModalState !== ModalState.CLOSED) {
      return (
        <FormModal
          onOpenChange={() => {
            setNewFormParentModalState(ModalState.CLOSED);
          }}
          fetchQueryKey={queryKey}
          workspace={workspace}
          form={formParentForCreate()}
          currentFoundation={foundation}
          foundationConfiguration={currentFoundationConfiguration}
          parentFoundationConfiguration={workspaceVersion.getPrevFoundationConfiguration(
            currentFoundationConfiguration.id
          )}
          workspaceConfigurationHelper={workspaceVersion}
          invalidateKey={SELECTED_FORM_TOTAL_KEY}
          isWorkspaceFormModal={true}
          existingForms={allSelectedFormSearch.data}
        />
      );
    }

    return <></>;
  }, [
    currentFoundationConfiguration,
    formForCreate,
    formParentForCreate,
    foundation,
    getFoundation,
    newFormModalState,
    newFormParentModalState,
    newFoundationModalState,
    editFoundationModalState,
    nextFoundationConfiguration,
    queryKey,
    workspace,
    workspaceVersion,
    allChildFormSearch.data,
    allSelectedFormSearch.data
  ]);

  const { mutate: updateFoundation } = useMutation({
    mutationFn: (newName: string) => {
      const foundationForNameChange = { ...foundation, name: newName };
      return putData(
        `/foundations/${foundation.id}`,
        FoundationForUpdate.fromFoundation(foundationForNameChange)
      );
    },
    onSuccess: async (data: Foundation) => {
      foundation.name = data.name;
      await queryClient.invalidateQueries({
        queryKey: [SELECTED_FOUNDATIONS_KEY]
      });
      queryClient.invalidateQueries({
        queryKey: [FOUNDATION_TOTAL_KEY]
      });
    }
  });

  const title =
    !foundation?.parentId || !currentFoundationConfiguration?.name
      ? d("ui.collection.browse.title")
      : foundation?.name;

  usePageTitle(
    buildShortPageTitle({
      section: title,
      context: workspace?.key ?? ""
    }),
    !!workspace?.key && !!title
  );

  const heading = useMemo(() => {
    if (searchTerm) {
      return d("ui.collection.browse.search.title", {
        searchTerm
      });
    }

    return (
      <Inline alignment="left" gap="100">
        <Heading
          size={HeadingSize.L}
          weight={
            !foundation?.parentId ? FontWeight.MEDIUM : FontWeight.REGULAR
          }
        >
          <Renamable
            value={title}
            onChange={updateFoundation}
            disabled={!foundation?.parentId}
          />
        </Heading>
        <Inline gap="050">
          <Tooltip
            content={d("ui.collection.browse.tooltip.foundationKey", {
              foundation: currentFoundationConfiguration?.name ?? ""
            })}
            delay={500}
          >
            <Pill
              label={foundation.key}
              variant="neutral"
              background="strong"
            />
          </Tooltip>
          <Tooltip
            content={d("ui.collection.browse.tooltip.foundationConfiguration")}
            delay={500}
          >
            <Pill
              label={currentFoundationConfiguration?.name}
              variant="neutral"
              background="strong"
            />
          </Tooltip>
        </Inline>
      </Inline>
    );
  }, [
    d,
    updateFoundation,
    currentFoundationConfiguration?.name,
    foundation.key,
    foundation?.parentId,
    searchTerm,
    title
  ]);

  const onEdit = useCallback(
    (
      selectedFoundationId: number,
      currentFoundationName: string,
      currentFoundationKey: string
    ) => {
      setEditFoundationModalState(ModalState.EDIT);
      setCurrentFoundationModalName(currentFoundationName);
      setCurrentFoundationModalKey(currentFoundationKey);
      setSelectedFoundationId(selectedFoundationId);
    },
    []
  );

  const renderContent = useCallback(() => {
    if (!homeHelper) {
      return <> </>;
    }
    if (!searchTerm) {
      return (
        <Stack gap="100" height="100">
          <Stack>
            <Inline
              gap="100"
              width="100"
              alignment="right"
              style={{
                height: "32px"
              }}
              spaceBetween
            >
              <Inline>
                {/* foundation level information - e.g. Avatar group of users who can access */}
              </Inline>
              <ButtonGroup>
                <FlowButtons
                  collectionId={foundation.id}
                  location={FlowButtonLocation.FOUNDATION}
                />
                <HomeNewButton
                  onClick={() => {
                    if (tab === CollectionsFoundationsTabs.SELECTED_FORMS) {
                      setNewFormParentModalState(ModalState.CREATE);
                    } else if (tab === CollectionsFoundationsTabs.CHILD_FORMS) {
                      setNewFormModalState(ModalState.CREATE);
                    } else if (
                      tab === CollectionsFoundationsTabs.CHILD_FOUNDATIONS
                    ) {
                      setNewFoundationModalState(ModalState.CREATE);
                    }
                  }}
                  nextFoundation={
                    nextFoundationConfiguration
                      ? {
                          onClick: () => {
                            setNewFoundationModalState(ModalState.CREATE);
                          },
                          label: d(
                            "ui.collection.browse.createButton.foundation",
                            {
                              foundation:
                                nextFoundationConfiguration?.name ?? ""
                            }
                          )
                        }
                      : undefined
                  }
                  currentForm={{
                    onClick: () => {
                      setNewFormParentModalState(ModalState.CREATE);
                    },
                    label: d("ui.collection.browse.createButton.form", {
                      foundationConfigurationName:
                        currentFoundationConfiguration?.name ?? ""
                    })
                  }}
                  nextForm={
                    nextFoundationConfiguration
                      ? {
                          onClick: () => {
                            setNewFormModalState(ModalState.CREATE);
                          },
                          label: d("ui.collection.browse.createButton.form", {
                            foundationConfigurationName:
                              nextFoundationConfiguration?.name ?? ""
                          })
                        }
                      : undefined
                  }
                />
              </ButtonGroup>
            </Inline>
            <Inline
              position="relative"
              width="100"
              spaceBetween
              alignment="left"
            >
              <Box width="100">
                <TabGroup
                  handleChange={(tab: string) => {
                    setSearchParams({ tab: tab, page: "1" });
                  }}
                  options={tabsHelper.tabs()}
                  value={tab}
                />
              </Box>
              <Box position="absolute" style={{ right: 0 }}>
                <Button
                  label="Save as starred"
                  variant={ButtonVariant.TEXT}
                  leftIcon={{ name: "grade" }}
                />
              </Box>
            </Inline>
          </Stack>

          {tab === "foundations" && nextFoundationConfiguration && (
            <CollectionsFoundationList
              workspace={workspace}
              foundation={foundation}
              foundationConfiguration={nextFoundationConfiguration}
              onNew={() => {
                setNewFoundationModalState(ModalState.CREATE);
              }}
              onEdit={(id, name, key) => onEdit(id, name, key)}
              homeHelper={homeHelper}
            />
          )}

          {tab === CollectionsFoundationsTabs.CHILD_FORMS &&
            nextFoundationConfiguration && (
              <CollectionsFormList
                workspace={workspace}
                foundation={foundation}
                foundationConfiguration={nextFoundationConfiguration}
                tab={tab}
                workspaceVersion={workspaceVersion}
                homeHelper={homeHelper}
              />
            )}
          {tab === CollectionsFoundationsTabs.SELECTED_FORMS && (
            <CollectionsFormList
              workspace={workspace}
              foundation={foundation}
              foundationConfiguration={currentFoundationConfiguration}
              tab={tab}
              workspaceVersion={workspaceVersion}
              homeHelper={homeHelper}
            />
          )}
        </Stack>
      );
    } else {
      return (
        <GlobalSearchTable
          searchData={searchResults}
          workspaceVersion={workspaceVersion}
          homeHelper={homeHelper}
          tab={tab}
        />
      );
    }
  }, [
    searchTerm,
    tab,
    nextFoundationConfiguration,
    currentFoundationConfiguration,
    d,
    searchResults,
    foundation,
    homeHelper,
    onEdit,
    tabsHelper,
    setSearchParams,
    workspace,
    workspaceVersion
  ]);

  return (
    <PageBodyTemplate
      heading={heading}
      breadcrumbs={dynamicBreadcrumbs} //...breadcrumbs,
      withoutScroll
      overlay={modals}
      headingRight={
        <Inline
          alignment="top-right"
          spaceBetween
          gap="100"
          style={{ width: "164px" }}
        >
          <SearchBar
            withDebounce
            value={searchTerm}
            handleChange={handleUpdateSearch}
            placeholder={d("ui.collection.browse.search.input.placeholder")}
            autoFocus
            width="100"
          />
        </Inline>
      }
    >
      {renderContent()}
    </PageBodyTemplate>
  );
};

HomeContent.displayName = "HomeContent";
