import React, { useCallback, useMemo } from "react";

import {
  FontWeight,
  Icon,
  Inline,
  Pill,
  TableWithPagination,
  Text
} from "@oneteam/onetheme";
import { get } from "lodash";
import { useNavigate } from "react-router-dom";

import { getFormUrl } from "@helpers/forms/formHelper";
import { WorkspaceConfigurationHelper } from "@helpers/workspace/WorkspaceConfigurationHelper.ts";

import { HomeHelper } from "@pages/collection/home/<USER>";

import { commonIcons } from "@src/constants/iconConstants";
import {
  FormAPI,
  FoundationAPI,
  GlobalSearchResultType,
  getFoundationHierarchy
} from "@src/helpers/searchHelper.ts";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { ExistingForm } from "@src/types/Form";
import { Foundation } from "@src/types/Foundation";

import { PropertyValueRenderer } from "./PropertyValueRenderer/PropertyValueRenderer";

interface GlobalSearchTableProps {
  searchData: GlobalSearchResultType;
  workspaceVersion: WorkspaceConfigurationHelper;
  homeHelper?: HomeHelper;
  tab: string;
}

export const GlobalSearchTable = (props: GlobalSearchTableProps) => {
  const { searchData, workspaceVersion, homeHelper, tab } = props;
  const { foundations, forms } = searchData;
  const d = useDictionary();
  const navigate = useNavigate();

  const data = useMemo(() => {
    foundations?.map(foundation => {
      foundation.type = "foundation";
      return foundation;
    });
    forms?.map(form => {
      form.type = "form";
      return form;
    });
    return [...foundations, ...forms];
  }, [foundations, forms]);

  const getFoundationConfiguration = useCallback(
    (foundationConfigurationId: string) => {
      return workspaceVersion.getFoundationConfiguration(
        foundationConfigurationId
      );
    },
    [workspaceVersion]
  );

  const getIntervalConfiguration = useCallback(
    (intervalId: string) => {
      return workspaceVersion.getIntervalById(intervalId);
    },
    [workspaceVersion]
  );

  const getFormConfiguration = useCallback(
    (formConfigurationId: string) => {
      return workspaceVersion.getFormConfiguration(formConfigurationId);
    },
    [workspaceVersion]
  );

  const rowClick = (item: FoundationAPI | FormAPI) => {
    if (!homeHelper) {
      return;
    }

    const relevantFoundationId = (() => {
      if (item.type === "foundation") {
        return item.id;
      }
      if (item.type === "form") {
        return item.foundation.id;
      }
    })();
    if (!relevantFoundationId) {
      return;
    }

    getFoundationHierarchy(relevantFoundationId.toString()).then(
      (hierarchy: { hierarchy: unknown[] }) => {
        const foundationHierarchyPath = hierarchy.hierarchy
          .slice(1)
          .map(h => {
            const foundation = h as { key: string };
            return foundation.key;
          })
          .join("/");

        if (item.type === "foundation") {
          navigate(
            homeHelper.hierarchicalFoundationPath(foundationHierarchyPath)
          );
        } else {
          const { basePath, formIdentifier } = getFormUrl({
            form: item,
            workspaceVersion,
            tab,
            homeHelper,
            foundationHierarchyPath
          });
          navigate(basePath + "/form" + `/${formIdentifier}`);
        }
      }
    );
  };

  const additionalPropertiesColumns = useMemo(() => {
    const foundationKeys =
      searchData?.foundations?.flatMap(foundation =>
        Object.keys(foundation.properties ?? {})
      ) ?? [];

    const formKeys =
      searchData?.forms?.flatMap(form => Object.keys(form.properties ?? {})) ??
      [];

    // Combine and get unique keys
    const uniqueKeys = Array.from(new Set([...foundationKeys, ...formKeys]));

    return uniqueKeys.map(key => ({
      header: key.split("_").join(" "),
      key: `properties.${key}`,
      canSort: true,
      formatter: (item: Foundation | ExistingForm) => {
        const value = get(item, `properties.${key}`, undefined);
        return <PropertyValueRenderer value={value} />;
      }
    }));
  }, [searchData?.forms, searchData?.foundations]);

  const getTableColumns = useCallback(() => {
    const isFoundation = (data: FoundationAPI | FormAPI) => {
      return data.type === "foundation";
    };

    return [
      {
        header: "",
        key: "type",
        canSort: false,
        formatter: (data: FoundationAPI | FormAPI) => {
          const icon = isFoundation(data)
            ? commonIcons.foundations
            : commonIcons.forms;
          return <Icon {...icon} color="text-secondary" />;
        },
        options: {
          width: "var(--icon-size-m)"
        }
      },
      {
        header: d("ui.terminology.foundation"),
        key: "name",
        canSort: true,
        formatter: (data: FoundationAPI | FormAPI) => {
          const isFoundationData = isFoundation(data);
          const foundationConfig = getFoundationConfiguration(
            isFoundationData
              ? data.foundationConfigurationId
              : data.foundation.foundationConfigurationId
          );

          if (isFoundationData) {
            return (
              <Inline alignment="left" gap="050">
                <Text weight={FontWeight.MEDIUM}>{data.name}</Text>
                <Pill key={data?.id} label={foundationConfig?.name} />
              </Inline>
            );
          } else {
            return (
              <Inline alignment="left" gap="050">
                <Text weight={FontWeight.MEDIUM}>{data.foundation.name}</Text>
                <Pill key={data?.id} label={foundationConfig?.name} />
              </Inline>
            );
          }
        }
      },
      {
        header: d("Details"),
        key: "details",
        canSort: true,
        formatter: (data: FoundationAPI | FormAPI) => {
          if (isFoundation(data)) {
            return <> </>;
          }
          const formConfig = getFormConfiguration(data.formConfigurationId);
          const interval = data.intervalId
            ? getIntervalConfiguration(data.intervalId)
            : null;
          if (!formConfig) {
            return <></>;
          }
          return (
            <Inline alignment="left" gap="050" wrap>
              <Text weight={FontWeight.MEDIUM}>{formConfig.name}</Text>
              <Pill key={formConfig.key} label={formConfig.key} />
              {interval && <Pill key={interval.id} label={interval.name} />}
            </Inline>
          );
        }
      },
      ...additionalPropertiesColumns
    ];
  }, [
    d,
    getFoundationConfiguration,
    getIntervalConfiguration,
    getFormConfiguration,
    additionalPropertiesColumns
  ]);

  const searchTableColumns = getTableColumns();

  return (
    <TableWithPagination
      isLoading={false}
      fillContainer
      columns={searchTableColumns}
      data={data ?? []}
      itemsPerPage={100}
      startingPage={1}
      noDataText={d("ui.common.noData")}
      totalCount={data?.length ?? 0}
      isControlledOutside={true}
      rowKeyAccessor="id"
      onRowClick={rowClick}
    />
  );
};
