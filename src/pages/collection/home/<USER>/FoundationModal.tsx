import React, { useMemo, useState } from "react";

import {
  <PERSON>ert,
  AlertVariant,
  Box,
  Floating,
  FloatingPosition,
  Form,
  Modal,
  Overlay,
  Stack
} from "@oneteam/onetheme";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFormContext } from "react-hook-form";
import z from "zod";

import { ApiValidationError, CustomError } from "@helpers/errorHelper.ts";
import { modalZIndex } from "@helpers/modalHelper.ts";
import { postData } from "@helpers/postData.ts";
import { putData } from "@helpers/putData";

import { NameKeyFormField } from "@components/shared/NameKeyFormField/NameKeyFormField";
import { autoGenerateKeyFromName } from "@components/shared/NameKeyFormField/autoGenerateKeyFromName";

import { FOUNDATION_TOTAL_KEY } from "@pages/collection/home/<USER>";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  Foundation,
  FoundationForCreate,
  FoundationForUpdate,
  foundationSchema
} from "@src/types/Foundation.ts";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";
import { keySchema } from "@src/types/Key";

import { ModalState } from "../HomeHelper";
import "./FoundationModal.scss";

interface FoundationModalFieldsProps {
  serverError: ApiValidationError[] | undefined;
  d: Dictionary;
  modalState: ModalState;
  foundationConfigurationName: FoundationConfiguration["name"];
  keyError?: string | undefined;
}

const FoundationModalFields = ({
  serverError,
  d,
  modalState,
  foundationConfigurationName,
  keyError
}: FoundationModalFieldsProps) => {
  const formContext = useFormContext();
  const [isKeyChanged, setIsKeyChanged] = useState(false);

  if (serverError?.length) {
    formContext.clearErrors();
    serverError.forEach(e => {
      formContext.setError(e.field, { message: d(e.key) });
    });
  }

  return (
    <Stack gap="100" width="100" contentsWidth="100">
      <NameKeyFormField
        nameField={{
          label: d("ui.foundations.fields.name.label", {
            foundationConfigurationName
          })
        }}
        keyField={{
          label: d("ui.foundations.fields.key.label", {
            foundationConfigurationName
          }),
          tooltip: d("ui.foundations.fields.key.tooltip", {
            foundationConfigurationName
          }),
          error: keyError,
          afterChange:
            modalState === ModalState.EDIT
              ? () => {
                  setIsKeyChanged(true);
                }
              : undefined,
          allowLowerCase: true,
          existingKeys: [] // This can be populated if needed
        }}
      />
      {isKeyChanged && (
        <Alert variant={AlertVariant.WARNING}>
          {d("ui.foundations.fields.key.changeWarning")}
        </Alert>
      )}
    </Stack>
  );
};

interface FoundationModalProps {
  onOpenChange: () => void;
  fetchQueryKey: string;
  foundation: Foundation;
  foundationConfiguration: FoundationConfiguration;
  modalState: ModalState;
}

export const FoundationModal = ({
  onOpenChange,
  fetchQueryKey,
  foundation,
  foundationConfiguration,
  modalState
}: FoundationModalProps) => {
  const [serverError, setServerError] = useState<CustomError | undefined>();
  const [keyError, setKeyError] = useState<string | undefined>(undefined);
  const d = useDictionary();

  const queryClient = useQueryClient();

  const handleSubmit = (foundation: Foundation) => {
    let key = foundation.key.trim();

    if (!key) {
      // If the key is not set, we can set it to the uppercase version of the name
      key = autoGenerateKeyFromName(foundation.name) ?? "";
    }

    try {
      const keyParsed = keySchema(d).parse(key);

      if (modalState === ModalState.CREATE) {
        saveFoundation({
          ...foundation,
          key: keyParsed
        });
      } else {
        updateFoundation({
          ...foundation,
          key: keyParsed
        });
      }
    } catch (error) {
      setKeyError((error as z.ZodError).errors[0].message);
    }
  };

  const { mutate: updateFoundation } = useMutation({
    mutationFn: (updatedFoundation: Foundation) => {
      // Close the modal if user didn't change anything
      if (
        updatedFoundation.name === foundation?.name &&
        updatedFoundation.key === foundation?.key
      ) {
        onOpenChange();
        return Promise.resolve();
      }

      return putData(
        `/foundations/${updatedFoundation.id}`,
        FoundationForUpdate.fromFoundation({
          ...updatedFoundation,
          foundationConfigurationId: foundationConfiguration.id
        })
      );
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [fetchQueryKey] });
      queryClient.invalidateQueries({
        queryKey: [FOUNDATION_TOTAL_KEY]
      });
      onOpenChange();
    },
    onError: (error: CustomError) => {
      setServerError(error);
    }
  });

  const { mutate: saveFoundation } = useMutation<
    Foundation,
    CustomError,
    Foundation
  >({
    mutationFn: (foundation: Foundation) => {
      return postData(
        `/workspaces/${foundation.workspaceId}/foundations`,
        FoundationForCreate.fromFoundation(foundation)
      );
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [fetchQueryKey] });
      queryClient.invalidateQueries({
        queryKey: [FOUNDATION_TOTAL_KEY]
      });
      onOpenChange();
    },
    onError: (error: CustomError) => {
      setServerError(error);
    }
  });

  const modalTitle = useMemo(() => {
    if (modalState === ModalState.CREATE) {
      return `${d("ui.collection.foundationModal.createTitle", { foundation: foundationConfiguration.name })}`;
    }

    return `${d("ui.collection.foundationModal.updateTitle", { foundation: foundationConfiguration.name })}`;
  }, [modalState, d, foundationConfiguration.name]);

  return (
    <Box style={{ zIndex: modalZIndex }}>
      <Overlay isOpen></Overlay>
      <Floating position={FloatingPosition.CENTER}>
        <Modal
          className="foundation-modal"
          onOpenChange={onOpenChange}
          heading={modalTitle}
        >
          <Form
            d={d}
            schema={foundationSchema(d)}
            submitLabel={d("ui.common.save")}
            defaultValues={foundation}
            handleSubmit={handleSubmit}
            cancelLabel={d("ui.common.cancel")}
            handleCancel={onOpenChange}
          >
            <FoundationModalFields
              modalState={modalState}
              serverError={serverError?.errors}
              foundationConfigurationName={foundationConfiguration.name}
              d={d}
              keyError={keyError}
            />
          </Form>
        </Modal>
      </Floating>
    </Box>
  );
};

FoundationModal.displayName = "FoundationModal";
