import { ToastNotificationVariant } from "@oneteam/onetheme";
import { useMutation } from "@tanstack/react-query";

import { postData } from "@helpers/postData";

import {
  DocChange,
  PushToastNotification
} from "@pages/workspace/WorkspaceLayout";

import { Dictionary } from "@src/hooks/useDictionary";
import {
  WorkspaceSecuredValueFromApi,
  WorkspaceVariable,
  WorkspaceVariableForApiUpdate
} from "@src/types/WorkspaceVariable";

import {
  NOTIFICATION_DURATION_MS,
  makeWorkspaceVariable
} from "./variableHelpers";

export const useUpdateVariable = ({
  workspaceId,
  d,
  pushToastNotification,
  docChange
}: {
  workspaceId: number;
  d: Dictionary;
  pushToastNotification: PushToastNotification;
  docChange: DocChange;
}) => {
  return useMutation({
    mutationFn: (data: WorkspaceVariableForApiUpdate) => {
      const updatePromise = (() => {
        if (data.wasSecured && data.value !== undefined) {
          return postData(`/workspaces/${workspaceId}/variables`, {
            value: data.value,
            isSecured: data.wasSecured
          });
        }

        if (data.wasSecured) {
          return Promise.resolve(data.reference);
        }

        return Promise.resolve(null);
      })();

      return updatePromise.then((fromApi?: WorkspaceSecuredValueFromApi) => {
        const referenceXorValue = fromApi
          ? {
              securedRef: fromApi.ref
            }
          : {
              value: data.value
            };

        const promise = new Promise<WorkspaceVariable>(resolve => {
          docChange(d => {
            d.variables ??= {};

            const id = data.id;
            const current = d.variables[id];
            d.variables[id] = {
              ...current,
              name: data.name ?? d.variables[id].name,
              description: data.description ?? d.variables[id].description,
              ...referenceXorValue,
              metadata: {
                ...current?.metadata,
                updatedAt: new Date().toISOString()
              }
            };

            const variable = makeWorkspaceVariable(d.variables[id], fromApi);
            resolve(variable);
          });
        });
        return promise;
      });
    },
    onSuccess: async (data: WorkspaceVariable) => {
      pushToastNotification(
        d("ui.notifications.success"),
        d("ui.configuration.variables.update.success"),
        ToastNotificationVariant.SUCCESS,
        NOTIFICATION_DURATION_MS
      );
      return data;
    },
    onError: (
      error: Error,
      workspaceVariable: WorkspaceVariableForApiUpdate
    ) => {
      console.error("Error creating variable:", error);
      pushToastNotification(
        d("errors.common.updateFailed"),
        d("errors.configurationVariables.update", {
          variableName: workspaceVariable?.name
        }),
        ToastNotificationVariant.DANGER,
        NOTIFICATION_DURATION_MS
      );
      return error;
    }
  });
};
