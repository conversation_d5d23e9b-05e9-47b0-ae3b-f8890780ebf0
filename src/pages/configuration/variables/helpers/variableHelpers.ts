import {
  WorkspaceSecuredValueFromApi,
  WorkspaceVariable,
  WorkspaceVariableConfiguration
} from "@src/types/WorkspaceVariable";

export const NOTIFICATION_DURATION_MS = 10000;

export const makeWorkspaceVariable = (
  fromConfig: WorkspaceVariableConfiguration,
  fromApi?: WorkspaceSecuredValueFromApi
): WorkspaceVariable => {
  // When it's secured we have two cases 'invalid' cases
  // 1. securedRef is set, but the reference doesn't exist in the API (e.g. referenced variable was deleted, or someone trying to reuse the ref)
  // 2. securedRef is not set (e.g. usually from an configuration import)
  //  in both cases we simply treat the reference has invalid and return the variable without reference
  const isSecured = fromConfig.isSecured;
  const hasSecuredRef = fromConfig.securedRef !== undefined;

  if (isSecured && (!hasSecuredRef || !fromApi)) {
    return {
      id: fromConfig.id,
      name: fromConfig.name,
      description: fromConfig.description,
      securedRef: undefined,
      isSecured: fromConfig.isSecured,
      hasErrors: true,
      metadata: fromConfig.metadata
    };
  }

  return {
    id: fromConfig.id,
    name: fromConfig.name,
    description: fromConfig.description,
    securedRef: fromApi,
    isSecured: fromConfig.isSecured,
    value: fromConfig.value,
    hasErrors: fromConfig.hasErrors,
    metadata: fromConfig.metadata
  };
};
