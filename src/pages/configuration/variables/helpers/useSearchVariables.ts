import { useQuery } from "@tanstack/react-query";
import { keyBy } from "lodash";

import * as PaginatedTableHelper from "@helpers/PaginatedTable";
import { getData } from "@helpers/getData";

import { Page, Sort } from "@src/types/Page";
import {
  WorkspaceSecuredValueFromApi,
  WorkspaceVariable,
  WorkspaceVariableConfiguration
} from "@src/types/WorkspaceVariable";

import { makeWorkspaceVariable } from "./variableHelpers";

export interface VariableSearchParams {
  search?: string;
  page?: number;
  pageSize?: number;
  asc?: string;
  dsc?: string;
}

const WORKSPACE_VARIABLES_QUERY_KEY = "workspace-variables";

export const useSearchVariables = (
  workspaceId: number,
  variablesFromConfig: WorkspaceVariableConfiguration[],
  params: VariableSearchParams
) => {
  const queryKey = [
    WORKSPACE_VARIABLES_QUERY_KEY,
    workspaceId,
    JSON.stringify(params)
  ];

  return useQuery<Page<WorkspaceVariable>>({
    queryKey: queryKey,
    queryFn: (): Promise<Page<WorkspaceVariable>> => {
      return getData(`/workspaces/${workspaceId}/variables`).then(
        (variablesFromApi: WorkspaceSecuredValueFromApi[]) => {
          const workspaceVariables = toWorkspaceVariables(
            variablesFromConfig,
            variablesFromApi
          );
          const results = searchVariables(workspaceVariables, params);
          return results;
        }
      );
    },
    enabled: variablesFromConfig.length > 0
  });
};

// config takes precedence over API, so if API exists but config doesn't, we will exclude it
const toWorkspaceVariables = (
  variablesFromConfig: WorkspaceVariableConfiguration[],
  variablesFromApi: WorkspaceSecuredValueFromApi[]
): WorkspaceVariable[] => {
  const fromApiByIdentifier = keyBy(variablesFromApi, "ref");

  // variables from config is our main source of truth and will be used to create the final list
  const result = variablesFromConfig.map(fromConfig => {
    const fromApi = fromConfig.securedRef
      ? fromApiByIdentifier[fromConfig.securedRef]
      : undefined;
    return makeWorkspaceVariable(fromConfig, fromApi);
  });
  return result;
};

const searchVariables = (
  variables: WorkspaceVariable[],
  params: VariableSearchParams
): Page<WorkspaceVariable> => {
  const { search: textFilter, page = 1, asc, dsc } = params;

  const matchNameOrDescriptionPredicate = (data: WorkspaceVariable) =>
    !textFilter ||
    data.name.toLowerCase().includes(textFilter.toLowerCase()) ||
    (data.description ?? "")
      ?.toLowerCase()
      .includes(textFilter.toLowerCase()) ||
    (data.value ?? "").toLowerCase().includes(textFilter.toLowerCase());

  const sort = PaginatedTableHelper.Sort.from(asc, dsc);

  const data = new PaginatedTableHelper.PaginatedTable(
    variables,
    page ?? 1,
    [matchNameOrDescriptionPredicate],
    sort
  );

  return {
    page: {
      page: data.page,
      pageSize: data.pageSize,
      sort: sortToPageSort(sort)
    },
    total: variables.length,
    items: data.dataForPage()
  };
};

function sortToPageSort(
  sort: PaginatedTableHelper.Sort<unknown>
): Sort | undefined {
  const str = sort.toServerParamString();
  if (!str) {
    return undefined;
  }
  const [field, direction] = str.split(",");
  return {
    fields: [
      {
        field,
        direction
      }
    ]
  };
}
