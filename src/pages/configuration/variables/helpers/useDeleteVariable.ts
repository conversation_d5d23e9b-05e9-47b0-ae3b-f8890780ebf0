import { ToastNotificationVariant } from "@oneteam/onetheme";
import { useMutation } from "@tanstack/react-query";

import {
  DocChange,
  PushToastNotification
} from "@pages/workspace/WorkspaceLayout";

import { Dictionary } from "@src/hooks/useDictionary";
import { WorkspaceVariable } from "@src/types/WorkspaceVariable";

import { NOTIFICATION_DURATION_MS } from "./variableHelpers";

export const useDeleteVariable = ({
  d,
  pushToastNotification,
  docChange
}: {
  workspaceId: number;
  d: Dictionary;
  pushToastNotification: PushToastNotification;
  docChange: DocChange;
}) => {
  return useMutation({
    mutationFn: (variable: WorkspaceVariable) => {
      const deletePromise = (() => {
        // We just delete from the document - Publishing is when the value is actually purged from the DB
        return Promise.resolve();
      })();

      return deletePromise.then(() => {
        const result = new Promise<void>(resolve => {
          docChange(d => {
            delete d.variables[variable.id];
            resolve();
          });
        });
        return result;
      });
    },
    onSuccess: async (_, variable: WorkspaceVariable) => {
      pushToastNotification(
        d("ui.notifications.success"),
        d("ui.configuration.variables.delete.success", {
          variableName: variable.name
        }),
        ToastNotificationVariant.SUCCESS,
        NOTIFICATION_DURATION_MS
      );
    },
    onError: (error: Error, variable: WorkspaceVariable) => {
      console.error("Error deleting variable:", error);
      pushToastNotification(
        d("errors.common.deletionFailed"),
        d("errors.configurationVariables.delete", {
          variableName: variable.name
        }),
        ToastNotificationVariant.DANGER,
        NOTIFICATION_DURATION_MS
      );
      return error;
    }
  });
};
