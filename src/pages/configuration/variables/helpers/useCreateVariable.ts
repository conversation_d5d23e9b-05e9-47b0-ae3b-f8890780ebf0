import { ToastNotificationVariant } from "@oneteam/onetheme";
import { useMutation } from "@tanstack/react-query";

import { customNanoId } from "@helpers/customNanoIdHelper";
import { postData } from "@helpers/postData";

import {
  DocChange,
  PushToastNotification
} from "@pages/workspace/WorkspaceLayout";

import { Dictionary } from "@src/hooks/useDictionary";
import {
  WorkspaceSecuredValueFromApi,
  WorkspaceVariable,
  WorkspaceVariableConfiguration,
  WorkspaceVariableForCreate
} from "@src/types/WorkspaceVariable";

import {
  NOTIFICATION_DURATION_MS,
  makeWorkspaceVariable
} from "./variableHelpers";

const createWorkspaceVariableConfiguration = (
  data: WorkspaceVariableForCreate,
  fromApi?: WorkspaceSecuredValueFromApi
): WorkspaceVariableConfiguration => {
  const securedRefXorValue = fromApi
    ? {
        securedRef: fromApi.ref
      }
    : {
        value: data.value
      };

  const id = customNanoId();
  const fromConfig = {
    id,
    name: data.name,
    description: data.description,
    isSecured: data.isSecured,
    ...securedRefXorValue,
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  } as WorkspaceVariableConfiguration;
  return fromConfig;
};

export const useCreateVariable = ({
  workspaceId,
  d,
  pushToastNotification,
  docChange
}: {
  workspaceId: number;
  d: Dictionary;
  pushToastNotification: PushToastNotification;
  docChange: DocChange;
}) => {
  return useMutation({
    mutationFn: (data: WorkspaceVariableForCreate) => {
      const createPromise = (() => {
        if (data.isSecured) {
          return postData(`/workspaces/${workspaceId}/variables`, {
            value: data.value,
            isSecured: data.isSecured
          });
        }
        return Promise.resolve(null);
      })();

      return createPromise.then((fromApi?: WorkspaceSecuredValueFromApi) => {
        const promise = new Promise<WorkspaceVariable>(resolve => {
          docChange(d => {
            d.variables ??= {};

            const fromConfig = createWorkspaceVariableConfiguration(
              data,
              fromApi
            );
            d.variables[fromConfig.id] = fromConfig;

            const variable = makeWorkspaceVariable(fromConfig, fromApi);
            resolve(variable);
          });
        });
        return promise;
      });
    },
    onSuccess: async (data: WorkspaceVariable) => {
      pushToastNotification(
        d("ui.notifications.success"),
        d("ui.configuration.variables.create.success"),
        ToastNotificationVariant.SUCCESS,
        NOTIFICATION_DURATION_MS
      );
      return data;
    },
    onError: (error: Error) => {
      console.error("Error creating variable:", error);
      pushToastNotification(
        d("errors.common.creationFailed"),
        d("error.configurationVariables.create.failed"),
        ToastNotificationVariant.DANGER,
        NOTIFICATION_DURATION_MS
      );
      return error;
    }
  });
};
