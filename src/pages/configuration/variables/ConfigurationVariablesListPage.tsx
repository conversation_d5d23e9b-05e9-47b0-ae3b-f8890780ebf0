import React, { useCallback, useEffect } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Button,
  ButtonGroup,
  ColorText,
  ConfirmationModal,
  DropdownItem,
  DropdownItemGroup,
  FloatingWithParentPosition,
  Icon,
  IconSize,
  Inline,
  KebabMenu,
  ModalDialog,
  Overlay,
  PageBodyTemplate,
  Pill,
  PillIcon,
  PillVariant,
  SearchBar,
  StatusLine,
  StatusLineVariant,
  TableColumn,
  TableKebabMenuFormatter,
  TableWithPagination,
  Text,
  TextAlignment,
  Tooltip
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { formatDate } from "@pages/collection/flows/FlowExecutionHelper";
import {
  DocChange,
  PushToastNotification
} from "@pages/workspace/WorkspaceLayout";

import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import { buildShortPageTitle, usePageTitle } from "@src/hooks/usePageTitle.ts";
import { usePaginatedSearchParams } from "@src/hooks/usePaginatedSearchParams";
import { WorkspaceVariable } from "@src/types/WorkspaceVariable";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace";

import { ConfigurationVariablesCrudModal } from "./ConfigurationVariablesCrudModal";
import { useDeleteVariable } from "./helpers/useDeleteVariable";
import { useSearchVariables } from "./helpers/useSearchVariables";

export const ConfigurationVariablesListPage = () => {
  const breadcrumbs = useBreadcrumbs();
  const d = useDictionary();
  const [variableForDeletion, setVariableForDeletion] = React.useState<
    WorkspaceVariable | undefined
  >(undefined);
  const [variableForEdit, setVariableForEdit] = React.useState<
    WorkspaceVariable | undefined
  >(undefined);
  const { document, docChange, workspace, pushToastNotification } =
    useOutletContext<{
      document: Doc<WorkspaceDocument>;
      pushToastNotification: PushToastNotification;
      workspace: Workspace;
      docChange: DocChange;
    }>();
  const [isCrudModalOpen, setIsCrudModalOpen] = React.useState<boolean>(false);

  usePageTitle(
    buildShortPageTitle({
      section: d("ui.configuration.variables.title"),
      context: workspace?.key ?? ""
    }),
    !!workspace?.key
  );

  const columns = columnDefinitions(d);
  const {
    sort,
    searchTerm,
    page,
    pageSize,
    asc,
    dsc,
    onChangeSorting,
    onChangePage,
    onChangeSearchTerm
  } = usePaginatedSearchParams<WorkspaceVariable>({
    columns
  });

  const {
    data: variables,
    isLoading,
    refetch
  } = useSearchVariables(
    workspace.id,
    Object.values(document.variables ?? {}),
    {
      search: searchTerm,
      page: page ? Number(page) : undefined,
      pageSize: pageSize ? Number(pageSize) : undefined,
      asc,
      dsc
    }
  );

  useEffect(() => {
    // when the document changes we need to also refetch the variables
    refetch({ cancelRefetch: false });
  }, [refetch, document.variables]);

  const { mutateAsync: deleteVariable, isPending: isDeleting } =
    useDeleteVariable({
      workspaceId: workspace.id,
      d,
      pushToastNotification,
      docChange
    });

  const editRow = useCallback((row: WorkspaceVariable) => {
    setVariableForEdit(row);
    setIsCrudModalOpen(true);
  }, []);

  const kebabMenu: TableKebabMenuFormatter<WorkspaceVariable> = useCallback(
    ({ row, props, closeMenu }) => {
      return (
        <KebabMenu {...props}>
          <DropdownItemGroup>
            <DropdownItem
              id="delete"
              leftElement={<Icon name="delete" color="traffic-danger" />}
              onClick={() => {
                setVariableForDeletion(row);
                closeMenu();
              }}
            >
              <Text color="traffic-onDanger">{d("ui.common.delete")}</Text>
            </DropdownItem>
          </DropdownItemGroup>
        </KebabMenu>
      );
    },
    [d]
  );

  const renderConfirmDeleteModal = useCallback(() => {
    if (!variableForDeletion) {
      return <></>;
    }

    return (
      <ConfirmDeletionModal
        variable={variableForDeletion}
        onConfirm={() => {
          if (isDeleting) {
            return;
          }
          deleteVariable(variableForDeletion).finally(() => {
            setVariableForDeletion(undefined);
          });
        }}
        onCancel={() => setVariableForDeletion(undefined)}
      />
    );
  }, [variableForDeletion, deleteVariable, isDeleting]);

  const renderCrudModal = useCallback(() => {
    if (!isCrudModalOpen) {
      return <></>;
    }

    return (
      <ConfigurationVariablesCrudModal
        onOpenChange={() => {
          setIsCrudModalOpen(false);
          setVariableForEdit(undefined);
        }}
        workspaceVariable={variableForEdit}
        workspaceId={workspace.id}
      />
    );
  }, [isCrudModalOpen, variableForEdit, workspace.id]);

  const startCreateWizard = useCallback(() => {
    setIsCrudModalOpen(true);
  }, []);

  return (
    <PageBodyTemplate
      breadcrumbs={breadcrumbs}
      heading={d("ui.configuration.variables.title")}
      withoutScroll
    >
      <Inline gap="100" spaceBetween>
        <Inline gap="100" alignment="left">
          <SearchBar
            withDebounce
            handleChange={onChangeSearchTerm}
            placeholder={d("ui.common.search")}
            value={searchTerm ?? ""}
            autoFocus
          />
        </Inline>
        <ButtonGroup alignment="right">
          <Button
            label={d("ui.configuration.variables.create.actionButton")}
            onClick={startCreateWizard}
          ></Button>
        </ButtonGroup>
      </Inline>

      <TableWithPagination
        isLoading={isLoading}
        fillContainer
        columns={columns}
        data={variables?.items ?? []}
        itemsPerPage={variables?.page?.pageSize ?? 10}
        startingPage={page ? Number(page) : 1}
        totalCount={variables?.total ?? 0}
        noDataText={d("ui.common.noData")}
        isControlledOutside={true}
        onChangePage={onChangePage}
        handleSort={onChangeSorting}
        sort={sort}
        kebabMenu={kebabMenu}
        rowKeyAccessor="id"
        onRowClick={row => {
          editRow(row);
        }}
      />
      {renderCrudModal()}
      {renderConfirmDeleteModal()}
    </PageBodyTemplate>
  );
};

function columnDefinitions(d: Dictionary): TableColumn<WorkspaceVariable>[] {
  return [
    {
      header: "",
      key: "hasErrors",
      options: {
        width: "var(--spacing-000)",
        paddingLess: true
      },
      formatter: (value: WorkspaceVariable) => {
        return value.hasErrors ? (
          <Inline className="form-list-status-line-container">
            <StatusLine
              variant={StatusLineVariant.WARNING}
              className="form-list-status-line"
            />
          </Inline>
        ) : (
          <></>
        );
      }
    },
    {
      header: d("ui.configuration.variables.columns.name"),
      key: "name",
      canSort: true
    },
    {
      header: d("ui.configuration.variables.columns.description"),
      key: "description",
      canSort: false,
      options: {
        color: ColorText.SECONDARY
      }
    },
    {
      header: d("ui.configuration.variables.columns.value"),
      key: "reference.value",
      canSort: false,
      formatter: (data: WorkspaceVariable) => {
        if (data.isSecured && data.securedRef === undefined) {
          return (
            <Inline alignment="left" gap="100">
              <Pill
                variant={PillVariant.WARNING}
                key={`value-warning`}
                label={d(`errors.configurationVariables.fields.value.notSet`)}
                icon={PillIcon.STANDARD}
              />
            </Inline>
          );
        }
        if (data.isSecured) {
          return (
            <Inline alignment="left" gap="none">
              {[...Array(10).keys()].map(_ => (
                <Icon
                  key={_}
                  name="emergency"
                  fillStyle="filled"
                  size={IconSize.X_SMALL}
                />
              ))}
            </Inline>
          );
        }

        if (data.value === undefined) {
          return (
            <Inline alignment="left" gap="100">
              <Pill
                variant={PillVariant.WARNING}
                key={`value-warning`}
                label={d(`errors.configurationVariables.fields.value.notSet`)}
                icon={PillIcon.STANDARD}
              />
            </Inline>
          );
        }
        return data.value;
      }
    },
    {
      header: d("ui.configuration.variables.columns.isSecured"),
      key: "isSecured",
      canSort: true,
      formatter: (data: WorkspaceVariable) => {
        if (!data.isSecured) {
          return <></>;
        }
        return (
          <Inline alignment="left" gap="100">
            <Pill
              variant={PillVariant.COLORED}
              key={`${data.isSecured}`}
              label={d(
                `ui.configuration.variables.fields.isSecured.state.secured`
              )}
              icon={PillIcon.CUSTOM}
              customIcon={{ name: "lock", fillStyle: "outlined" }}
            />
          </Inline>
        );
      }
    },
    {
      header: d("ui.configuration.variables.columns.updatedAt"),
      key: "metadata.updatedAt",
      canSort: true,
      options: {
        alignment: TextAlignment.RIGHT
      },
      formatter: (data: WorkspaceVariable) => (
        <Inline alignment="left" gap="100">
          {data.metadata && (
            <Tooltip
              content={data.metadata.updatedAt}
              delay={500}
              position={FloatingWithParentPosition.BOTTOM_RIGHT}
            >
              <Text size="s" color="text-tertiary">
                {formatDate(data.metadata.updatedAt)}
              </Text>
            </Tooltip>
          )}
        </Inline>
      )
    }
  ];
}

const ConfirmDeletionModal = ({
  variable,
  onConfirm,
  onCancel = () => {}
}: {
  variable: WorkspaceVariable;
  onConfirm: () => void;
  onCancel?: () => void;
}) => {
  const d = useDictionary();

  const message = variable.isSecured
    ? d("ui.configuration.variables.delete.confirmation.secured.message", {
        variableName: variable.name
      })
    : d("ui.configuration.variables.delete.confirmation.unsecured.message", {
        variableName: variable.name
      });

  return (
    <ModalDialog isOpen>
      <Overlay isOpen />
      <ConfirmationModal
        heading={d("ui.configuration.variables.delete.confirmation.title")}
        message={message}
        confirmLabel={d("ui.common.delete")}
        onConfirm={onConfirm}
        onCancel={() => onCancel?.()}
        variant="danger"
      />
    </ModalDialog>
  );
};
