import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Alert,
  AlertVariant,
  Form,
  Loading,
  Modal,
  ModalDialog,
  Overlay,
  Stack
} from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";
import { useOutletContext } from "react-router-dom";

import { CustomError } from "@helpers/errorHelper";
import { valueUniqueness } from "@helpers/stringHelper";

import { OTAIFormField } from "@components/shared/OTAIForm/OTAIFormField";

import {
  DocChange,
  PushToastNotification
} from "@pages/workspace/WorkspaceLayout";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import { useTenantConfig } from "@src/hooks/useTenantConfig";
import { Question } from "@src/types/Question";
import {
  WorkspaceVariable,
  WorkspaceVariableForCreate,
  WorkspaceVariableForUpdateForm,
  getWorkspaceVariableForCreateSchema,
  getWorkspaceVariableForUpdateSchema
} from "@src/types/WorkspaceVariable";
import { WorkspaceDocument } from "@src/types/documentTypes";

import { useCreateVariable } from "./helpers/useCreateVariable";
import { useUpdateVariable } from "./helpers/useUpdateVariable";

export type WorkspaceVariableModalProps = {
  onOpenChange: (isOpen: boolean) => void;
  workspaceVariable?: WorkspaceVariable | null;
  workspaceId: number;
};

export const ConfigurationVariablesCrudModal = ({
  onOpenChange,
  workspaceVariable,
  workspaceId
}: WorkspaceVariableModalProps) => {
  const d = useDictionary();
  const closeModal = useCallback(() => {
    onOpenChange(false);
  }, [onOpenChange]);
  const { document } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
  }>();
  const tenantConfig = useTenantConfig();
  const localeString = tenantConfig?.locale;

  const isNameUnique = useMemo(() => {
    const asSet = new Set<string>(
      Object.values(document.variables ?? {})
        .filter(variable => workspaceVariable?.name !== variable.name)
        .map(variable => valueUniqueness(variable.name, localeString))
    );
    return (name: string) => {
      return !asSet.has(valueUniqueness(name, localeString));
    };
  }, [document.variables, localeString, workspaceVariable?.name]);

  const heading = useMemo(() => {
    if (workspaceVariable) {
      return d("ui.configuration.variables.update.title", {
        variableName: workspaceVariable.name
      });
    }
    return d("ui.configuration.variables.create.title");
  }, [d, workspaceVariable]);

  return (
    <ModalDialog isOpen onOpenChange={onOpenChange}>
      <Overlay isOpen />
      <Modal
        heading={heading}
        onOpenChange={closeModal}
        style={{ minWidth: "300px", width: "25vw" }}
      >
        {workspaceVariable ? (
          <UpdateModalBody
            workspaceVariable={workspaceVariable}
            closeModal={closeModal}
            workspaceId={workspaceId}
            nameUniquenessCheck={isNameUnique}
          />
        ) : (
          <CreateModalBody
            closeModal={closeModal}
            workspaceId={workspaceId}
            nameUniquenessCheck={isNameUnique}
          />
        )}
      </Modal>
    </ModalDialog>
  );
};

const CreateModalBody = ({
  closeModal,
  workspaceId,
  nameUniquenessCheck
}: {
  closeModal: () => void;
  workspaceId: number;
  nameUniquenessCheck: (name: string) => boolean;
}) => {
  const d = useDictionary();
  const { docChange, pushToastNotification } = useOutletContext<{
    pushToastNotification: PushToastNotification;
    docChange: DocChange;
  }>();
  const formFields = useMemo(() => crudFields(d), [d]);
  const [error, setError] = useState<CustomError | null>(null);

  const { mutateAsync: createVariable, isPending } = useCreateVariable({
    workspaceId,
    d,
    pushToastNotification,
    docChange
  });

  const handleSubmit = useCallback(
    async (data: WorkspaceVariableForCreate) => {
      createVariable(data)
        .then(() => {
          closeModal();
        })
        .catch(error => {
          console.error("Error creating variable:", error);
          setError(error);
        });
    },
    [closeModal, createVariable]
  );

  return (
    <Stack>
      <Form<WorkspaceVariableForCreate>
        schema={getWorkspaceVariableForCreateSchema(d, nameUniquenessCheck)}
        submitLabel={d("ui.common.save")}
        handleSubmit={handleSubmit}
        cancelLabel={d("ui.common.cancel")}
        handleCancel={closeModal}
        disabled={isPending}
        d={d}
      >
        <ModalFormFields formFields={formFields} />
        {error && (
          <Alert variant={AlertVariant.DANGER} width="100">
            {d("errors.configurationVariables.create")}
          </Alert>
        )}
        {isPending && <Loading size={24} />}
      </Form>
    </Stack>
  );
};

const UpdateModalBody = ({
  workspaceVariable,
  closeModal,
  workspaceId,
  nameUniquenessCheck
}: {
  workspaceVariable: WorkspaceVariable;
  closeModal: () => void;
  workspaceId: number;
  nameUniquenessCheck: (name: string) => boolean;
}) => {
  const d = useDictionary();
  const { docChange, pushToastNotification } = useOutletContext<{
    pushToastNotification: PushToastNotification;
    docChange: DocChange;
  }>();

  const formFields = useMemo(
    () => crudFields(d, workspaceVariable),
    [d, workspaceVariable]
  );
  const [error, setError] = useState<CustomError | null>(null);

  const { mutateAsync: updateVariable, isPending } = useUpdateVariable({
    workspaceId,
    d,
    pushToastNotification,
    docChange
  });

  const handleSubmit = useCallback(
    async (data: WorkspaceVariableForUpdateForm) => {
      // for plaintext value we can always update :)
      const didValueChange = (() => {
        if (!workspaceVariable.securedRef) {
          return true;
        }
        if (workspaceVariable.isSecured && data.value?.length) {
          return true;
        } else if (workspaceVariable.isSecured && !data.value) {
          return false;
        }
        return !workspaceVariable.isSecured;
      })();
      const referenceXorValue = didValueChange
        ? {
            value: data.value!
          }
        : {
            reference: workspaceVariable.securedRef!
          };

      updateVariable({
        id: workspaceVariable.id,
        name: data.name,
        description: data.description,
        wasSecured: workspaceVariable.isSecured,
        ...referenceXorValue
      })
        .then(() => {
          closeModal();
        })
        .catch(error => {
          console.error("Error updating variable:", error);
          setError(error);
        });
    },
    [
      closeModal,
      updateVariable,
      workspaceVariable.id,
      workspaceVariable.securedRef,
      workspaceVariable.isSecured
    ]
  );

  return (
    <Stack>
      <Form<WorkspaceVariableForUpdateForm>
        schema={getWorkspaceVariableForUpdateSchema(
          d,
          nameUniquenessCheck,
          !workspaceVariable?.securedRef
        )}
        submitLabel={d("ui.common.save")}
        handleSubmit={handleSubmit}
        cancelLabel={d("ui.common.cancel")}
        handleCancel={closeModal}
        disabled={isPending}
        d={d}
      >
        <ModalFormFields formFields={formFields} />
        {error && (
          <Alert variant={AlertVariant.DANGER} width="100">
            {d("errors.configurationVariables.update", {
              variableName: workspaceVariable.name
            })}
          </Alert>
        )}
        {isPending && <Loading size={24} />}
      </Form>
    </Stack>
  );
};

const ModalFormFields = ({ formFields = [] }: { formFields: CrudField[] }) => {
  const formContext = useFormContext();

  const onChangeHandler = useCallback(
    (field: CrudField, input: unknown) => {
      if (field.transform) {
        const transformed = field.transform(input as string);
        formContext.setValue(field.id, transformed);
      }
      formContext.trigger(field.id);
    },
    [formContext]
  );

  return (
    <>
      {formFields.map((field: CrudField) => (
        <OTAIFormField
          key={field.id}
          question={field}
          onlyTriggerChangeWhenBlur={false}
          disabled={field.properties?.disabled || false}
          onChange={input => onChangeHandler(field, input)}
        />
      ))}
    </>
  );
};

interface CrudField extends Question {
  transform?: (value: string) => string;
}

const crudFields = (
  d: Dictionary,
  existing?: WorkspaceVariable
): CrudField[] => {
  const isEdit = existing !== undefined;

  return [
    {
      id: "name",
      identifier: "name",
      properties: {
        placeholder: "",
        required: true,
        defaultValue: isEdit ? existing.name : ""
      },
      text: d("ui.configuration.variables.fields.name.label"),
      description: "",
      type: "text",
      transform: (value: string) => value.toUpperCase()
    },
    {
      id: "description",
      identifier: "description",
      properties: {
        placeholder: "",
        required: false,
        defaultValue: isEdit ? (existing.description ?? "") : ""
      },
      text: d("ui.configuration.variables.fields.description.label"),
      description: "",
      type: "text"
    },
    {
      id: "value",
      identifier: "value",
      properties: {
        placeholder: "",
        required: true,
        isSecret: isEdit && existing.isSecured,
        defaultValue: isEdit ? (existing.value ?? "") : ""
      },
      text: d("ui.configuration.variables.fields.value.label"),
      description: "",
      type: "text"
    },
    {
      id: "isSecured",
      identifier: "isSecured",
      properties: {
        required: true,
        disabled: isEdit, // Disable if editing
        defaultValue: isEdit ? existing.isSecured : false
      },
      text: d("ui.configuration.variables.fields.isSecured.label"),
      description: d("ui.configuration.variables.fields.isSecured.description"),
      type: "boolean"
    }
  ];
};
