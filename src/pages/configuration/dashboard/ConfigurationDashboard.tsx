import React from "react";

import { Doc } from "@automerge/automerge-repo";
import { Box, Inline, Loading, PageBodyTemplate } from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { getResourceLength } from "@helpers/OrderedMapNoState.ts";

import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs.tsx";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { buildShortPageTitle, usePageTitle } from "@src/hooks/usePageTitle.ts";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace.ts";

import { WorkspaceConfigurationButtons } from "../buttons/WorkspaceConfigurationButtons";
import "./ConfigurationDashboard.scss";
import { ConfigurationDashboardFoundationPanel } from "./ConfigurationDashboardFoundationPanel";

export const ConfigurationDashboard = () => {
  const d = useDictionary();
  const breadcrumbs = useBreadcrumbs();

  const { workspace, document } = useOutletContext<{
    workspace: Workspace;
    document: Doc<WorkspaceDocument>;
  }>();

  usePageTitle(
    buildShortPageTitle({
      section: d("ui.configuration.title"),
      context: workspace?.key ?? ""
    }),
    !!workspace?.key
  );

  if (!getResourceLength(document?.foundations) || !workspace?.id) {
    return (
      <Box height="100" contentsHeight="fill">
        <Loading size={24} />
      </Box>
    );
  }

  return (
    <PageBodyTemplate
      heading={d("ui.configuration.dashboard.title")}
      breadcrumbs={breadcrumbs}
      headingRight={
        <Inline alignment="bottom-right">
          <WorkspaceConfigurationButtons />
        </Inline>
      }
    >
      <Box width="fill">
        <Box gap="200" className="configuration-dashboard">
          <ConfigurationDashboardFoundationPanel
            foundations={document?.foundations}
          />
        </Box>
      </Box>
    </PageBodyTemplate>
  );
};
