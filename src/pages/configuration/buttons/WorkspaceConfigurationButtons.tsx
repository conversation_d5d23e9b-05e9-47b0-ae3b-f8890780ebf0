import React, { useMemo, useState } from "react";

import {
  ButtonMenu,
  ButtonVariant,
  ConfirmationModal,
  DropdownItem,
  DropdownItemGroup,
  Icon,
  ModalDialog,
  Overlay
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { useWorkspaceVersion } from "@pages/collection/home/<USER>";
import { DocChange } from "@pages/workspace/WorkspaceLayout";

import { useDictionary } from "@src/hooks/useDictionary";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace";

import { ImportModal } from "./ImportModal";
import "./WorkspaceConfigurationButtons.scss";
import { useDownloadWorkspaceConfiguration } from "./useDownloadWorkspaceConfiguration";
import { usePublishWorkspaceVersion } from "./usePublishWorkspaceVersion";

export const WorkspaceConfigurationButtons = () => {
  const {
    document: workspaceConfiguration,
    docChange,
    workspace
  } = useOutletContext<{
    document: WorkspaceDocument;
    docChange: DocChange;
    workspace: Workspace;
  }>();
  const d = useDictionary();
  const { publishWorkspaceVersion } = usePublishWorkspaceVersion({ workspace });
  const { lastPublishedVersion } = useWorkspaceVersion(workspace);
  const { downloadWorkspaceConfiguration } = useDownloadWorkspaceConfiguration(
    initiateFileDownloadForUser
  );

  const [isButtonMenuOpen, setIsButtonMenuOpen] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [confirmRevertModalOpen, setConfirmRevertModalOpen] = useState(false);

  const confirmRevertModal = useMemo(() => {
    if (!confirmRevertModalOpen) {
      return null;
    }

    const handleRevertToLastPublishedVersion = () => {
      if (lastPublishedVersion?.configuration) {
        docChange((d: Record<string, unknown>) => {
          const keysToSkipRevert = [
            "id",
            "name",
            "description",
            "metadata",
            "type"
          ];

          const keysToUpdate = [
            ...new Set([
              ...Object.keys(d),
              ...Object.keys(lastPublishedVersion.configuration)
            ])
          ].filter(key => !keysToSkipRevert.includes(key));

          const lastPublishedConfiguration =
            lastPublishedVersion.configuration as Record<string, unknown>;
          for (const key of keysToUpdate) {
            if (lastPublishedConfiguration[key]) {
              d[key] = lastPublishedConfiguration[key];
            } else {
              delete d[key];
            }
          }
        });
        setConfirmRevertModalOpen(false);
      }
    };

    return (
      <ModalDialog isOpen>
        <Overlay isOpen />
        <ConfirmationModal
          closeOnClickOutside
          variant="warning"
          heading={d("ui.workspace.revertToPublishedVersion.title")}
          message={d("ui.workspace.revertToPublishedVersion.message")}
          onConfirm={handleRevertToLastPublishedVersion}
          confirmLabel={d("ui.workspace.revertToPublishedVersion.confirmLabel")}
          onCancel={() => {
            setConfirmRevertModalOpen(false);
          }}
        />
      </ModalDialog>
    );
  }, [
    confirmRevertModalOpen,
    d,
    docChange,
    lastPublishedVersion?.configuration
  ]);

  return (
    <>
      <ButtonMenu
        label={`${d("ui.terminology.action")}s`}
        isOpen={isButtonMenuOpen}
        onOpenChange={setIsButtonMenuOpen}
        variant={ButtonVariant.PRIMARY}
        position="bottom-right"
        minWidthAsParent={false}
      >
        <DropdownItemGroup>
          <DropdownItem
            id="publish"
            key="publish"
            leftElement={<Icon name="upload_file" />}
            onClick={() => {
              publishWorkspaceVersion();
              setIsButtonMenuOpen(false);
            }}
          >
            {d("ui.configuration.dashboard.publish")}
          </DropdownItem>
        </DropdownItemGroup>
        <DropdownItemGroup hasDivider>
          <DropdownItem
            id="import"
            key="import"
            leftElement={<Icon name="upload" />}
            onClick={() => {
              setIsImportModalOpen(true);
              setIsButtonMenuOpen(false);
            }}
          >
            {d("ui.configuration.dashboard.import")}
          </DropdownItem>
          <DropdownItem
            id="export"
            key="export"
            leftElement={<Icon name="download" />}
            onClick={() => {
              downloadWorkspaceConfiguration(workspaceConfiguration);
              setIsButtonMenuOpen(false);
            }}
          >
            {d("ui.configuration.dashboard.export")}
          </DropdownItem>
        </DropdownItemGroup>
        {lastPublishedVersion && (
          <DropdownItemGroup hasDivider>
            <DropdownItem
              id="revert"
              key="revert"
              leftElement={<Icon name="history" />}
              onClick={() => {
                setConfirmRevertModalOpen(true);
                setIsButtonMenuOpen(false);
              }}
            >
              {d("ui.workspace.revertToPublishedVersion.title")}
            </DropdownItem>
          </DropdownItemGroup>
        )}
      </ButtonMenu>
      {isImportModalOpen && (
        <ImportModal
          workspaceConfiguration={workspaceConfiguration}
          onOpenChange={setIsImportModalOpen}
          workspace={workspace}
          d={d}
        />
      )}
      {confirmRevertModal}
    </>
  );
};

function initiateFileDownloadForUser(file: File) {
  const link = window.document.createElement("a");
  const url = URL.createObjectURL(file);

  link.href = url;
  link.download = file.name;
  window.document.body.appendChild(link);
  link.click();

  window.document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
}
