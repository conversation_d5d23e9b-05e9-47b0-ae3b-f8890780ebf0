import { useMutation } from "@tanstack/react-query";

import { WorkspaceVariableConfiguration } from "@src/types/WorkspaceVariable";
import { WorkspaceDocument } from "@src/types/documentTypes";

function toWorkspaceVariablesForExport(
  variables: Record<string, WorkspaceVariableConfiguration>
): Record<string, WorkspaceVariableConfiguration> {
  if (!variables) {
    return {};
  }

  return Object.fromEntries(
    Object.entries(variables).map(([key, variable]) => [
      key,
      {
        ...variable,
        securedRef: undefined,
        value: variable.isSecured ? undefined : variable.value
      }
    ])
  );
}

export const useDownloadWorkspaceConfiguration = (
  initiateFileDownloadForUser: (file: File) => void
) => {
  const { mutate: downloadWorkspaceConfiguration } = useMutation({
    // NB: Currently we can export purely from the WorkspaceConfiguration Document but in the future this may be delegated to the BE
    mutationFn: async (workspaceConfiguration: WorkspaceDocument) => {
      const configurationWithoutVariables = {
        ...workspaceConfiguration,
        variables: toWorkspaceVariablesForExport(
          workspaceConfiguration.variables
        )
      };

      const contentAsString = JSON.stringify(configurationWithoutVariables);
      const bytes = new TextEncoder().encode(contentAsString);

      const file = new File(
        [bytes],
        `${workspaceConfiguration.key}-export-${new Date().toISOString()}.json`,
        {
          type: "text/plain"
        }
      );

      return file;
    },
    onSuccess: (file: File) => {
      initiateFileDownloadForUser(file);
    }
  });

  return {
    downloadWorkspaceConfiguration
  };
};
