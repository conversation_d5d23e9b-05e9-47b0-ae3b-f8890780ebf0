import React, { use<PERSON>allback, useMemo, useState } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Button,
  ButtonGroup,
  ConfirmationModal,
  DropdownItem,
  DropdownItemCheckbox,
  DropdownItemGroup,
  DropdownMenu,
  FontWeight,
  Icon,
  IconButton,
  Inline,
  KebabMenu,
  ModalDialog,
  Overlay,
  PageBodyTemplate,
  Pill,
  PillVariant,
  SearchBar,
  SelectValue,
  StatusCircleVariant,
  StatusLine,
  StatusLineVariant,
  TableColumn,
  TableKebabMenuFormatter,
  TableWithPagination,
  Text,
  TextAlignment,
  Tooltip,
  matchesSearchTerm,
  returnStringIfTrue,
  useQueryParams,
  useTableSort
} from "@oneteam/onetheme";
import {
  generatePath,
  useNavigate,
  useOutletContext,
  useParams,
  useSearchParams
} from "react-router-dom";

import { DocumentErrorHelper } from "@helpers/DocumentErrorHelper.ts";
import { mapOverResource } from "@helpers/OrderedMapNoState";
import { PaginatedTable, Sort } from "@helpers/PaginatedTable.ts";
import { customNanoId } from "@helpers/customNanoIdHelper";

import { ConfigurationLabelsSelect } from "@components/shared/ConfigurationLabelsSelect/ConfigurationLabelsSelect";

import { formatDate } from "@pages/collection/flows/FlowExecutionHelper";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { routeConstants } from "@src/constants/routeConstants";
import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs.tsx";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import { buildShortPageTitle, usePageTitle } from "@src/hooks/usePageTitle.ts";
import {
  FlowConfiguration,
  FlowConfigurationStatusType
} from "@src/types/FlowConfiguration/FlowConfiguration";
import { LabelAvailableTo, labelColorToPill } from "@src/types/Label";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace.ts";

import "./ConfigurationFlowList.scss";
import { ConfigurationFlowListModal } from "./ConfigurationFlowListModal";
import { findStepInFlowConfigurationByTextFilter } from "./flowConfigurationHelper";

export const ConfigurationFlowList = () => {
  const d = useDictionary();
  const navigate = useNavigate();
  const breadcrumbs = useBreadcrumbs();
  const params = useParams();
  const { queryParams, updateQueryParams } = useQueryParams({
    initialDefaultIfNone: {
      page: "1",
      status: FlowConfigurationStatusType.ACTIVE
    },
    useSearchParams
  });
  const [flowConfigToEdit, setFlowConfigToEdit] = useState<
    FlowConfiguration | undefined
  >(undefined);
  const [flowModalOpen, setFlowModalOpen] = useState(false);
  const [filterKebabIsOpen, setFilterKebabIsOpen] = useState(false);
  const [confirmDeleteFlowId, setConfirmDeleteFlowId] = useState<
    string | undefined
  >(undefined);

  const { docChange, document, docErrorHelper, workspace } = useOutletContext<{
    docChange: DocChange;
    document: Doc<WorkspaceDocument>;
    docErrorHelper: DocumentErrorHelper;
    workspace: Workspace;
  }>();

  usePageTitle(
    buildShortPageTitle({
      section: d("ui.configuration.flows.title"),
      context: workspace?.key ?? ""
    }),
    !!workspace?.key
  );

  const configurationFlows: FlowConfiguration[] = useMemo(() => {
    const tempFlows = mapOverResource(document.flows, flow => flow);
    tempFlows?.forEach((flow: FlowConfiguration) => {
      flow.hasErrors =
        docErrorHelper.getErrorCountByPrefix(`$.flows.entities.${flow?.id}`) >
        0;
    });
    return tempFlows;
  }, [document, docErrorHelper]);

  const statusOptions = Object.values(FlowConfigurationStatusType).map(
    status => ({
      label: d(`ui.configuration.flows.status.${status}.label`),
      value: status
    })
  );

  const { textFilter, page, pageSize, asc, dsc, status, labels, showHidden } =
    useMemo(
      () => ({
        textFilter: queryParams.get("search") ?? undefined,
        page: queryParams.get("page") ?? undefined,
        pageSize: queryParams.get("pageSize") ?? undefined,
        asc: queryParams.get("asc") ?? undefined,
        dsc: queryParams.get("dsc") ?? undefined,
        status: queryParams.get("status") ?? undefined,
        labels: queryParams.get("labels") ?? undefined,
        showHidden: queryParams.get("showHidden") ?? undefined
      }),
      [queryParams]
    );

  const flowColumns = useMemo(
    () => getColumns(d, document, textFilter),
    [d, document, textFilter]
  );

  const doUpdateQueryParams = useCallback(
    (newParams: object) => {
      const current = {
        page,
        pageSize,
        search: textFilter,
        asc,
        dsc,
        status,
        labels,
        showHidden
      };
      // merge new properties into current
      const updatedQueryParams = {
        ...current,
        ...newParams
      };
      if (!updatedQueryParams.showHidden) {
        delete updatedQueryParams.showHidden;
      }
      updateQueryParams(updatedQueryParams);
    },
    [
      page,
      pageSize,
      textFilter,
      asc,
      dsc,
      status,
      labels,
      showHidden,
      updateQueryParams
    ]
  );

  const onChangePage = useCallback(
    (newPage: number) => {
      doUpdateQueryParams({ page: String(newPage) });
    },
    [doUpdateQueryParams]
  );

  const onChangeSearchTerm = useCallback(
    (input: string) => {
      doUpdateQueryParams({
        search: input,
        page: "1" // reset page to 1 when changing filter criteria since the total count may change
      });
    },
    [doUpdateQueryParams]
  );

  const handleStatusSelect = useCallback(
    (value?: SelectValue) => {
      doUpdateQueryParams({ status: value?.toString() });
    },
    [doUpdateQueryParams]
  );

  const handleLabelSelect = useCallback(
    (value?: SelectValue) => {
      doUpdateQueryParams({ labels: value?.toString() });
    },
    [doUpdateQueryParams]
  );

  const handleToggleShowHiddenFlows = useCallback(
    (isChecked?: boolean) => {
      doUpdateQueryParams({ showHidden: isChecked?.toString() });
    },
    [doUpdateQueryParams]
  );

  const { sort, handleSetSort } = useTableSort<FlowConfiguration>({
    columns: flowColumns,
    queryParams,
    updateQueryParams: (newParams: { [key: string]: string | undefined }) => {
      doUpdateQueryParams({
        page: newParams.page,
        search: textFilter,
        asc: newParams.asc,
        dsc: newParams.dsc,
        status: status
      });
    }
  });

  const table = useMemo(() => {
    return new PaginatedTable<FlowConfiguration>(
      configurationFlows,
      page ? parseInt(page) : 1,
      [
        (data: FlowConfiguration) => {
          if (!textFilter) {
            return true;
          }
          const nameMatches = matchesSearchTerm({
            searchTerm: textFilter,
            obj: data,
            keysToMatch: ["name"]
          });
          const stepMatches = !!findStepInFlowConfigurationByTextFilter(
            data,
            textFilter
          );
          return nameMatches || stepMatches;
        },
        (data: FlowConfiguration) => {
          const s = status?.split(",") ?? [];
          return (
            s.length > 0 &&
            s.includes(data.status ?? FlowConfigurationStatusType.ACTIVE)
          );
        },
        (data: FlowConfiguration) => {
          const l = labels?.split(",") ?? [];
          return (
            l.length === 0 ||
            (!!data.labels &&
              l.every(labelId => data.labels?.includes(labelId)))
          );
        },
        (data: FlowConfiguration) =>
          showHidden === "true" || !data.properties?.hidden
      ],
      Sort.from(asc, dsc)
    );
  }, [
    configurationFlows,
    page,
    asc,
    dsc,
    textFilter,
    status,
    labels,
    showHidden
  ]);

  const data = useMemo(() => {
    const hasWarning = {
      variant: StatusCircleVariant.WARNING,
      message: d("ui.configuration.flows.step.variants.action.noActionWarning")
    };

    return table
      .dataForPage()
      .map(flow => {
        return {
          ...flow,
          hasWarning: flow.start && flow.start !== "" ? null : hasWarning
        };
      })
      .sort((a, b) =>
        a.status !== FlowConfigurationStatusType.INACTIVE &&
        b.status === FlowConfigurationStatusType.INACTIVE
          ? -1
          : 1
      );
  }, [table, d]);

  const getFilterComponent = () => {
    return (
      <Inline
        gap="150"
        alignment="left"
        style={{ alignItems: "center", minWidth: "10%" }}
        width="100"
      >
        <ConfigurationLabelsSelect
          name="labels"
          value={labels?.split(",") ?? []}
          labelFor={LabelAvailableTo.FLOW_CONFIGURATION}
          onChange={handleLabelSelect}
          placeholder={d("ui.configuration.labels.filter.placeholder")}
          width="fit"
          withSelectAll={false}
        />
        <DropdownMenu
          isOpen={filterKebabIsOpen}
          className="flows-list-status-line-container"
          onOpenChange={setFilterKebabIsOpen}
          trigger={({ onClick }) => (
            <IconButton
              name="filter_alt"
              onClick={onClick}
              color={
                showHidden === "true" ||
                status?.includes(FlowConfigurationStatusType.INACTIVE)
                  ? "color"
                  : "primary"
              }
            />
          )}
        >
          <DropdownItemGroup
            title={d("ui.configuration.flows.status.filter.title")}
          >
            {statusOptions.map(option => (
              <DropdownItemCheckbox
                key={option.value}
                id={`status-${option.value}`}
                isChecked={status?.split(",").includes(option.value) ?? false}
                onChange={isChecked =>
                  handleStatusSelect(
                    isChecked
                      ? [
                          ...(status ? (status?.split(",") ?? []) : []),
                          option.value
                        ]
                      : (status?.split(",").filter(s => s !== option.value) ??
                          undefined)
                  )
                }
              >
                {option.label}
              </DropdownItemCheckbox>
            ))}
          </DropdownItemGroup>
          <DropdownItemGroup hasDivider>
            <DropdownItemCheckbox
              id="showHidden"
              leftElement={<Icon name="visibility_off" />}
              onChange={handleToggleShowHiddenFlows}
              isChecked={showHidden === "true"}
              description={d("ui.configuration.flows.showHidden.description")}
            >
              {d("ui.configuration.flows.showHidden.label")}
            </DropdownItemCheckbox>
          </DropdownItemGroup>
        </DropdownMenu>
      </Inline>
    );
  };

  const handleDuplicateFlow = useCallback(
    (flow: FlowConfiguration) => {
      const newFlowName = d("ui.configuration.flows.duplicateName", {
        name: flow.name
      });
      docChange(d => {
        const newFlowId = customNanoId();
        const newFlow = {
          ...JSON.parse(JSON.stringify(flow)),
          id: newFlowId,
          name: newFlowName
        };
        d.flows.entities[newFlowId] = newFlow;
        d.flows.order.push(newFlowId);
      });
    },
    [d, docChange]
  );

  const handleToggleFlowHidden = useCallback(
    (flow: FlowConfiguration) => {
      docChange(d => {
        const updatedHidden = flow.properties?.hidden ? false : true;
        const flowToUpdate = d.flows.entities[flow.id];
        flowToUpdate.properties ??= {};
        flowToUpdate.properties.hidden = updatedHidden;
      });
    },
    [docChange]
  );

  const kebabMenu: TableKebabMenuFormatter<FlowConfiguration> = ({
    row,
    props,
    closeMenu
  }) => {
    return (
      <KebabMenu {...props}>
        <DropdownItemGroup>
          <DropdownItem
            id="update"
            leftElement={<Icon name="edit" />}
            onClick={() => {
              setFlowConfigToEdit(row);
              setFlowModalOpen(true);
              closeMenu();
            }}
          >
            {d("ui.common.update")}
          </DropdownItem>
          <DropdownItem
            id="duplicate"
            leftElement={<Icon name="difference" />}
            onClick={() => {
              handleDuplicateFlow(row);
              closeMenu();
            }}
          >
            {d("ui.common.duplicate")}
          </DropdownItem>
        </DropdownItemGroup>
        <DropdownItemGroup hasDivider>
          <DropdownItem
            id="hidden"
            leftElement={
              <Icon
                name={row.properties?.hidden ? "visibility" : "visibility_off"}
              />
            }
            onClick={() => {
              handleToggleFlowHidden(row);
              closeMenu();
            }}
          >
            {row.properties?.hidden ? d("ui.common.show") : d("ui.common.hide")}
          </DropdownItem>
          <DropdownItem
            id="status"
            leftElement={
              <Icon
                name={
                  row.status === FlowConfigurationStatusType.INACTIVE
                    ? "add_circle"
                    : "do_not_disturb_on"
                }
              />
            }
            onClick={() => {
              docChange(d => {
                d.flows.entities[row.id].status =
                  row.status === FlowConfigurationStatusType.INACTIVE
                    ? FlowConfigurationStatusType.ACTIVE
                    : FlowConfigurationStatusType.INACTIVE;
              });

              closeMenu();
            }}
            description={
              FlowConfigurationStatusType.INACTIVE
                ? d("ui.configuration.flows.status.active.action.description")
                : d("ui.configuration.flows.status.inactive.action.description")
            }
          >
            {row.status === FlowConfigurationStatusType.INACTIVE
              ? d("ui.configuration.flows.status.active.action.label")
              : d("ui.configuration.flows.status.inactive.action.label")}
          </DropdownItem>
        </DropdownItemGroup>
        <DropdownItemGroup hasDivider>
          <DropdownItem
            id="delete"
            leftElement={<Icon name="delete" color="traffic-danger" />}
            onClick={() => {
              setConfirmDeleteFlowId(row.id);
              closeMenu();
            }}
          >
            <Text color="traffic-onDanger">{d("ui.common.delete")}</Text>
          </DropdownItem>
        </DropdownItemGroup>
      </KebabMenu>
    );
  };

  const confirmDeleteModal = useMemo(() => {
    if (!confirmDeleteFlowId) {
      return null;
    }

    return (
      <ModalDialog isOpen>
        <Overlay isOpen />
        <ConfirmationModal
          closeOnClickOutside
          variant="danger"
          heading={d("ui.configuration.flows.deleteModal.title")}
          message={d("ui.configuration.flows.deleteModal.message")}
          onConfirm={() => {
            if (confirmDeleteFlowId) {
              setConfirmDeleteFlowId(undefined);
              docChange(d => {
                delete d.flows.entities[confirmDeleteFlowId];
                d.flows.order = d.flows.order.filter(
                  id => id !== confirmDeleteFlowId
                );
              });
              setConfirmDeleteFlowId(undefined);
            }
          }}
          confirmLabel={d("ui.configuration.flows.deleteModal.confirmLabel")}
          onCancel={() => {
            setConfirmDeleteFlowId(undefined);
          }}
        />
      </ModalDialog>
    );
  }, [confirmDeleteFlowId, d, docChange]);

  return (
    <PageBodyTemplate
      heading={d("ui.configuration.flows.title")}
      breadcrumbs={breadcrumbs}
      overlay={
        <>
          {flowModalOpen && (
            <ConfigurationFlowListModal
              onOpenChange={() => {
                setFlowModalOpen(false);
                setFlowConfigToEdit(undefined);
              }}
              flowConfigToEdit={flowConfigToEdit}
            />
          )}
          {confirmDeleteModal}
        </>
      }
      withoutScroll
    >
      <Inline gap="100" spaceBetween wrap>
        <Inline gap="100" alignment="left">
          <SearchBar
            withDebounce
            handleChange={onChangeSearchTerm}
            placeholder={d("ui.common.search")}
            value={textFilter ?? ""}
            autoFocus
            style={{ minWidth: "164px", width: "164px" }}
          />
          {getFilterComponent()}
        </Inline>
        <ButtonGroup alignment="right">
          <Button
            label={d("ui.configuration.flows.createButton")}
            onClick={() => {
              setFlowModalOpen(true);
            }}
          />
        </ButtonGroup>
      </Inline>
      <TableWithPagination
        handleSort={handleSetSort}
        sort={sort}
        fillContainer
        columns={flowColumns}
        data={data}
        itemsPerPage={table.pageSize}
        startingPage={table.page}
        kebabMenu={kebabMenu}
        totalCount={table.size()}
        noDataText={d("ui.common.noData")}
        isControlledOutside={true}
        onChangePage={onChangePage}
        rowKeyAccessor="id"
        onRowClick={flow => {
          navigate(
            generatePath(routeConstants.configurationFlow, {
              ...params,
              configurationFlowId: flow.id
            })
          );
        }}
      />
    </PageBodyTemplate>
  );
};

function getColumns(
  d: Dictionary,
  document: WorkspaceDocument,
  textFilter: string | undefined
): TableColumn<FlowConfiguration>[] {
  return [
    {
      header: "",
      key: "hasErrors",
      options: {
        width: "var(--spacing-000)",
        paddingLess: true
      },
      formatter: (value: FlowConfiguration) => {
        if (value.hasErrors || value.hasWarning) {
          return (
            <Inline
              className={`flows-list-status-line-container ${returnStringIfTrue(
                value.status === FlowConfigurationStatusType.INACTIVE ||
                  value.properties?.hidden,
                "flows-list-low-opacity"
              )}`}
            >
              <StatusLine
                variant={
                  value.hasErrors
                    ? StatusLineVariant.DANGER
                    : StatusLineVariant.WARNING
                }
                className="flows-list-status-line"
                key={`${value.id}`}
              />
            </Inline>
          );
        } else {
          return <></>;
        }
      }
    },
    {
      header: d("ui.forms.fields.name.label"),
      key: "name",
      canSort: true,
      formatter: (data: FlowConfiguration) => {
        const matchingSteps = (() => {
          if (!textFilter) {
            return <></>;
          }

          const firstMatchingStep = findStepInFlowConfigurationByTextFilter(
            data,
            textFilter
          );
          if (!firstMatchingStep) {
            return <></>;
          }
          const stepVariantLabel = d(
            `ui.configuration.flows.step.variants.${firstMatchingStep.variant}.label`
          );
          return (
            <Inline>
              <Text color="text-tertiary" size="xs">
                {`${stepVariantLabel} - ${firstMatchingStep.name}`}
              </Text>
            </Inline>
          );
        })();

        return (
          <Inline
            alignment="left"
            gap="100"
            className={returnStringIfTrue(
              data.status === FlowConfigurationStatusType.INACTIVE ||
                data.properties?.hidden,
              "flows-list-low-opacity"
            )}
          >
            {data.properties?.hidden && (
              <Icon
                className={"flows-list-low-opacity_pill"}
                key={data.id}
                name="visibility_off"
                color="text-primary"
              />
            )}
            <Text weight={FontWeight.MEDIUM}>{data.name}</Text>
            {data.status === FlowConfigurationStatusType.INACTIVE && (
              <Pill
                className={"flows-list-low-opacity_pill"}
                key={data.id}
                label={d("ui.configuration.flows.status.inactive.label")}
              />
            )}
            {matchingSteps}
          </Inline>
        );
      }
    },
    {
      header: d("ui.configuration.labels.title"),
      key: "labels",
      formatter: (data: FlowConfiguration) => {
        if (!data.labels?.length) {
          return;
        }

        return (
          <Inline
            alignment="left"
            gap="100"
            className={returnStringIfTrue(
              data.status === FlowConfigurationStatusType.INACTIVE,
              "flows-list-labels-inactive"
            )}
          >
            {data.labels.map((labelId: string) => {
              const label = document.labels[labelId];
              return (
                <Pill
                  key={label.id}
                  label={label.name}
                  {...(label.color
                    ? labelColorToPill[label.color]
                    : {
                        variant: PillVariant.NEUTRAL
                      })}
                />
              );
            })}
          </Inline>
        );
      }
    },
    {
      header: d("ui.common.lastModified"),
      key: "metadata.updatedAt",
      canSort: true,
      options: {
        alignment: TextAlignment.RIGHT
      },
      formatter: (data: FlowConfiguration) => (
        <Inline alignment="left" gap="100">
          {data.metadata && (
            <Tooltip content={data.metadata.updatedAt} delay={500}>
              <Text size="s" color="text-tertiary">
                {formatDate(data.metadata.updatedAt)}
              </Text>
            </Tooltip>
          )}
        </Inline>
      )
    }
  ];
}
