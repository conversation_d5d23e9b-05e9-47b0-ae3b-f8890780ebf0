import { matchesSearchTerm } from "@oneteam/onetheme";

import { FlowConfiguration } from "@src/types/FlowConfiguration/FlowConfiguration";
import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { IteratorStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

function stepMatchingPredicate(
  step: FlowStep,
  textFilter: string | undefined
): boolean {
  if (!textFilter) {
    return true;
  }

  return (
    step.id === textFilter ||
    matchesSearchTerm({
      searchTerm: textFilter,
      obj: step,
      keysToMatch: ["name"]
    })
  );
}

// find the step in the config that matches the textFilter
export function findStepInFlowConfigurationByTextFilter(
  data: FlowConfiguration,
  textFilter: string | undefined
): FlowStep | undefined {
  if (!textFilter) {
    return undefined;
  }

  const steps = [
    ...Object.values(data.steps),
    ...Object.values(data.triggers ?? {})
  ];
  while (steps.length > 0) {
    const step = steps.shift();
    if (!step) {
      continue;
    }

    if (stepMatchingPredicate(step, textFilter)) {
      return step;
    }

    if (step.variant === "iterator") {
      const iteratorConfig = (step.properties as IteratorStepProperties)
        .configuration;
      steps.push(...Object.values(iteratorConfig.steps));
    }
  }
  return undefined;
}
