import React, {
  CSSProperties,
  PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import {
  Edge,
  EdgeChange,
  NodeChange,
  OnNodesChange,
  ReactFlow,
  applyEdgeChanges,
  applyNodeChanges,
  useEdgesState,
  useNodesInitialized,
  useNodesState,
  useReactFlow
} from "@xyflow/react";

import {
  EdgeTypes,
  buildEdgesAndNodesFromDocument,
  getPositionedSteps
} from "@pages/configuration/flows/FlowCanvas/FlowCanvasHelper.ts";

import { FlowConfiguration } from "@src/types/FlowConfiguration/FlowConfiguration";
import { FlowStepId } from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { useConfigurationFlowContext } from "../ConfigurationFlowContext";
import { AddButtonEdge } from "./CanvasEdges/AddButtonEdge";
import { ConditionEdge } from "./CanvasEdges/ConditionEdge";
import { AddTriggerNode } from "./CanvasNodes/AddTriggerNode";
import { FlowAddNode } from "./CanvasNodes/FlowAddNode";
import { FlowStepNode } from "./CanvasNodes/FlowStepNode";
import { FlowStepNodeProps } from "./CanvasNodes/FlowStepNodeProps";
import { IteratorStartStep } from "./CanvasNodes/IteratorStartStep";
import "./FlowCanvas.scss";

const transition = "all 0.2s";

interface FlowCanvasProps {
  configurationFlow: FlowConfiguration;
  isIterator?: boolean;
  interactive?: boolean;
  centreOnLoad?: boolean;
  stepIdOnParentFlow?: FlowStepId; // for flows in flows or iterator steps
  spacing?: { x: number; y: number };
}

export const FlowCanvas = ({
  configurationFlow,
  isIterator = false,
  interactive = false,
  centreOnLoad = false,
  spacing: separation,
  stepIdOnParentFlow = undefined,
  children
}: PropsWithChildren<FlowCanvasProps>) => {
  const edgeTypes = {
    [EdgeTypes.AddButtonEdge]: AddButtonEdge,
    [EdgeTypes.ConditionEdge]: ConditionEdge
  };

  const nodeTypes = {
    flowStep: FlowStepNode,
    addStepNode: FlowAddNode,
    addTriggerNode: AddTriggerNode,
    iteratorStartStep: IteratorStartStep
  };
  const initialNodesAndEdges = useMemo(
    () =>
      buildEdgesAndNodesFromDocument(
        configurationFlow,
        isIterator,
        stepIdOnParentFlow
      ),
    [configurationFlow, isIterator, stepIdOnParentFlow]
  );

  const [edges, setEdges] = useEdgesState(initialNodesAndEdges.edges);
  const [nodes, setNodes] = useNodesState(initialNodesAndEdges.nodes);

  const [hasInitiallyLaidOut, setHasInitiallyLaidOut] = useState(false);
  const [updatedLayout, setUpdatedLayout] = useState(false);
  const [initialFitView, setInitialFitView] = useState(false);

  const { zoomLevel, mainViewportRef, setRefreshFlowCanvas } =
    useConfigurationFlowContext();

  const { fitView, getEdges, zoomTo, viewportInitialized } = useReactFlow();

  const [floatingLayerStyle, setFloatingLayerStyle] = useState<CSSProperties>({
    opacity: 0,
    transition
  });
  const isMounted = useRef(false);

  useEffect(() => {
    isMounted.current = true;

    if (isMounted.current) {
      setTimeout(() => {
        setFloatingLayerStyle({ opacity: 1, transition });
      }, 100);
    }

    return () => {
      setFloatingLayerStyle({ opacity: 0, transition });
      isMounted.current = false;
    };
  }, []);

  const refreshFlowCanvas = useCallback(() => {
    const { nodes: updatedNodes, edges: updatedEdges } =
      buildEdgesAndNodesFromDocument(
        configurationFlow,
        isIterator,
        stepIdOnParentFlow
      );
    setNodes(updatedNodes);
    setEdges(updatedEdges);
  }, [configurationFlow, isIterator, setEdges, setNodes, stepIdOnParentFlow]);

  // Update the refreshFlowCanvas function in the context
  useEffect(() => {
    setRefreshFlowCanvas(() => refreshFlowCanvas);
  }, [setRefreshFlowCanvas, refreshFlowCanvas]);

  // build nodes from document whenever there's a change
  useEffect(() => {
    refreshFlowCanvas();
    // TODO: make this only listen to a change on the path structure, not the whole configurationFlow doc
  }, [
    configurationFlow,
    setNodes,
    setEdges,
    isIterator,
    stepIdOnParentFlow,
    refreshFlowCanvas
  ]);

  useEffect(() => {
    if (hasInitiallyLaidOut && !updatedLayout) {
      return;
    }

    // Initial layout or updated layout
    const originalZoom = zoomLevel;

    const { nodes: positionedNodes, edges: positionedEdges } =
      getPositionedSteps(nodes, edges, separation);
    setNodes(positionedNodes);
    setEdges(positionedEdges);
    setHasInitiallyLaidOut(true);
    setUpdatedLayout(false);
    if (originalZoom && interactive) {
      setTimeout(() => {
        mainViewportRef?.zoomTo(originalZoom);
      }, 10);
    }
  }, [
    setEdges,
    setNodes,
    hasInitiallyLaidOut,
    getEdges,
    updatedLayout,
    nodes,
    edges,
    separation,
    zoomLevel,
    zoomTo,
    interactive,
    mainViewportRef
  ]);

  const nodesInitialized = useNodesInitialized();

  useEffect(() => {
    // Setup initial viewport
    if (
      !updatedLayout &&
      !initialFitView &&
      hasInitiallyLaidOut &&
      viewportInitialized &&
      nodesInitialized &&
      centreOnLoad
    ) {
      fitView({ padding: 0.2, maxZoom: 1 });
      setInitialFitView(true);
    }
  }, [
    fitView,
    hasInitiallyLaidOut,
    initialFitView,
    updatedLayout,
    centreOnLoad,
    nodes,
    nodesInitialized,
    viewportInitialized
  ]);

  const onEdgesChange = useCallback(
    (changes: EdgeChange<Edge>[]) => {
      setEdges(newEdges => applyEdgeChanges(changes, newEdges));
      setUpdatedLayout(true);
    },
    [setEdges]
  );

  const onNodesChange = useCallback(
    (changes: NodeChange<FlowStepNodeProps>[]) => {
      setNodes(newNodes => applyNodeChanges(changes, newNodes));
      setUpdatedLayout(true);
    },

    [setNodes]
  );
  return (
    <ReactFlow
      className="flow-canvas"
      edgesFocusable={false}
      style={floatingLayerStyle}
      edgeTypes={edgeTypes}
      nodeTypes={nodeTypes}
      panOnScroll={interactive}
      zoomOnScroll={interactive}
      zoomOnPinch={interactive}
      panOnDrag={interactive}
      zoomOnDoubleClick={interactive}
      onMoveStart={() => {
        if (interactive) {
          return;
        }
        const mouseUpEvent = new MouseEvent("mouseup", {
          bubbles: true,
          cancelable: true,
          view: window
        });
        document.dispatchEvent(mouseUpEvent);
      }}
      nodesDraggable={false}
      draggable={false}
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange as OnNodesChange}
      onEdgesChange={onEdgesChange}
      proOptions={{ hideAttribution: true }}
      defaultEdgeOptions={{}}
      minZoom={interactive ? 0.25 : 1}
      maxZoom={interactive ? 2 : 1}
    >
      {children}
    </ReactFlow>
  );
};
