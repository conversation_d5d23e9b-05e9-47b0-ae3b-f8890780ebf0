import React, { useMemo } from "react";

import {
  Box,
  Heading,
  HeadingSize,
  Inline,
  Stack,
  Text,
  WhiteboardBlock,
  getClassNames
} from "@oneteam/onetheme";

import { FlowStepVariantPill } from "@components/flows/FlowStepVariantPill/FlowStepVariantPill";
import { VariableFieldPartVariable } from "@components/flows/VariableField/VariableFieldPart/VariableFieldPartVariable";
import { WhiteboardStatusCircle } from "@components/shared/WhiteboardStatusCircle.tsx";

import { useConfigurationFlowErrorCount } from "@pages/configuration/flows/hooks/useConfigurationFlowErrorCount";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  FlowStep,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { IteratorStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { FlowStepTypeConfiguration } from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";

import { FlowStepKebabMenu } from "../FlowStepKebabMenu/FlowStepKebabMenu";
import "./IteratorFlowStepWrapper.scss";

export const IteratorFlowStepWrapper = ({
  flowStep,
  onClick,
  handleDelete,
  handleDuplicate,
  isHighlighted = false,
  isSelected = false,
  isDisabled = false,
  iteratorDepth = 1,
  flowStepTypeConfiguration,
  bottomContents = <></>,
  children
}: React.PropsWithChildren<{
  flowStep: FlowStep<IteratorStepProperties>;
  onClick?: () => void;
  handleDelete?: () => void;
  handleDuplicate?: () => void;
  isDisabled?: boolean;
  iteratorDepth?: number;
  flowStepTypeConfiguration?: FlowStepTypeConfiguration;
  isHighlighted?: boolean;
  isSelected?: boolean;
  bottomContents?: React.ReactNode;
}>) => {
  const list = useMemo(() => {
    return flowStep.properties.inputs?.list;
  }, [flowStep.properties.inputs]);

  const d = useDictionary();
  const errorCount = useConfigurationFlowErrorCount(flowStep);

  return (
    <Stack gap="050" className="iterator-flow-step-wrapper__container">
      <WhiteboardBlock
        onClick={onClick}
        className={getClassNames([
          "nopan",
          "nodrag",
          "iterator-flow-step-wrapper",
          `iterator-flow-step-wrapper--depth-${Math.min(iteratorDepth, 3)}`
        ])}
        isHighlighted={isHighlighted}
        isSelected={isSelected}
        isDisabled={isDisabled}
        style={{
          backgroundColor: `rgba(#cfd3e0, ${iteratorDepth * 0.3})`
        }}
      >
        <Stack className="iterator-flow-step-wrapper__content" gap="100">
          {errorCount > 0 && (
            <WhiteboardStatusCircle
              tooltipContent={d("errors.configurationFlow.tooltip")}
            />
          )}
          <Inline
            className="iterator-flow-step-wrapper__top"
            width="100"
            spaceBetween
          >
            <Inline gap="100" alignment="left">
              <FlowStepVariantPill
                variant={flowStep.variant as FlowStepVariant}
              />
              {/* TODO: make dynamic */}
              <Inline gap="025" alignment="left">
                <Text size="s" color="text-secondary">
                  {flowStepTypeConfiguration?.name}
                </Text>
                <VariableFieldPartVariable
                  id={`iterator-list-${flowStep.id}`}
                  value={String(list ?? "").replaceAll(/{{|}}/g, "")}
                />
              </Inline>
            </Inline>
            <FlowStepKebabMenu
              flowStep={flowStep}
              handleEdit={!isSelected ? onClick : undefined}
              handleDelete={() => handleDelete?.()}
              handleDuplicate={() => handleDuplicate?.()}
            />
          </Inline>
          <Stack gap="050">
            <Heading size={HeadingSize.S} maxLines={3}>
              {flowStep.name}
            </Heading>
          </Stack>
          {/* Iterator variables before content */}
          <Box
            className="iterator-flow-step-wrapper__bottom"
            alignment="center"
          >
            {children}
          </Box>
        </Stack>
      </WhiteboardBlock>
      {bottomContents}
    </Stack>
  );
};
