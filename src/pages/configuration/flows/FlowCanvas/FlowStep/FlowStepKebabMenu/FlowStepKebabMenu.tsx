import React, { useState } from "react";

import {
  DropdownItem,
  DropdownItemGroup,
  FloatingWithParentPosition,
  Icon,
  KebabMenu,
  Text,
  getClassNames
} from "@oneteam/onetheme";
import { CopyToClipboard } from "react-copy-to-clipboard";

import { commonIcons } from "@src/constants/iconConstants";
import { useDictionary } from "@src/hooks/useDictionary";
import {
  FlowStep,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { ConditionStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

import "./FlowStepKebabMenu.scss";

export const FlowStepKebabMenu = ({
  handleEdit,
  handleDuplicate,
  handleDelete,
  handleRename,
  flowStep,
  disabled
}: {
  handleEdit?: () => void;
  handleDuplicate?: () => void;
  handleDelete?: () => void;
  handleRename?: () => void;
  flowStep?: FlowStep;
  disabled?: boolean;
}) => {
  const d = useDictionary();
  const [isOpen, setIsOpen] = useState(false);

  const callWithClose = (callback: () => void) => () => {
    setIsOpen(false);
    callback();
  };

  if (
    disabled ||
    (!handleEdit && !handleDuplicate && !handleDelete && !handleRename)
  ) {
    return <></>;
  }

  const serializeStep = (flowStep: FlowStep) => {
    if (flowStep.variant === FlowStepVariant.CONDITION) {
      const conditionStep = structuredClone(
        flowStep
      ) as FlowStep<ConditionStepProperties>;
      for (const branch in conditionStep.properties.branches) {
        conditionStep.properties.branches[branch].next = null;
      }
      return JSON.stringify(conditionStep);
    }

    return JSON.stringify(flowStep);
  };

  return (
    <KebabMenu
      className={getClassNames([
        "nodrag",
        "nopan",
        "flow-step-kebab-menu",
        `flow-step-kebab-menu--${isOpen ? "open" : "closed"}`
      ])}
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      position={FloatingWithParentPosition.BOTTOM_RIGHT}
    >
      {(!!handleEdit || !!handleDuplicate || !!handleRename) && (
        <DropdownItemGroup>
          {handleRename && (
            <DropdownItem
              leftElement={<Icon {...commonIcons.rename} />}
              onClick={callWithClose(handleRename)}
            >
              {d("ui.common.rename")}
            </DropdownItem>
          )}
          {handleEdit && (
            <DropdownItem
              leftElement={<Icon {...commonIcons.update} />}
              onClick={callWithClose(handleEdit)}
            >
              {d("ui.common.update")}
            </DropdownItem>
          )}
          {handleDuplicate && (
            <DropdownItem
              leftElement={<Icon {...commonIcons.duplicate} />}
              onClick={callWithClose(handleDuplicate)}
            >
              {d("ui.common.duplicate")}
            </DropdownItem>
          )}
          {flowStep && flowStep.variant !== FlowStepVariant.TRIGGER && (
            <CopyToClipboard text={serializeStep(flowStep)}>
              <DropdownItem
                leftElement={<Icon {...commonIcons.copy} />}
                onClick={callWithClose(() => {})}
              >
                {d("ui.common.copy")}
              </DropdownItem>
            </CopyToClipboard>
          )}
        </DropdownItemGroup>
      )}

      {handleDelete && (
        <DropdownItemGroup
          hasDivider={!!handleEdit || !!handleDuplicate || !!handleRename}
        >
          <DropdownItem
            leftElement={
              <Icon {...commonIcons.delete} color="traffic-danger" />
            }
            onClick={handleDelete}
          >
            <Text color="traffic-onDanger"> {d("ui.common.delete")}</Text>
          </DropdownItem>
        </DropdownItemGroup>
      )}
    </KebabMenu>
  );
};
