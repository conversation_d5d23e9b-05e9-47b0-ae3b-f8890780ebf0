import React, { ReactNode, useCallback, useState } from "react";

import { ReactFlowInstance } from "@xyflow/react";

import {
  FlowConfiguration,
  MockFlowContext
} from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  FlowStepTypeConfiguration,
  FlowStepTypeConfigurationPrimaryIdentifier
} from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";
import { VariableTypeDefinition } from "@src/types/FlowConfiguration/Variables";
import { WorkspaceVariableConfiguration } from "@src/types/WorkspaceVariable";

import {
  ConfigurationFlowContext,
  SelectingStepType
} from "./ConfigurationFlowContext";

export const ConfigurationFlowProvider = ({
  children
}: {
  children: ReactNode;
}) => {
  const [selectedStepId, setSelectedStepId] = useState<string | undefined>(
    undefined
  );
  const [configurationFlow, setConfigurationFlow] = useState<
    FlowConfiguration | undefined
  >(undefined);

  const [configurationWorkspaceVariables, setConfigurationWorkspaceVariables] =
    useState<
      | {
          [variableId: string]: WorkspaceVariableConfiguration;
        }
      | undefined
    >(undefined);

  const [
    flowStepTypeConfigByPrimaryIdentifier,
    setFlowStepTypeConfigByPrimaryIdentifier
  ] = useState<
    | {
        [
          key: FlowStepTypeConfigurationPrimaryIdentifier
        ]: FlowStepTypeConfiguration;
      }
    | undefined
  >(undefined);

  const [selectingStepType, setSelectingStepType] = useState<
    SelectingStepType | undefined
  >(undefined);

  const [path, setPath] = useState<string[] | undefined>(undefined);

  const [mockFlowContext, setMockFlowContext] = useState<
    MockFlowContext | undefined
  >(undefined);

  const [variableDefinitions, setVariableDefinitions] = useState<
    | {
        [path: string]: VariableTypeDefinition;
      }
    | undefined
  >(undefined);

  const [variablesByName, setVariablesByName] = useState<
    | {
        [path: string]: VariableTypeDefinition;
      }
    | undefined
  >(undefined);

  const [variablesByPath, setVariablesByPath] = useState<
    | {
        [path: string]: VariableTypeDefinition;
      }
    | undefined
  >(undefined);

  const [settings, setSettings] = useState({
    debugMode: false,
    showMiniMap: true,
    isRenaming: false
  });

  const setSelectedStepIdWithUnfocus = useCallback((stepId?: string) => {
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }
    setSelectedStepId(stepId);
  }, []);

  const [zoomLevel, setZoomLevel] = useState<number | undefined>(undefined);

  const [mainViewportRef, setMainViewportRef] =
    useState<ReactFlowInstance | null>(null);

  const [refreshFlowCanvas, setRefreshFlowCanvas] = useState(() => () => {});

  const contextValue = React.useMemo(
    () => ({
      selectedStepId,
      configurationFlow,
      setSelectedStepId,
      setConfigurationFlow,
      flowStepTypeConfigByPrimaryIdentifier,
      setFlowStepTypeConfigByPrimaryIdentifier,
      selectingStepType,
      setSelectingStepType,
      path,
      setPath,
      mockFlowContext,
      setMockFlowContext,
      variableDefinitions,
      setVariableDefinitions,
      variablesByName,
      setVariablesByName,
      variablesByPath,
      setVariablesByPath,
      setSelectedStepIdWithUnfocus,
      zoomLevel,
      setZoomLevel,
      mainViewportRef,
      setMainViewportRef,
      settings,
      setSettings,
      refreshFlowCanvas,
      setRefreshFlowCanvas,
      configurationWorkspaceVariables,
      setConfigurationWorkspaceVariables
    }),
    [
      selectedStepId,
      configurationFlow,
      flowStepTypeConfigByPrimaryIdentifier,
      selectingStepType,
      path,
      mockFlowContext,
      variableDefinitions,
      variablesByName,
      variablesByPath,
      setSelectedStepIdWithUnfocus,
      zoomLevel,
      mainViewportRef,
      settings,
      refreshFlowCanvas,
      setRefreshFlowCanvas
    ]
  );

  return (
    <ConfigurationFlowContext.Provider value={contextValue}>
      {children}
    </ConfigurationFlowContext.Provider>
  );
};
