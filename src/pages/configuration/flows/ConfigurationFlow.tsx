import React, { useEffect, useMemo, useRef } from "react";

import { Doc } from "@automerge/automerge-repo";
import { Form, Stack, useUrlHash } from "@oneteam/onetheme";
import { ReactFlowProvider } from "@xyflow/react";
import { UseFormReturn } from "react-hook-form";
import {
  generatePath,
  useNavigate,
  useOutletContext,
  useParams
} from "react-router-dom";
import { z } from "zod";

import { routeConstants } from "@src/constants/routeConstants.ts";
import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs";
import { useDictionary } from "@src/hooks/useDictionary";
import {
  buildConfigurationEntityPageTitle,
  usePageTitle
} from "@src/hooks/usePageTitle.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";
import { Workspace } from "@src/types/workspace.ts";

import { useConfigurationFlowContext } from "./ConfigurationFlowContext.tsx";
import { ConfigurationFlowProvider } from "./ConfigurationFlowProvider.tsx";
import { Flow } from "./Flow.tsx";
import { FlowCanvasProvider } from "./FlowCanvas/FlowCanvasProvider.tsx";

enum ConfigurationFlowMode {
  VIEW = "view",
  EDIT = "edit",
  CONDITIONAL_LOGIC = "conditional-logic"
}

export const ConfigurationFlow = () => {
  const navigate = useNavigate();
  const { document, workspace } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    workspace: Workspace;
  }>();
  const d = useDictionary();
  const { configurationFlowId, ...params } = useParams();
  // const { setConfigurationWorkspaceVariables } = useConfigurationFlowContext();

  const { urlHashDetail, updateUrlHash } = useUrlHash();

  const configurationFlow = useMemo(() => {
    if (!configurationFlowId) {
      return;
    }
    return document.flows.entities[configurationFlowId];
  }, [configurationFlowId, document.flows.entities]);

  const configurationFlowLabels = useMemo(
    () => configurationFlow?.labels?.map(labelId => document.labels[labelId]),
    [configurationFlow, document.labels]
  );

  // useEffect(() => {
  //   setConfigurationWorkspaceVariables(document.variables);
  // }, [document.variables, setConfigurationWorkspaceVariables]);

  const configurationWorkspaceVariables = useMemo(() => {
    return document.variables;
  }, [document.variables]);

  const breadcrumbs = useBreadcrumbs({
    flowConfiguration: {
      text: configurationFlow?.name ?? d("ui.common.name")
    }
  });

  usePageTitle(
    buildConfigurationEntityPageTitle({
      section: d("ui.configuration.flows.title"),
      entityName: configurationFlow?.name ?? "",
      context: workspace?.key ?? ""
    }),
    !!workspace?.key && !!configurationFlow?.name
  );

  useEffect(() => {
    const urlHashMode = urlHashDetail?.get("mode") as ConfigurationFlowMode;

    if (!urlHashMode) {
      updateUrlHash("mode", ConfigurationFlowMode.EDIT);
    }
  }, [updateUrlHash, urlHashDetail]);

  useEffect(() => {
    if (
      document.flows.entities &&
      configurationFlowId &&
      document.flows.entities[configurationFlowId] === undefined
    ) {
      navigate(generatePath(routeConstants.configurationFlowList, params));
    }
  }, [document.flows.entities, configurationFlowId, navigate, params]);

  const formRef = useRef<UseFormReturn<{ name: string }> | null>(null);
  const setForm = (form: UseFormReturn<{ name: string }>) => {
    formRef.current = form;
  };

  if (configurationFlow === undefined || !configurationFlow) {
    return <></>;
  }

  return (
    <Form
      schema={z.object({ name: z.string() })}
      handleSubmit={() => {}}
      d={d}
      hideFormButtons
      defaultValues={configurationFlow}
      setForm={setForm}
    >
      <Stack height="100" width="100">
        <ConfigurationFlowProvider>
          <FlowCanvasProvider>
            <ReactFlowProvider>
              {configurationFlow && (
                <Flow
                  breadcrumbs={breadcrumbs}
                  configurationFlow={configurationFlow}
                  configurationFlowLabels={configurationFlowLabels}
                  configurationWorkspaceVariables={
                    configurationWorkspaceVariables
                  }
                />
              )}
            </ReactFlowProvider>
          </FlowCanvasProvider>
        </ConfigurationFlowProvider>
      </Stack>
    </Form>
  );
};
