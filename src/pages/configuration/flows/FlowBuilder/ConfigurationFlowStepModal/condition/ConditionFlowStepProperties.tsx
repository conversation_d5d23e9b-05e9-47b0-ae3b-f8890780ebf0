import React, { useCallback, useState } from "react";

import {
  Accordion,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  ColorText,
  CustomAccordionTrigger,
  Divider,
  Heading,
  HeadingSize,
  IconButton,
  Inline,
  Label,
  OpenCloseIcon,
  Renamable,
  Stack,
  Text,
  TextField
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { getByPath } from "@helpers/configurationFormHelper.ts";

import { useConfigurationFlowError } from "@pages/configuration/flows/hooks/useConfigurationFlowError";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { commonIcons } from "@src/constants/iconConstants";
import { useDictionary } from "@src/hooks/useDictionary";
import {
  CompoundCondition,
  Condition
} from "@src/types/FlowConfiguration/Condition";
import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  ConditionStepBranch,
  ConditionStepProperties
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

import { ConditionContent } from "./ConditionContent";
import "./ConditionFlowStepProperties.scss";

export const ConditionFlowStepProperties = ({
  step,
  docAccessor,
  onBranchAdd,
  onBranchRemove
}: {
  step: FlowStep<ConditionStepProperties>;
  docAccessor: string[];
  onBranchAdd?: () => void;
  onBranchRemove?: (index: number) => void;
}) => {
  const d = useDictionary();
  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();
  const { branches = [] } = step.properties;
  const [expandedBranch, setExpandedBranch] = useState<{
    [key: string]: boolean;
  }>(
    branches.reduce(
      (acc, _, index) => ({
        ...acc,
        [index]: true
      }),
      {}
    )
  );

  const isCompoundConditionType = useCallback(
    (condition: Condition): condition is CompoundCondition => {
      if (!condition || typeof condition !== "object") {
        return false;
      }
      return "AND" in condition || "OR" in condition;
    },
    []
  );

  const handleBranchNameOnChange = useCallback(
    (index: number) => {
      return (value: string) => {
        docChange(d => {
          const properties = getByPath<ConditionStepProperties>(d, docAccessor);
          properties.branches[index].name = value;
        });
      };
    },
    [docAccessor, docChange]
  );

  const { getServerError } = useConfigurationFlowError();
  const branchNameLabel = `${d("ui.configuration.flows.step.variants.condition.propertiesModal.branch")} ${d("ui.common.name")}`;
  const branchNameError = useCallback(
    (index: number) => {
      return getServerError(
        [...docAccessor, "branches", index.toString(), "name"],
        branchNameLabel
      )?.message;
    },
    [docAccessor, getServerError, branchNameLabel]
  );

  const renderTrigger = useCallback(
    (branch: ConditionStepBranch, index: number): CustomAccordionTrigger =>
      ({ isOpen, onClick }) => (
        <Inline
          width="100"
          onClick={onClick}
          style={{ cursor: "pointer" }}
          alignment="left"
          gap="050"
          spaceBetween
        >
          <Inline width="100" gap="050" alignment="left">
            <Inline alignment="left" gap="000">
              <OpenCloseIcon isOpen={isOpen} />
              <Heading size={HeadingSize.XXS} color={ColorText.SECONDARY}>
                {index + 1}
              </Heading>
            </Inline>
            <Text size="m" color="text-secondary">
              <Renamable
                value={branch.name}
                onChange={handleBranchNameOnChange(index)}
                className="branch-name"
                error={branchNameError(index)}
              />
            </Text>
          </Inline>
          {onBranchRemove && (
            <IconButton
              {...commonIcons.delete}
              onClick={e => {
                e.stopPropagation();
                onBranchRemove?.(index);
                setExpandedBranch(prev => {
                  // eslint-disable-next-line @typescript-eslint/no-unused-vars
                  const { [index]: _, ...rest } = prev;
                  return rest;
                });
              }}
            />
          )}
        </Inline>
      ),
    [branchNameError, handleBranchNameOnChange, onBranchRemove]
  );

  const handleAccordionToggle = (index: number) => {
    setExpandedBranch(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const renderContent = useCallback(() => {
    return branches.map((branch, index) => {
      const key = `${step.id}-${branch.name}-${index}`;
      return (
        <React.Fragment key={key}>
          {index === 0 && <Divider className="branch-divider--top" />}
          <Stack gap="100">
            <Accordion
              contentOverflow="visible"
              trigger={renderTrigger(branch, index)}
              isOpen={expandedBranch[index]}
              onOpenChange={() => {
                handleAccordionToggle(index);
              }}
            >
              <Stack
                gap="100"
                style={{
                  paddingLeft: "var(--spacing-050)",
                  paddingTop: "var(--spacing-100)"
                }}
              >
                <TextField
                  label={branchNameLabel}
                  name={`branches.${index}.name`}
                  value={branch.name}
                  onChange={handleBranchNameOnChange(index)}
                  className="branch-name"
                  onlyTriggerChangeWhenBlur={true}
                  error={branchNameError(index)}
                  required
                />

                <Stack gap="025">
                  <Label
                    label={d(
                      "ui.configuration.flows.step.variants.condition.label"
                    )}
                  />
                  <Card
                    style={{
                      padding: "var(--spacing-100)"
                    }}
                  >
                    <ConditionContent
                      condition={branch.condition ?? ({} as Condition)}
                      docAccessor={[
                        ...docAccessor,
                        "branches",
                        index.toString(),
                        "condition"
                      ]}
                      d={d}
                      isSimpleConditionInRoot={
                        !isCompoundConditionType(branch.condition)
                      }
                      isCompoundConditionType={isCompoundConditionType}
                      isRoot={true}
                    />
                  </Card>
                </Stack>
              </Stack>
            </Accordion>
          </Stack>
          {index < branches.length - 1 && (
            <Divider className="branch-divider" />
          )}
        </React.Fragment>
      );
    });
  }, [
    branches,
    renderTrigger,
    expandedBranch,
    branchNameLabel,
    handleBranchNameOnChange,
    branchNameError,
    d,
    docAccessor,
    isCompoundConditionType,
    step.id
  ]);

  return (
    <Stack gap="000" height="100" overflow="auto" style={{ maxHeight: "80vh" }}>
      {branches?.length > 0 && (
        <Stack
          key="fit-branch"
          height="fit"
          gap="100"
          overflow="auto"
          style={{ maxHeight: "100%", paddingBottom: "var(--spacing-100)" }}
        >
          {renderContent()}
        </Stack>
      )}

      <Stack gap="100">
        <Divider />
        <Inline spaceBetween alignment="left">
          <Button
            label={`${d("ui.common.add")} ${d("ui.configuration.flows.step.variants.condition.propertiesModal.branch")}`}
            variant="secondary"
            onClick={() => {
              onBranchAdd?.();
              setExpandedBranch({
                ...expandedBranch,
                [branches.length]: true
              });
            }}
            leftIcon={{ name: "add" }}
          />
          {branches.length > 0 && (
            <Box alignment="right">
              {!Object.values(expandedBranch)?.find(item => item === true) ? (
                <Button
                  variant="text"
                  label={d(
                    "ui.configuration.flows.step.variants.common.propertiesModal.expandAll"
                  )}
                  onClick={() =>
                    setExpandedBranch(
                      branches.reduce(
                        (acc, _, index) => ({ ...acc, [index]: true }),
                        {}
                      )
                    )
                  }
                />
              ) : (
                <Button
                  variant="text"
                  label={d(
                    "ui.configuration.flows.step.variants.common.propertiesModal.collapseAll"
                  )}
                  onClick={() => setExpandedBranch({})}
                />
              )}
            </Box>
          )}
        </Inline>
      </Stack>
    </Stack>
  );
};
