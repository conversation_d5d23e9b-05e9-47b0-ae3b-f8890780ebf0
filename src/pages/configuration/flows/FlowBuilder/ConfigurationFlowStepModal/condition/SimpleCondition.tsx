import React, { useMemo } from "react";

import { Inline, Select, SelectOptionType } from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { getByPath } from "@helpers/configurationFormHelper.ts";

import { VariableField } from "@components/flows/VariableField";

import { useConfigurationFlowError } from "@pages/configuration/flows/hooks/useConfigurationFlowError";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import {
  ConditionItem,
  Operators,
  SINGLE_ARGUMENT_OPERATORS
} from "@src/types/FlowConfiguration/Condition";

import "./ConditionFlowStepProperties.scss";

const createOperatorOptions = (d: Dictionary): SelectOptionType[] => {
  return Object.values(Operators).map(value => ({
    label: d(
      `ui.configuration.flows.step.variants.condition.operator.${value}.label`
    ),
    description: d(
      `ui.configuration.flows.step.variants.condition.operator.${value}.description`
    ),
    value
  }));
};

export const SimpleCondition = ({
  condition,
  docAccessor
}: {
  condition: ConditionItem;
  docAccessor: string[];
}) => {
  const d = useDictionary();
  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();
  const operatorOptions = useMemo(() => createOperatorOptions(d), [d]);

  const filteredDocAccessor = docAccessor.filter(item => item !== "");

  const { getServerError } = useConfigurationFlowError();

  return (
    <Inline
      alignment="left"
      contentsHeight="fill"
      contentsWidth="fill"
      gap="100"
    >
      <VariableField
        value={String(condition.lhs ?? "")}
        onChange={text => {
          docChange(d => {
            const conditionItem = getByPath<ConditionItem>(
              d,
              filteredDocAccessor
            );
            if (conditionItem) {
              conditionItem.lhs = text;
            }
          });
        }}
        error={
          getServerError([...filteredDocAccessor, "lhs"], d("ui.common.value"))
            ?.message
        }
      />
      <Select
        name="type"
        options={operatorOptions}
        value={condition.operator}
        onChange={value => {
          docChange(d => {
            const conditionItem = getByPath<ConditionItem>(
              d,
              filteredDocAccessor
            );
            const newOperator = Object.values(Operators).find(
              op => op === value
            );
            if (newOperator && conditionItem) {
              conditionItem.operator = newOperator;
              if (SINGLE_ARGUMENT_OPERATORS.includes(newOperator)) {
                delete conditionItem.rhs;
              } else {
                conditionItem.rhs = "";
              }
            }
          });
        }}
        onlyTriggerChangeWhenBlur={true}
        error={getServerError([...filteredDocAccessor, "operator"])?.message}
      />
      {condition?.rhs !== undefined && (
        <VariableField
          value={String(condition.rhs ?? "")}
          onChange={text => {
            docChange(d => {
              const conditionItem = getByPath<ConditionItem>(
                d,
                filteredDocAccessor
              );
              if (conditionItem) {
                conditionItem.rhs = text;
              }
            });
          }}
          error={
            getServerError(
              [...filteredDocAccessor, "rhs"],
              d("ui.common.value")
            )?.message
          }
          // TODO: get LHS variable type and use it to build RHS schema
          // schema={{
          //   type: "number"
          // }}
        />
      )}
    </Inline>
  );
};
