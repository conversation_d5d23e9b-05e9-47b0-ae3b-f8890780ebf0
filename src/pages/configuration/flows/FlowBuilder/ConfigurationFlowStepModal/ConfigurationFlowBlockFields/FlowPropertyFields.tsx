import React, { use<PERSON><PERSON>back, useMemo } from "react";

import { Doc } from "@automerge/automerge-repo";
import { Box, Button, Stack, Text } from "@oneteam/onetheme";
import { generatePath, useOutletContext } from "react-router-dom";

import { DocumentErrorHelper } from "@helpers/DocumentErrorHelper";
import { getByPath } from "@helpers/configurationFormHelper";
import { InternalOtaiFormField } from "@helpers/otaiFormHelper";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { ConfigurationFlowVariable } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowVariable/ConfigurationFlowVariable";
import { DocChange } from "@pages/workspace/WorkspaceLayout";

import { routeConstants } from "@src/constants/routeConstants";
import { useDictionary } from "@src/hooks/useDictionary";
import {
  FlowConfigurationStatusType,
  FlowPath,
  MockFlowContext
} from "@src/types/FlowConfiguration/FlowConfiguration";
import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  FlowVariantStepProperties,
  TriggerStepProperties,
  triggerStepTypesCallableFromAnotherFlow
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace";

import "./ConfigurationFlowStepFields.scss";
import { CustomPropertyField } from "./CustomPropertyField";
import { FieldDivider } from "./FieldDivider";

export const FlowPropertyFields = ({
  selectedStep,
  handleSaveInput,
  disabled,
  mockFlowContext,
  settings,
  pathInDocument
}: {
  selectedStep: FlowStep<FlowVariantStepProperties>;
  handleSaveInput: (value: string, identifier: string) => void;
  disabled?: boolean;
  mockFlowContext?: MockFlowContext;
  settings?: { debugMode: boolean };
  pathInDocument: FlowPath;
}) => {
  const { configurationFlow } = useConfigurationFlowContext();
  const d = useDictionary();
  const { document, workspace } = useOutletContext<{
    workspace: Workspace;
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
    docErrorHelper: DocumentErrorHelper;
  }>();

  const configurationFlows = useMemo(() => {
    if (!document.flows?.entities) {
      return {};
    }
    return document.flows.entities;
  }, [document.flows.entities]);

  const configurationFlowOptions = useMemo(() => {
    return Object.values(configurationFlows ?? {})
      .filter(configurationFlowOption => {
        if (
          configurationFlowOption?.id === configurationFlow?.id ||
          configurationFlowOption?.status ===
            FlowConfigurationStatusType.INACTIVE
        ) {
          return false;
        }
        const triggerTypes = Object.values(
          configurationFlowOption?.triggers ?? {}
        )?.map(
          trigger =>
            (trigger?.properties as TriggerStepProperties).typePrimaryIdentifier
        );
        return triggerTypes?.some(triggerType =>
          triggerStepTypesCallableFromAnotherFlow.includes(triggerType)
        );
      })
      .map(option => ({
        label: option.name,
        value: option.id
      }));
  }, [configurationFlow?.id, configurationFlows]);

  const selectedConfigurationFlow = useMemo(
    () =>
      selectedStep.properties?.inputs?.flowConfigurationId
        ? configurationFlows[
            selectedStep.properties?.inputs?.flowConfigurationId
          ]
        : undefined,
    [selectedStep.properties?.inputs?.flowConfigurationId, configurationFlows]
  );

  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();
  const flowConfigQnIdentifier = "flowConfigurationId";

  const handleSaveFlowConfigurationId = useCallback(
    (value: string, identifier: string) => {
      handleSaveInput(value, identifier);

      if (identifier != flowConfigQnIdentifier) {
        console.error("Invalid identifier for flow configuration ID");
        return;
      }

      // handle change name of step to the selected flow name
      docChange(doc => {
        const selectedFlowName = configurationFlows[value]?.name;
        const currentFlowStep = getByPath<FlowStep>(
          doc,
          pathInDocument.slice(0, -2)
        );
        currentFlowStep.name = selectedFlowName ?? d("ui.terminology.flow");
      });
    },
    [configurationFlows, d, docChange, handleSaveInput, pathInDocument]
  );

  return (
    <Stack gap="150" width="100" contentsWidth="100" height="100">
      <Stack gap="025">
        <CustomPropertyField
          key={flowConfigQnIdentifier}
          question={
            {
              identifier: flowConfigQnIdentifier,
              type: "select",
              text: d(
                "ui.configuration.flows.step.variants.flow.propertiesModal.flowConfiguration.label"
              ),
              description: d(
                "ui.configuration.flows.step.variants.flow.propertiesModal.flowConfiguration.description"
              ),
              tooltip: d(
                "ui.configuration.flows.step.variants.flow.propertiesModal.flowConfiguration.tooltip"
              ),
              properties: {
                required: true,
                options: configurationFlowOptions,
                clearIfOptionMissing: false
              }
            } as InternalOtaiFormField
          }
          selectedStep={selectedStep}
          handleSaveInput={handleSaveFlowConfigurationId}
          disabled={disabled}
          mockFlowContext={mockFlowContext}
          debugMode={settings?.debugMode}
          pathInDocument={pathInDocument}
        />
        {selectedStep.properties?.inputs?.flowConfigurationId && (
          <Box width="fit">
            <Button
              size="small"
              variant="text"
              label={d(
                "ui.configuration.flows.step.variants.flow.propertiesModal.flowConfiguration.openButton"
              )}
              leftIcon={{ name: "open_in_new" }}
              onClick={() => {
                window.open(
                  `/ai${generatePath(routeConstants.configurationFlow, {
                    workspaceKey: workspace.key,
                    configurationFlowId:
                      selectedStep.properties?.inputs?.flowConfigurationId
                  })}`,
                  "_blank"
                );
              }}
            />
          </Box>
        )}
      </Stack>
      {selectedStep.properties?.inputs?.flowConfigurationId && (
        <>
          <FieldDivider
            label={d(
              "ui.configuration.flows.step.variants.common.propertiesModal.inputs.label"
            )}
          />
          {selectedConfigurationFlow?.startingVariables?.map(variable => {
            return (
              <CustomPropertyField
                key={`${flowConfigQnIdentifier}_${variable.identifier}`}
                question={
                  {
                    identifier: variable.identifier,
                    type: "variable",
                    text: variable.text ?? variable.identifier,
                    properties: {
                      type: variable.type,
                      properties: variable.properties ?? {}
                    }
                  } as InternalOtaiFormField
                }
                selectedStep={selectedStep}
                handleSaveInput={handleSaveInput}
                disabled={disabled}
                mockFlowContext={mockFlowContext}
                debugMode={settings?.debugMode}
                pathInDocument={pathInDocument}
              />
            );
          })}
          <FieldDivider
            label={d("ui.configuration.flows.variables.output.label")}
          />
          <Stack gap="050">
            {(selectedConfigurationFlow?.endingVariables ?? [])?.length > 0 ? (
              Object.values(
                selectedConfigurationFlow?.endingVariables ?? {}
              )?.map(variable => (
                <ConfigurationFlowVariable
                  key={variable.identifier}
                  variable={variable}
                  variant="line-item"
                />
              ))
            ) : (
              <Text size="xs" weight="regular" color="text-tertiary">
                {d("ui.configuration.flows.variables.output.empty")}
              </Text>
            )}
          </Stack>
        </>
      )}
    </Stack>
  );
};
