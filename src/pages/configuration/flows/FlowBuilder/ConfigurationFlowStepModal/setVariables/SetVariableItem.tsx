import React, { useC<PERSON>back, useMemo, useState } from "react";

import {
  Accordion,
  Box,
  Button,
  ColorText,
  CustomAccordionTrigger,
  Divider,
  DragAndDrop,
  DraggableItem,
  DropdownItem,
  DropdownItemGroup,
  FloatingWithParentPosition,
  Heading,
  HeadingSize,
  Icon,
  IconButton,
  Inline,
  KebabMenu,
  Label,
  OnDrop,
  OpenCloseIcon,
  Pill,
  Select,
  Stack,
  Text,
  TextField
} from "@oneteam/onetheme";

import { VariableField } from "@components/flows/VariableField";
import { QuestionTypeSelect } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionTypeSelect/QuestionTypeSelect";
import { ReorderIcon } from "@components/shared/ReorderIcon";

import { ConfigurationFlowVariable } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowVariable/ConfigurationFlowVariable";
import { useConfigurationFlowError } from "@pages/configuration/flows/hooks/useConfigurationFlowError";

import { commonErrors } from "@src/constants/errorMessages";
import { commonIcons } from "@src/constants/iconConstants";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import {
  FlowPath,
  MockVariable
} from "@src/types/FlowConfiguration/FlowConfiguration";
import { FlowStepId } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  Variable,
  VariableConfiguration,
  VariableFieldKeys,
  VariableValue,
  setVariablesTypeOptions
} from "@src/types/FlowConfiguration/Variables";
import { QuestionTypes } from "@src/types/Question";

import "./SetVariablesFlowStepProperties.scss";

const identifierRegex = RegExp("^[a-zA-Z0-9_]*$");

export const SetVariableItem = ({
  index,
  variable,
  columns,
  addOption,
  removeOption,
  updateOption,
  reorderOptions,
  expandedVariable,
  setExpandedVariable,
  handleFieldChange,
  isFirstItem,
  hasDivider,
  // dropzoneTag,
  autoFocusLast,
  handleRemove,
  handleDuplicate,
  sourceStepId,
  handleGoToSource,
  occurrenceWithinStep = 1,
  stepOutputVariables,
  path
}: {
  index: number;
  variable: MockVariable & {
    value?: VariableValue;
  };
  columns: (VariableConfiguration & { key: number })[];
  addOption: () => void;
  reorderOptions: OnDrop;
  removeOption: (index: number) => void;
  updateOption: (
    index: number,
    property: "identifier" | "type",
    value: string
  ) => void;
  expandedVariable: { [key: string]: boolean };
  setExpandedVariable: React.Dispatch<
    React.SetStateAction<{ [key: string]: boolean }>
  >;
  handleFieldChange: (
    index: number,
    field: VariableFieldKeys,
    value?: string
  ) => void;
  isFirstItem?: boolean;
  hasDivider: boolean;
  dropzoneTag: string;
  autoFocusLast?: boolean;
  handleRemove?: () => void;
  handleDuplicate?: () => void;
  sourceStepId?: FlowStepId;
  handleGoToSource?: () => void;
  occurrenceWithinStep?: number;
  stepOutputVariables?: string[];
  path: FlowPath;
}) => {
  const [isKebabOpen, setIsKebabOpen] = useState(false);

  const d = useDictionary();
  const IDENTIFIER_LABEL_KEY =
    "ui.configuration.flows.variables.identifier.label";

  const { getServerError } = useConfigurationFlowError();

  const getErrors = useCallback(
    (identifier: string) => {
      const serverError = getServerError(
        [...path, index.toString(), "identifier"],
        d(IDENTIFIER_LABEL_KEY)
      );
      if (serverError) {
        return d(serverError?.message);
      }

      if (!identifier?.length) {
        return d(commonErrors.required, {
          name: d(IDENTIFIER_LABEL_KEY)
        });
      }

      if (!/^[a-zA-Z0-9_]+$/.test(identifier || "")) {
        return d(commonErrors.alphanumeric, {
          name: d(IDENTIFIER_LABEL_KEY),
          constraint: d("errors.common.constraint")
        });
      }

      if (stepOutputVariables?.includes(variable.identifier)) {
        return d("ui.configuration.flows.variables.output.cannotSet");
      }

      if (["global", "event", "variables", "thisStep"].includes(identifier)) {
        return d("ui.configuration.flows.variables.reservedName.error");
      }
      return undefined;
    },
    [d, getServerError, index, path, stepOutputVariables, variable.identifier]
  );

  const kebabMenu = useMemo(() => {
    if (!handleRemove && !handleDuplicate) {
      return <></>;
    }
    return (
      <KebabMenu
        className="set-variable-kebab-menu"
        isOpen={isKebabOpen}
        onOpenChange={isOpen => setIsKebabOpen(isOpen)}
        position={FloatingWithParentPosition.BOTTOM_RIGHT}
      >
        <DropdownItemGroup>
          {handleDuplicate && (
            <DropdownItem
              leftElement={<Icon {...commonIcons.duplicate} />}
              onClick={() => {
                handleDuplicate();
                setIsKebabOpen(false);
              }}
            >
              {d("ui.common.duplicate")}
            </DropdownItem>
          )}
          {handleRemove && (
            <DropdownItem
              leftElement={
                <Icon {...commonIcons.delete} color="traffic-danger" />
              }
              onClick={() => {
                handleRemove();
                setIsKebabOpen(false);
              }}
            >
              <Text color="traffic-onDanger">{d("ui.common.delete")}</Text>
            </DropdownItem>
          )}
        </DropdownItemGroup>
      </KebabMenu>
    );
  }, [d, handleDuplicate, handleRemove, isKebabOpen]);

  const renderTrigger = useCallback(
    (variable: Variable, index: number): CustomAccordionTrigger =>
      ({ isOpen, onClick }) => (
        <Inline width="100" alignment="left" spaceBetween>
          <Inline
            width="100"
            onClick={onClick}
            style={{ cursor: "pointer" }}
            overflow="hidden"
            alignment="left"
            gap="050"
          >
            <Inline alignment="left" gap="000">
              <OpenCloseIcon isOpen={isOpen} />
              <Heading size={HeadingSize.XXS} color={ColorText.SECONDARY}>
                {index + 1}
              </Heading>
            </Inline>
            <ConfigurationFlowVariable
              variable={variable}
              handleRename={value =>
                handleFieldChange(index, "identifier", value)
              }
            />
            {sourceStepId || occurrenceWithinStep > 1 ? (
              <Pill
                label={d("ui.configuration.flows.variables.reused")}
                variant="neutral"
                rightElement={
                  handleGoToSource && sourceStepId ? (
                    <IconButton
                      label="Open source step"
                      size="s"
                      name="my_location"
                      color="text-secondary"
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleGoToSource();
                      }}
                    />
                  ) : undefined
                }
              />
            ) : (
              <Pill label={`NEW`} variant="neutral" />
            )}
          </Inline>
          {kebabMenu}
        </Inline>
      ),
    [
      sourceStepId,
      occurrenceWithinStep,
      d,
      handleGoToSource,
      kebabMenu,
      handleFieldChange
    ]
  );

  const renderVariableValueInputField = useCallback(
    (
      variable: MockVariable & {
        value?: VariableValue;
      },
      index: number
    ) => {
      if (
        (variable.properties?.listOperation as ListOperationOptions) ===
          ListOperationOptions.REMOVE_ITEM ||
        (variable.properties?.fileOperation as FileOperationOptions) ===
          FileOperationOptions.REMOVE_FILE
      ) {
        return <></>;
      }

      return (
        <Inline gap="100" contentsWidth="100">
          <VariableField
            label={d("ui.configuration.flows.variables.value.label")}
            description={d(
              "ui.configuration.flows.variables.value.description"
            )}
            value={(variable?.value ?? "") as string}
            onChange={value => {
              handleFieldChange(index, "value", value);
            }}
            width="fit"
            isExpression
          />
        </Inline>
      );
    },
    [d, handleFieldChange]
  );

  if (!(Object.values(QuestionTypes) as string[]).includes(variable.type)) {
    handleFieldChange(index, "type", QuestionTypes.TEXT);
    return <></>;
  }

  return (
    // <DraggableItem
    //   canDropOver={data => {
    //     return data.source.context.dropzoneTags.includes(dropzoneTag);
    //   }}
    // >
    <>
      {isFirstItem && <Divider className="set-variable-divider" />}
      <Stack
        className="set-variable-item"
        gap="100"
        style={{ marginTop: "var(--spacing-100)" }}
      >
        <Accordion
          contentOverflow="visible"
          trigger={renderTrigger(variable, index)}
          isOpen={expandedVariable[index]}
          onOpenChange={() => {
            setExpandedVariable(prev => ({
              ...prev,
              [index]: !prev[index]
            }));
          }}
        >
          <Stack
            gap="100"
            style={{
              paddingLeft: "var(--spacing-050)",
              paddingTop: "var(--spacing-100, 2px)"
            }}
            width="100"
          >
            <Inline gap="100" width="100" alignment="left">
              <Box width="100" contentsWidth="100">
                <TextField
                  label={d(IDENTIFIER_LABEL_KEY)}
                  description={d(
                    "ui.configuration.flows.variables.identifier.description"
                  )}
                  name={`variables.${index}.identifier`}
                  value={variable.identifier}
                  onChange={value => {
                    // regex - can only contain letters, numbers, and underscores
                    handleFieldChange(index, "identifier", value ?? "");
                  }}
                  regex={identifierRegex}
                  onlyTriggerChangeWhenBlur={true}
                  width="fit"
                  error={getErrors(variable.identifier)}
                  autoFocus={autoFocusLast}
                  required
                />
              </Box>
              <Stack width="fit" alignment="left" gap="050">
                <QuestionTypeSelect
                  type={variable.type as `${QuestionTypes}`}
                  path={[]}
                  handleChange={value => {
                    handleFieldChange(index, "type", value);
                  }}
                  disabled={!!sourceStepId}
                  options={setVariablesTypeOptions}
                />
              </Stack>
            </Inline>

            {variable.type === QuestionTypes.TABLE && (
              <>
                <TableColumnsProperties
                  d={d}
                  columns={columns}
                  addOption={addOption}
                  removeOption={removeOption}
                  updateOption={updateOption}
                  reorderOptions={reorderOptions}
                  disabled={!!sourceStepId}
                />
                <TableSetVariableProperties
                  variable={variable}
                  index={index}
                  handleFieldChange={handleFieldChange}
                  d={d}
                />
              </>
            )}

            {variable.type === QuestionTypes.FILES && (
              // operation - set, add, remove(idx)
              // variable or url+filename
              <FileSetVariableProperties
                variable={variable}
                index={index}
                handleFieldChange={handleFieldChange}
                d={d}
              />
            )}

            {variable.type === QuestionTypes.LIST && (
              <ListSetVariableProperties
                variable={variable}
                index={index}
                handleFieldChange={handleFieldChange}
                d={d}
              />
            )}

            {/* This is the actual varaible input field */}
            {renderVariableValueInputField(variable, index)}
          </Stack>
        </Accordion>
        {hasDivider && <Divider className="set-variable-divider" />}
      </Stack>
    </>
    // </DraggableItem>
  );
};

enum TableOperationOptions {
  SET_TABLE = "setTable",
  SET_CELL = "setCell",
  SET_ROW = "setRow",
  SET_COLUMN = "setColumn"
}

function getTableOperationOptions(d: Dictionary) {
  return Object.values(TableOperationOptions).map(option => ({
    value: option,
    label: d(
      `ui.configuration.flows.variables.type.table.operation.options.${option}.label`
    ),
    description: d(
      `ui.configuration.flows.variables.type.table.operation.options.${option}.description`
    )
  }));
}

enum FileOperationOptions {
  SET_FILE = "setFiles",
  ADD_FILE = "addFiles",
  REMOVE_FILE = "removeFiles"
}

function getFileOperationOptions(d: Dictionary) {
  return Object.values(FileOperationOptions).map(option => ({
    value: option,
    label: d(
      `ui.configuration.flows.variables.type.files.operation.options.${option}.label`
    ),
    description: d(
      `ui.configuration.flows.variables.type.files.operation.options.${option}.description`
    )
  }));
}

const TableColumnsProperties = ({
  d,
  columns,
  addOption,
  removeOption,
  updateOption,
  reorderOptions,
  disabled = false
}: {
  d: Dictionary;
  columns: (VariableConfiguration & { key: number })[];
  addOption: () => void;
  reorderOptions: OnDrop;
  removeOption: (index: number) => void;
  updateOption: (
    index: number,
    property: "identifier" | "type",
    value: string
  ) => void;
  disabled?: boolean;
}) => {
  const [columnAdded, setColumnAdded] = React.useState(false);

  const isColumnValueDuplicated = useMemo(() => {
    const valueToKeys = new Map();
    columns.forEach(o =>
      valueToKeys.set(o.identifier, (valueToKeys.get(o.identifier) || 0) + 1)
    );

    return (column: VariableConfiguration) =>
      valueToKeys.get(column.identifier) > 1;
  }, [columns]);

  const columnValueErrors = useCallback(
    (column: VariableConfiguration) => {
      const isDuplicate = isColumnValueDuplicated(column);
      if (isDuplicate) {
        return d(commonErrors.duplicate, {
          name: d("ui.configuration.forms.question.select.options.value.label")
        });
      }
    },
    [d, isColumnValueDuplicated]
  );

  return (
    <Stack
      className="set-variable-properties-columns"
      alignment="left"
      gap="050"
      width="100"
      contentsWidth="100"
      padding="050"
      style={{
        backgroundColor: "var(--color-surface-primary)",
        borderRadius: "var(--border-radius-rounded, 8px)",
        cursor: disabled ? "not-allowed" : "default"
      }}
    >
      <Heading size="xs">
        {d("ui.configuration.forms.question.table.title")}
      </Heading>
      {/* heading */}
      {columns?.length ? (
        <Inline
          className="set-variable-properties-columns__header"
          width="100"
          gap="050"
          spaceBetween
          alignment="center"
        >
          <IconButton
            name=""
            fillStyle="filled"
            disabled
            style={{ visibility: "hidden" }}
          />
          <Inline width="100">
            <Label
              label={d(
                "ui.configuration.flows.variables.type.table.columnIdentifier.label"
              )}
              required
            />
          </Inline>
          <Box
            width="100"
            style={{
              width: "var(--spacing-1000)",
              minWidth: "var(--spacing-1000)"
            }}
          >
            <Label
              label={d("ui.configuration.flows.variables.type.label")}
              required
            />
          </Box>
          <IconButton
            name=""
            fillStyle="filled"
            disabled
            style={{ visibility: "hidden" }}
          />
        </Inline>
      ) : (
        <></>
      )}
      <Stack
        style={{
          pointerEvents: disabled ? "none" : "auto",
          userSelect: disabled ? "none" : "auto"
        }}
      >
        <DragAndDrop
          onDrop={reorderOptions}
          dropzoneTags={["set-variable-properties-columns"]}
          className="set-variable-properties-columns__list"
        >
          {columns?.map((column, index) => (
            <DraggableItem
              index={index}
              key={column.key}
              canDropOver={data => {
                return data.source.context.dropzoneTags.includes(
                  "set-variable-properties-columns"
                );
              }}
            >
              <Inline
                className="set-variable-properties-columns__item"
                width="100"
                gap="050"
                spaceBetween
                key={column.key}
                alignment="left"
              >
                <IconButton
                  name="do_not_disturb_on"
                  fillStyle="filled"
                  onClick={() => removeOption(index)}
                  disabled={disabled}
                />
                <TextField
                  width="100"
                  value={String(column.identifier)}
                  onChange={value => updateOption(index, "identifier", value)}
                  disabled={disabled}
                  error={columnValueErrors(column)}
                  maxLength={100}
                  autoFocus={columnAdded && index === columns.length - 1}
                  onlyTriggerChangeWhenBlur
                />
                <Box
                  alignment="left"
                  style={{
                    width: "var(--spacing-1000)",
                    minWidth: "var(--spacing-1000)"
                  }}
                >
                  <QuestionTypeSelect
                    type={column.type as QuestionTypes}
                    path={[]}
                    handleChange={value => {
                      updateOption(index, "type", value);
                    }}
                    disabled={disabled}
                    options={setVariablesTypeOptions}
                  />
                </Box>
                {!disabled ? (
                  <ReorderIcon />
                ) : (
                  <IconButton
                    name=""
                    fillStyle="filled"
                    disabled
                    style={{ visibility: "hidden" }}
                  />
                )}
              </Inline>
            </DraggableItem>
          ))}
        </DragAndDrop>
      </Stack>
      <Box alignment="left">
        <Button
          variant="text"
          onClick={() => {
            setColumnAdded(true);
            addOption();
          }}
          disabled={disabled}
          label={d("ui.configuration.forms.question.select.options.add")}
          leftIcon={{ name: "add_circle", fillStyle: "filled" }}
        />
      </Box>
    </Stack>
  );
};

const TableSetVariableProperties = ({
  variable,
  index,
  handleFieldChange,
  d
}: {
  variable: MockVariable & {
    value?: VariableValue;
  };
  index: number;
  handleFieldChange: (
    index: number,
    field: VariableFieldKeys,
    value?: string
  ) => void;
  d: Dictionary;
}) => {
  return (
    <Stack
      gap="100"
      width="100"
      contentsWidth="100"
      padding="075"
      style={{
        backgroundColor: "var(--color-surface-primary)",
        borderRadius: "8px"
      }}
    >
      <Select
        label={d("ui.configuration.flows.variables.type.table.operation.label")}
        description={d(
          "ui.configuration.flows.variables.type.table.operation.description"
        )}
        required
        name={`variables.${index}.operation`}
        value={variable.properties?.operation as string}
        onChange={value => {
          handleFieldChange(index, "properties.operation", value as string);
        }}
        onlyTriggerChangeWhenBlur
        options={getTableOperationOptions(d)}
      />
      {[
        TableOperationOptions.SET_CELL,
        TableOperationOptions.SET_COLUMN
      ].includes(variable.properties?.operation as TableOperationOptions) && (
        // Dropdown of columns
        <VariableField
          label={d(
            "ui.configuration.flows.variables.type.table.columnIdentifier.label"
          )}
          description={d(
            "ui.configuration.flows.variables.type.table.columnIdentifier.description"
          )}
          required
          value={(variable.properties?.columnIdentifier ?? "") as string}
          onChange={value => {
            handleFieldChange(index, "properties.columnIdentifier", value);
          }}
          schema={{
            type: QuestionTypes.TEXT
          }}
        />
      )}
      {[TableOperationOptions.SET_CELL, TableOperationOptions.SET_ROW].includes(
        variable.properties?.operation as TableOperationOptions
      ) && (
        <Stack>
          <Label
            label={d("ui.configuration.flows.variables.type.table.row.label")}
            description={d(
              "ui.configuration.flows.variables.type.table.row.description"
            )}
          />
          <Inline gap="100" alignment="left">
            <VariableField
              label={d(
                "ui.configuration.flows.variables.type.table.row.identifier.label"
              )}
              description={d(
                "ui.configuration.flows.variables.type.table.row.identifier.description"
              )}
              value={(variable.properties?.rowIdentifier ?? "") as string}
              onChange={value => {
                handleFieldChange(index, "properties.rowIdentifier", value);
              }}
              schema={{
                type: QuestionTypes.TEXT
              }}
            />
            {d("ui.common.or")}
            <VariableField
              label={d(
                "ui.configuration.flows.variables.type.table.row.index.label"
              )}
              description={d(
                "ui.configuration.flows.variables.type.table.row.index.description"
              )}
              value={(variable.properties?.rowIndex ?? "") as string}
              onChange={value => {
                handleFieldChange(index, "properties.rowIndex", value);
              }}
              schema={{
                type: QuestionTypes.NUMBER
              }}
            />
          </Inline>
        </Stack>
      )}
    </Stack>
  );
};

enum ListOperationOptions {
  SET_LIST = "setList",
  SET_ITEM = "setItem",
  REMOVE_ITEM = "removeItem"
}

function getListOperationOptions(d: Dictionary) {
  return Object.values(ListOperationOptions).map(option => ({
    value: option,
    label: d(
      `ui.configuration.flows.variables.type.list.operation.options.${option}.label`
    ),
    description: d(
      `ui.configuration.flows.variables.type.list.operation.options.${option}.description`
    )
  }));
}

const ListSetVariableProperties = ({
  variable,
  index,
  handleFieldChange,
  d
}: {
  variable: MockVariable & {
    value?: VariableValue;
  };
  index: number;
  handleFieldChange: (
    index: number,
    field: VariableFieldKeys,
    value?: string
  ) => void;
  d: Dictionary;
}) => {
  const itemIndexDescription =
    variable.properties?.listOperation === ListOperationOptions.REMOVE_ITEM
      ? ""
      : d("ui.configuration.flows.variables.type.list.item.index.description");
  return (
    <Stack
      gap="100"
      width="100"
      contentsWidth="100"
      padding="075"
      style={{
        backgroundColor: "var(--color-surface-primary)",
        borderRadius: "8px"
      }}
    >
      <Select
        label={d("ui.configuration.flows.variables.type.list.operation.label")}
        description={d(
          "ui.configuration.flows.variables.type.list.operation.description"
        )}
        required
        name={`variables.${index}.listOperation`}
        value={variable.properties?.listOperation as string}
        onChange={value => {
          handleFieldChange(index, "properties.listOperation", value as string);
        }}
        onlyTriggerChangeWhenBlur
        options={getListOperationOptions(d)}
      />
      {[
        ListOperationOptions.SET_ITEM,
        ListOperationOptions.REMOVE_ITEM
      ].includes(
        variable.properties?.listOperation as ListOperationOptions
      ) && (
        <Stack>
          <Inline gap="100" alignment="left">
            <VariableField
              label={d(
                "ui.configuration.flows.variables.type.list.item.index.label"
              )}
              description={itemIndexDescription}
              value={(variable.properties?.itemIndex ?? null) as string}
              onChange={value => {
                handleFieldChange(index, "properties.itemIndex", value);
              }}
              schema={{
                type: QuestionTypes.NUMBER
              }}
            />
          </Inline>
        </Stack>
      )}
    </Stack>
  );
};

const FileSetVariableProperties = ({
  variable,
  index,
  handleFieldChange,
  d
}: {
  variable: MockVariable & {
    value?: VariableValue;
  };
  index: number;
  handleFieldChange: (
    index: number,
    field: VariableFieldKeys,
    value?: string
  ) => void;
  d: Dictionary;
}) => {
  const itemIndexDescription =
    variable.properties?.fileOperation === FileOperationOptions.REMOVE_FILE
      ? ""
      : d("ui.configuration.flows.variables.type.files.item.index.description");
  return (
    <Stack
      gap="100"
      width="100"
      contentsWidth="100"
      padding="075"
      style={{
        backgroundColor: "var(--color-surface-primary)",
        borderRadius: "8px"
      }}
    >
      <Select
        label={d("ui.configuration.flows.variables.type.files.operation.label")}
        description={d(
          "ui.configuration.flows.variables.type.files.operation.description"
        )}
        required
        name={`variables.${index}.fileOperation`}
        value={variable.properties?.fileOperation as string}
        onChange={value => {
          handleFieldChange(index, "properties.fileOperation", value as string);
        }}
        onlyTriggerChangeWhenBlur
        options={getFileOperationOptions(d)}
      />
      {[FileOperationOptions.REMOVE_FILE].includes(
        variable.properties?.fileOperation as FileOperationOptions
      ) && (
        <Stack>
          <Inline gap="100" alignment="left">
            <VariableField
              label={d(
                `ui.configuration.flows.variables.type.files.operation.options.${variable.properties?.fileOperation}.parameters.itemIndex.label`
              )}
              description={itemIndexDescription}
              value={(variable.properties?.itemIndex ?? null) as string}
              onChange={value => {
                handleFieldChange(index, "properties.itemIndex", value);
              }}
              schema={{
                type: QuestionTypes.NUMBER
              }}
            />
          </Inline>
        </Stack>
      )}
    </Stack>
  );
};
