import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";

import { reorderWithEdge } from "@atlaskit/pragmatic-drag-and-drop-hitbox/util/reorder-with-edge";
import {
  Box,
  Button,
  Divider,
  DragAndDrop,
  Inline,
  OnDrop,
  Stack
} from "@oneteam/onetheme";

import { getStepIdPathToStepId } from "@helpers/flows/flowHelpers";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";

import { useDictionary } from "@src/hooks/useDictionary";
import { FlowPath } from "@src/types/FlowConfiguration/FlowConfiguration.ts";
import { FlowStep } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { SetVariablesStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import {
  TableVariableColumn,
  Variable,
  VariableConfiguration,
  VariableFieldKeys
} from "@src/types/FlowConfiguration/Variables";
import { QuestionTypes } from "@src/types/Question.ts";

import { SetVariableItem } from "./SetVariableItem";
import "./SetVariablesFlowStepProperties.scss";

export const SetVariablesFlowStepProperties = ({
  step,
  onFieldChange,
  onVariableAdd,
  onVariableRemove,
  onVariableUpdate,
  path
}: {
  step: FlowStep<SetVariablesStepProperties>;
  onFieldChange: (
    index: number,
    field: VariableFieldKeys,
    value?: string
  ) => void;
  onVariableAdd: (variable: Variable) => void;
  onVariableRemove: (index: number) => void;
  onVariableUpdate: ({
    variableIdentifier,
    sourceVariableStepIds,
    columns,
    change
  }: {
    variableIdentifier: string;
    sourceVariableStepIds: string[];
    columns: VariableConfiguration[];
    change: { op: string; index: number };
  }) => void;
  path: FlowPath;
}) => {
  const { configurationFlow, mockFlowContext, setSelectedStepId } =
    useConfigurationFlowContext();
  const d = useDictionary();
  const dropzoneTag = `variables-${step.id}`;
  const { variables = [] } = step.properties;
  const [expandedVariable, setExpandedVariable] = useState<{
    [key: string]: boolean;
  }>(variables.reduce((acc, _, index) => ({ ...acc, [index]: true }), {}));
  const [autoFocusLast, setAutoFocusLast] = useState(false);

  const stepIdPathToStepId = useMemo(
    () =>
      configurationFlow
        ? getStepIdPathToStepId({
            stepId: step.id,
            configurationFlow
          })
        : [],
    [configurationFlow, step.id]
  );

  const getUniqueIdentifier = useCallback(
    (name?: string) => {
      const variableNames = Object.values(mockFlowContext?.variables ?? {}).map(
        v => v.identifier
      );

      const idealName = name ?? "variable";
      let uniqueIdentifier = idealName;
      let variableCount = 1;
      while (variableNames.includes(uniqueIdentifier)) {
        uniqueIdentifier = `${idealName}_${variableCount}`;
        variableCount++;
      }
      return uniqueIdentifier;
    },
    [mockFlowContext?.variables]
  );

  const renderContent = useCallback(() => {
    function handleGoToSource(sourceStepId: string) {
      setSelectedStepId(sourceStepId);
    }

    const occurrencesWithinStep: {
      [identifier: string]: number;
    } = {};
    return variables.map((variable, variableIndex) => {
      const sourceVariable = mockFlowContext?.variables?.[variable.identifier];

      const sources = sourceVariable?.stepIds
        ?.filter(s => stepIdPathToStepId.indexOf(s) !== -1)
        .sort((a, b) => {
          return stepIdPathToStepId.indexOf(a) - stepIdPathToStepId.indexOf(b);
        });
      const sourceStepId = sources?.[0];

      const sourceVariableStepIds = sourceStepId
        ? [sourceStepId]
        : (sourceVariable?.stepIds ?? []);

      const columns =
        step.properties?.variables[variableIndex].properties?.columns?.map(
          (column, idx) => ({
            key: idx,
            identifier: column.identifier,
            type: column.type
          })
        ) ?? [];

      const addOption = () => {
        onVariableUpdate({
          variableIdentifier: variable.identifier,
          sourceVariableStepIds,
          columns: [
            {
              identifier: "",
              type: QuestionTypes.TEXT
            }
          ],
          change: {
            op: TableVariableColumn.ADD,
            index: columns.length
          }
        });
      };

      const duplicateOption = () => {
        onVariableAdd({
          ...JSON.parse(JSON.stringify(variable)),
          // Duplicate the variable with a new identifier
          identifier: getUniqueIdentifier(variable.identifier)
        });
      };

      const updateOption = (
        index: number,
        property: "identifier" | "type",
        value: string
      ) => {
        const column = {
          identifier: columns[index].identifier,
          type: columns[index].type
        };
        onVariableUpdate({
          variableIdentifier: variable.identifier,
          sourceVariableStepIds,
          columns: [
            {
              ...column,
              [property]: value
            }
          ],
          change: {
            op: TableVariableColumn.UPDATE,
            index
          }
        });
      };

      const removeOption = (index: number) => {
        onVariableUpdate({
          variableIdentifier: variable.identifier,
          sourceVariableStepIds,
          columns: [columns[index]],
          change: {
            op: TableVariableColumn.DELETE,
            index
          }
        });
      };

      const reorderItems: OnDrop = ({ source, destination }) => {
        const reordered = reorderWithEdge({
          list: columns,
          startIndex: source.index,
          indexOfTarget: destination.index,
          closestEdgeOfTarget: destination.closestEdgeOfTarget,
          axis: "vertical"
        }).map(c => ({
          identifier: c.identifier,
          type: c.type
        }));

        onVariableUpdate({
          variableIdentifier: variable.identifier,
          sourceVariableStepIds,
          columns: reordered,
          change: {
            op: TableVariableColumn.REORDER,
            index: 0
          }
        });
      };

      occurrencesWithinStep[variable.identifier] =
        1 + (occurrencesWithinStep[variable.identifier] ?? 0);

      return (
        <SetVariableItem
          key={`${variableIndex}.${variable.identifier}`}
          index={variableIndex}
          variable={variable}
          columns={columns}
          addOption={addOption}
          removeOption={removeOption}
          updateOption={updateOption}
          reorderOptions={reorderItems}
          expandedVariable={expandedVariable}
          setExpandedVariable={setExpandedVariable}
          handleFieldChange={onFieldChange}
          isFirstItem={variableIndex === 0}
          hasDivider={variableIndex !== variables.length - 1}
          dropzoneTag={dropzoneTag}
          autoFocusLast={
            autoFocusLast && variableIndex === variables.length - 1
          }
          handleRemove={
            variables.length > 1
              ? () => onVariableRemove(variableIndex)
              : undefined
          }
          handleDuplicate={duplicateOption}
          sourceStepId={sourceStepId}
          handleGoToSource={
            sourceStepId ? () => handleGoToSource(sourceStepId) : undefined
          }
          occurrenceWithinStep={occurrencesWithinStep[variable.identifier]}
          stepOutputVariables={mockFlowContext?.stepOutputVariables}
          path={path}
        />
      );
    });
  }, [
    variables,
    setSelectedStepId,
    mockFlowContext?.variables,
    mockFlowContext?.stepOutputVariables,
    step.properties?.variables,
    expandedVariable,
    onFieldChange,
    dropzoneTag,
    autoFocusLast,
    path,
    stepIdPathToStepId,
    onVariableUpdate,
    onVariableAdd,
    getUniqueIdentifier,
    onVariableRemove
  ]);

  return (
    <Stack gap="000" height="100" style={{ maxHeight: "80vh" }}>
      <Stack gap="100" style={{ maxHeight: "100%" }} overflow="auto">
        {variables?.length > 0 && (
          <Box
            height="fit"
            style={{
              maxHeight: "100%",
              paddingBottom: "var(--spacing-100)",
              overflowY: "auto",
              overflowX: "hidden"
            }}
          >
            <DragAndDrop onDrop={() => {}} dropzoneTags={[dropzoneTag]}>
              {renderContent()}
            </DragAndDrop>
          </Box>
        )}
      </Stack>
      <Stack gap="100" alignment="left">
        {variables.length > 0 && <Divider />}
        <Inline spaceBetween width="100">
          <Button
            label={d(
              "ui.configuration.flows.step.variants.setVariables.propertiesModal.addButton"
            )}
            variant="secondary"
            onClick={() => {
              const uniqueIdentifier = getUniqueIdentifier();

              onVariableAdd({
                type: QuestionTypes.TEXT,
                // Temporary identifier
                identifier: uniqueIdentifier,
                value: ""
              });
              setExpandedVariable({
                ...expandedVariable,
                [variables.length]: true // Make the new variable expanded in UI
              });
              setAutoFocusLast(true);
              setTimeout(
                () => setAutoFocusLast(false),
                0 // Wait for the new variable to be rendered
              );
            }}
            leftIcon={{ name: "add" }}
          />
          {variables.length > 0 && (
            <Box alignment="right">
              {!Object.values(expandedVariable)?.find(item => item === true) ? (
                <Button
                  variant="text"
                  label={d(
                    "ui.configuration.flows.step.variants.common.propertiesModal.expandAll"
                  )}
                  onClick={() =>
                    setExpandedVariable(
                      variables.reduce(
                        (acc, _, index) => ({ ...acc, [index]: true }),
                        {}
                      )
                    )
                  }
                />
              ) : (
                <Button
                  variant="text"
                  label={d(
                    "ui.configuration.flows.step.variants.common.propertiesModal.collapseAll"
                  )}
                  onClick={() => setExpandedVariable({})}
                />
              )}
            </Box>
          )}
        </Inline>
      </Stack>
    </Stack>
  );
};
