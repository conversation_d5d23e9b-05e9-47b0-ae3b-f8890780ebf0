import React from "react";

import {
  Box,
  DropdownItem,
  DropdownItemCheckbox,
  DropdownItemGroup,
  DropdownMenu,
  Icon,
  WhiteboardTool,
  WhiteboardToolVariant,
  WhiteboardToolbar
} from "@oneteam/onetheme";
import { generatePath, useNavigate, useParams } from "react-router-dom";
import useOnClickOutside from "use-onclickoutside";

import { commonIcons } from "@src/constants/iconConstants";
import { routeConstants } from "@src/constants/routeConstants";
import { Dictionary } from "@src/hooks/useDictionary";
import {
  FlowConfiguration,
  FlowConfigurationStatusType
} from "@src/types/FlowConfiguration/FlowConfiguration";

import { useConfigurationFlowContext } from "../../ConfigurationFlowContext";
import {
  ConfigurationFlowHelpModal,
  ConfigurationFlowHelpModalRef
} from "../ConfigurationFlowHelpModal/ConfigurationFlowHelpModal";

export const ConfigurationFlowToolbar = ({
  d,
  configurationFlow,
  handleUpdateSetting,
  handleFlowStatusSetting,
  handleToggleFlowHidden
}: {
  d: Dictionary;
  configurationFlow: FlowConfiguration;
  handleUpdateSetting: (key: string, value: boolean) => void;
  handleFlowStatusSetting: () => void;
  handleToggleFlowHidden: () => void;
}) => {
  const params = useParams();
  const navigate = useNavigate();
  const [openItem, setOpenItem] = React.useState<string | undefined>(undefined);
  const { settings } = useConfigurationFlowContext();
  const helpModalRef = React.useRef<ConfigurationFlowHelpModalRef>(null);
  const ref = React.useRef(null);

  useOnClickOutside(ref, () => {
    setOpenItem(undefined);
  });
  return (
    <Box ref={ref}>
      <WhiteboardToolbar
        style={{
          padding: "var(--spacing-050, 4px)",
          gap: "var(--components-whiteboard-toolbar-gap, 8px)"
        }}
      >
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="File"
              isSelected={isOpen}
              onClick={() => {
                console.log("File clicked");
                //   onClick();
                setOpenItem(current =>
                  current === "file" ? undefined : "file"
                );
              }}
            />
          )}
          isOpen={openItem === "file"}
          // onOpenChange={isOpen =>
          //   setOpenItem(current => (current ? "file" : undefined))
          // }
        >
          <DropdownItemGroup>
            <DropdownItem
              leftElement={<Icon name="arrow_back" />}
              onClick={() => {
                navigate(
                  generatePath(routeConstants.configurationFlowList, params)
                );
                setOpenItem(undefined);
              }}
            >
              Back to flows
            </DropdownItem>
          </DropdownItemGroup>
          <DropdownItemGroup hasDivider>
            <DropdownItem
              leftElement={<Icon name="help" />}
              onClick={() => {
                helpModalRef?.current?.openModal();
              }}
            >
              {d("ui.common.help")}
            </DropdownItem>
          </DropdownItemGroup>
          <DropdownItemGroup hasDivider>
            <DropdownItem
              id="status"
              leftElement={
                <Icon
                  name={
                    configurationFlow.properties?.hidden
                      ? "visibility"
                      : "visibility_off"
                  }
                />
              }
              onClick={() => {
                handleToggleFlowHidden();
                setOpenItem(undefined);
              }}
            >
              {configurationFlow.properties?.hidden
                ? d("ui.common.show")
                : d("ui.common.hide")}
            </DropdownItem>
            <DropdownItem
              leftElement={
                <Icon
                  name={
                    configurationFlow.status ===
                    FlowConfigurationStatusType.INACTIVE
                      ? "add_circle"
                      : "do_not_disturb_on"
                  }
                />
              }
              onClick={() => {
                handleFlowStatusSetting();
                setOpenItem(undefined);
              }}
              description={
                configurationFlow.status ===
                FlowConfigurationStatusType.INACTIVE
                  ? d(`ui.configuration.flows.status.active.action.description`)
                  : d(
                      `ui.configuration.flows.status.inactive.action.description`
                    )
              }
            >
              {configurationFlow.status === FlowConfigurationStatusType.INACTIVE
                ? d(`ui.configuration.flows.status.active.action.label`)
                : d(`ui.configuration.flows.status.inactive.action.label`)}
            </DropdownItem>
          </DropdownItemGroup>
        </DropdownMenu>
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="Edit"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "edit" ? undefined : "edit"
                );
              }}
            />
          )}
          isOpen={openItem === "edit"}
        >
          <DropdownItemGroup>
            <DropdownItem
              leftElement={<Icon {...commonIcons.rename} />}
              onClick={() => {
                if (settings?.isRenaming) {
                  handleUpdateSetting("isRenaming", false);
                  setTimeout(() => {
                    handleUpdateSetting("isRenaming", true);
                  });
                } else {
                  handleUpdateSetting("isRenaming", true);
                }
                setOpenItem(undefined);
              }}
            >
              {d("ui.common.rename")}
            </DropdownItem>
          </DropdownItemGroup>
        </DropdownMenu>
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="View"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "view" ? undefined : "view"
                );
              }}
            />
          )}
          isOpen={openItem === "view"}
          width="maxContent"
        >
          <DropdownItemGroup>
            <DropdownItemCheckbox
              isChecked={settings?.debugMode ?? false}
              onChange={updated => handleUpdateSetting("debugMode", updated)}
            >
              <Box width="maxContent">Debug mode</Box>
            </DropdownItemCheckbox>
            <DropdownItemCheckbox
              isChecked={settings?.showMiniMap ?? false}
              onChange={updated => handleUpdateSetting("showMiniMap", updated)}
            >
              Mini map
            </DropdownItemCheckbox>
          </DropdownItemGroup>
        </DropdownMenu>
      </WhiteboardToolbar>
      <ConfigurationFlowHelpModal ref={helpModalRef} />
    </Box>
  );
};
