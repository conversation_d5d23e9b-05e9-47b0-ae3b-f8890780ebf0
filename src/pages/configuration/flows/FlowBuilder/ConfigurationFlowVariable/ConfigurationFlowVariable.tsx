import React, {
  PropsWith<PERSON>hildren,
  SyntheticEvent,
  useEffect,
  useRef
} from "react";

import {
  Box,
  FloatingWithParent,
  Icon,
  IconButton,
  Inline,
  Renamable,
  Text,
  Tooltip
} from "@oneteam/onetheme";
import { CopyToClipboard } from "react-copy-to-clipboard";

import { QuestionType } from "@components/forms/QuestionType/QuestionType";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary";
import { MockVariable } from "@src/types/FlowConfiguration/FlowConfiguration";
import { FlowStepVariant } from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import "./ConfigurationFlowVariable.scss";

export const ConfigurationFlowVariable = ({
  variable,
  name = variable.identifier,
  copyText = `{{${variable.identifier}}}`,
  variant = "default",
  handleRename,
  handleGoToSource,
  isAvailable = true,
  onClick,
  kebabMenu = <></>
}: {
  variable: MockVariable;
  name?: string;
  copyText?: string;
  variant?: "default" | "line-item";
  handleRename?: (newName: string) => void;
  handleGoToSource?: () => void;
  isAvailable?: boolean;
  onClick?: (e: SyntheticEvent<HTMLElement, Event>) => void;
  kebabMenu?: React.ReactNode;
}) => {
  const d = useDictionary();
  const parentRef = useRef<HTMLDivElement>(null);
  const [copied, setCopied] = React.useState(false);

  useEffect(() => {
    // reset copied state after 2 seconds
    if (copied) {
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    }
  }, [copied]);

  return (
    <VariableWrapper
      copyText={copyText}
      onCopy={() => setCopied(true)}
      variant={variant}
    >
      <Inline
        overflow="hidden"
        classNames={["flow-variable", `flow-variable--variant-${variant}`]}
        gap="050"
        alignment="left"
        position="relative"
      >
        <Inline width="100" gap="050" alignment="left" spaceBetween>
          <Inline
            gap="025"
            alignment="left"
            overflow="hidden"
            onClick={onClick}
          >
            <QuestionType
              className="flow-variable__icon"
              iconOnly
              type={variable.type ?? "unknown"}
              size="small"
              contentStyle={{
                opacity: !isAvailable ? 0.3 : 1
              }}
            />
            <TextWrapper isAvailable={isAvailable} d={d}>
              <Inline
                gap="025"
                alignment="left"
                wrap
                style={{
                  opacity: !isAvailable ? 0.3 : 1
                }}
              >
                <Text
                  className="flow-variable__identifier"
                  size="m"
                  weight="regular"
                >
                  {handleRename &&
                  !variable.isStartingVariable &&
                  variable.sourceStepVariant !== FlowStepVariant.ITERATOR ? (
                    <Renamable
                      value={variable.identifier}
                      onChange={handleRename}
                    />
                  ) : (
                    name
                  )}
                </Text>
                {variable.description && (
                  <Text
                    className="flow-variable__description"
                    size="xs"
                    color="text-tertiary"
                  >
                    {variable.description}
                  </Text>
                )}
              </Inline>
            </TextWrapper>
          </Inline>
          <Inline gap="050">
            {handleGoToSource && (
              <Inline gap="050">
                <IconButton
                  className="flow-variable__copy"
                  label={d("ui.configuration.flows.variables.openSourceStep")}
                  size="m"
                  name="my_location"
                  color="text-primary"
                  onClick={e => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleGoToSource();
                  }}
                />
              </Inline>
            )}
            {variant === "line-item" && (
              <CopyToClipboard text={copyText} onCopy={() => setCopied(true)}>
                <Box ref={parentRef}>
                  <IconButton
                    className="flow-variable__copy"
                    name="content_copy"
                    size="m"
                    color={copied ? "text-tertiary" : "text-primary"}
                  />
                </Box>
              </CopyToClipboard>
            )}
            {kebabMenu && (
              <Box width="fit" className="flow-variable__kebab">
                {kebabMenu}
              </Box>
            )}

            {variable.isStartingVariable && (
              <Tooltip
                content={d(
                  "ui.configuration.flows.variables.startingVariable.title"
                )}
              >
                <Icon size="m" name="play_circle" color="color" />
              </Tooltip>
            )}
            {variable.isEndingVariable && (
              <Tooltip
                content={d(
                  "ui.configuration.flows.variables.endingVariable.title"
                )}
              >
                <Icon size="m" name="chip_extraction" color="color" />
              </Tooltip>
            )}
          </Inline>
        </Inline>
        {copied && (
          <FloatingWithParent parentRef={parentRef} position="right">
            <Inline
              alignment="left"
              padding="025"
              style={{
                borderRadius: "var(--components-tooltip-border-radius, 4px)",
                background: "var(--components-tooltip-color-background)"
              }}
            >
              <Icon name="check" size="s" color="text-invert-primary" />
              <Text size="m" color="text-invert-primary">
                Copied!
              </Text>
            </Inline>
          </FloatingWithParent>
        )}
      </Inline>
    </VariableWrapper>
  );
};

const TextWrapper = ({
  isAvailable,
  d,
  children
}: PropsWithChildren<{
  isAvailable: boolean;
  d: Dictionary;
}>) => {
  if (isAvailable) {
    return <>{children}</>;
  }
  return (
    <Tooltip
      content={d("ui.configuration.flows.variables.notAvailableTooltip")}
    >
      {children}
    </Tooltip>
  );
};

const VariableWrapper = ({
  variant,
  copyText,
  onCopy,
  children
}: PropsWithChildren<{
  variant: "default" | "line-item";
  copyText: string;
  onCopy?: (text: string, result: boolean) => void;
}>) => {
  if (variant === "line-item") {
    return <>{children}</>;
  }
  return (
    <CopyToClipboard text={copyText} onCopy={onCopy}>
      {children}
    </CopyToClipboard>
  );
};
