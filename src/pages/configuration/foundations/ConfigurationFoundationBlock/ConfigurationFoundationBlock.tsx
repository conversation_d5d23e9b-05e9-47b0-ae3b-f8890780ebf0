import React, { useCallback, useEffect, useMemo, useState } from "react";

import {
  ColorText,
  ConfirmationModal,
  FontWeight,
  Form,
  Heading,
  HeadingSize,
  IconButton,
  Inline,
  ModalDialog,
  Overlay,
  Stack,
  Text,
  WhiteboardBlock,
  getClassNames,
  returnStringIfTrue
} from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";
import { useOutletContext } from "react-router-dom";

import {
  DocumentErrorHelper,
  ErrorFieldKeyMap
} from "@helpers/DocumentErrorHelper.ts";
import { initialSetupName } from "@helpers/configurationFormHelper";

import { WhiteboardStatusCircle } from "@components/shared/WhiteboardStatusCircle.tsx";

import { validateFoundationDeletion } from "@pages/configuration/foundations/ConfigurationFoundationHelper";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace";

import "./ConfigurationFoundationBlock.scss";

export interface ConfigurationFoundationBlockProps {
  id: string;
  index: number;
  accessor: string;
  isDisabled?: boolean;
  isRenamable?: boolean;
  handlers?: {
    handleRename?: (foundationConfiguration: FoundationConfiguration) => void;
    handleDelete?: (id: string, formIdsForFoundation?: string[]) => void;
    handleOnClick?: (id: string) => void;
  };
  isSelected?: boolean;
  isHighlighted?: boolean;
  controlFocus?: boolean;
  size?: "container" | "medium";
}

export const ConfigurationFoundationBlock = ({
  id,
  index,
  accessor,
  isDisabled,
  handlers,
  isSelected,
  isHighlighted,
  controlFocus,
  isRenamable = true,
  size
}: ConfigurationFoundationBlockProps) => {
  const [confirmDeleteModalOpen, setConfirmDeleteModalOpen] = useState(false);
  const [confirmWarningModalOpen, setConfirmWarningModalOpen] = useState(false);
  const [formIdsForFoundation, setFormIdsForFoundation] = useState<string[]>(
    []
  );
  const { getValues, setError, clearErrors } = useFormContext();
  const d = useDictionary();

  const foundationConfiguration = getValues(accessor);

  const { workspace, document } = useOutletContext<{
    workspace: Workspace;
    document: WorkspaceDocument;
  }>();

  const { docErrorHelper } = useOutletContext<{
    docErrorHelper: DocumentErrorHelper;
  }>();

  const errors: ErrorFieldKeyMap[] = useMemo(() => {
    return (
      docErrorHelper.getDictionaryMessagesForErrors({
        prefix: `$.foundations.entities.${id}`,
        resourceName: "foundations",
        index: index
      }) ?? []
    );
  }, [docErrorHelper, id, index]);

  useEffect(() => {
    // zod error comes up instantaneously, before this useEffect runs
    // ideally zod and server errors should be in sync
    // we would have a slight delay for error to pop up in extremely slow networks
    // delay of around 3/4 seconds in 100kbps network, testing with dev tools in local
    clearErrors(`${accessor}`);

    errors?.forEach(error => {
      setError(`${error.accessor}`, { message: d(error.message) });
    });
  }, [accessor, clearErrors, d, errors, setError]);

  const statusCircle = useMemo(() => {
    if (!errors?.length) {
      return <></>;
    }
    return (
      <WhiteboardStatusCircle
        tooltipContent={d("errors.foundationConfiguration.tooltip")}
      />
    );
  }, [errors, d]);

  // Handle the delete action
  const handleDeleteAction = useCallback(() => {
    validateFoundationDeletion(workspace.id, id).then(
      canDeleteFoundationLevelInCollection => {
        const existingFormIdsForFoundation = Object.keys(
          document.forms ?? {}
        ).filter(formId => document.forms[formId].foundationId === id);
        setFormIdsForFoundation(existingFormIdsForFoundation);

        if (!canDeleteFoundationLevelInCollection) {
          setConfirmDeleteModalOpen(true);
        } else if (existingFormIdsForFoundation.length) {
          setConfirmWarningModalOpen(true);
        } else {
          handlers?.handleDelete?.(
            getValues(accessor).id,
            existingFormIdsForFoundation
          );
        }
      }
    );
  }, [
    workspace.id,
    id,
    document.forms,
    setFormIdsForFoundation,
    setConfirmDeleteModalOpen,
    setConfirmWarningModalOpen,
    handlers,
    getValues,
    accessor
  ]);

  const confirmDeleteModal = useMemo(() => {
    if (!confirmDeleteModalOpen) {
      return null;
    }

    return (
      <ModalDialog isOpen>
        <Overlay isOpen />
        <ConfirmationModal
          closeOnClickOutside
          variant="danger"
          heading={d("ui.configuration.foundations.deleteModal.title", {
            FoundationLevelName: foundationConfiguration?.name
          })}
          message={d("ui.configuration.foundations.deleteModal.message")}
          onConfirm={() => {
            setConfirmDeleteModalOpen(false);
          }}
          confirmLabel={d(
            "ui.configuration.foundations.deleteModal.confirmLabel"
          )}
          hideCancelButton
        />
      </ModalDialog>
    );
  }, [d, confirmDeleteModalOpen, foundationConfiguration?.name]);

  const confirmWarningModal = useMemo(() => {
    if (!confirmWarningModalOpen) {
      return null;
    }

    return (
      <ModalDialog isOpen>
        <Overlay isOpen />
        <ConfirmationModal
          closeOnClickOutside
          variant="warning"
          heading={d("ui.configuration.foundations.warningModal.title", {
            FoundationLevelName: foundationConfiguration?.name
          })}
          message={d("ui.configuration.foundations.warningModal.message")}
          onConfirm={() => {
            handlers?.handleDelete?.(
              getValues(accessor).id,
              formIdsForFoundation
            );
            setConfirmWarningModalOpen(false);
          }}
          confirmLabel={d("$d(ui.common.delete)")}
          onCancel={() => {
            setConfirmWarningModalOpen(false);
          }}
        />
      </ModalDialog>
    );
  }, [
    d,
    confirmWarningModalOpen,
    foundationConfiguration?.name,
    accessor,
    formIdsForFoundation,
    getValues,
    handlers
  ]);

  return (
    <>
      <WhiteboardBlock
        className={getClassNames([
          "configuration-foundation-block",
          returnStringIfTrue(size, `configuration-foundation-block--${size}`)
        ])}
        isSelected={isSelected}
        isHighlighted={isHighlighted}
        onClick={() => handlers?.handleOnClick?.(id)}
      >
        {statusCircle}
        <Inline gap="100" alignment="left" spaceBetween>
          <Inline gap="100" width="100" alignment="left">
            {/* show level for all but the root node */}
            {/* {index !== 0 && ( */}
            <Heading
              size={HeadingSize.S}
              weight={FontWeight.REGULAR}
              color={ColorText.TERTIARY}
            >
              {index + 1}
            </Heading>
            {/* )} */}
            <Stack>
              <Heading
                size={HeadingSize.S}
                weight={FontWeight.REGULAR}
                style={{
                  maxWidth: "100%"
                }}
              >
                {isDisabled || !isRenamable ? (
                  foundationConfiguration?.name
                ) : (
                  <Form.Renamable
                    name={`${accessor}.name`}
                    onChange={() => {
                      handlers?.handleRename?.(getValues(`${accessor}`));
                    }}
                    disabled={isDisabled || !isRenamable}
                    controlFocus={
                      controlFocus ||
                      getValues(`${accessor}.name`)?.includes(initialSetupName)
                    }
                  />
                )}
              </Heading>
              {foundationConfiguration?.description && (
                <Text
                  size="s"
                  color="text-secondary"
                  style={{
                    marginTop: "-2px",
                    marginLeft: "2px"
                  }}
                >
                  {foundationConfiguration?.description}
                </Text>
              )}
            </Stack>
          </Inline>
          <Inline gap="050" className="configuration-foundation-block__actions">
            {handlers?.handleDelete && !isDisabled && (
              <IconButton
                name="delete"
                onClick={event => {
                  event.stopPropagation();
                  handleDeleteAction();
                }}
              />
            )}
          </Inline>
        </Inline>
      </WhiteboardBlock>
      {confirmDeleteModal}
      {confirmWarningModal}
    </>
  );
};
