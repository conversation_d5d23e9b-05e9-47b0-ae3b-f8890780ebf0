// https://tkdodo.eu/blog/react-query-and-forms
// https://www.reddit.com/r/reactjs/comments/111mrek/react_hook_form_tanstack_usequery_async/
import React from "react";

import { Doc } from "@automerge/automerge-repo";
import { Box, Loading } from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { getResourceLength } from "@helpers/OrderedMapNoState.ts";

import { ConfigurationFoundationForm } from "@pages/configuration/foundations/ConfigurationFoundationForm.tsx";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { buildShortPageTitle, usePageTitle } from "@src/hooks/usePageTitle.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";
import { Workspace } from "@src/types/workspace.ts";

export const ConfigurationFoundation = () => {
  const { workspace, document, docChange } = useOutletContext<{
    workspace: Workspace;
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const d = useDictionary();

  usePageTitle(
    buildShortPageTitle({
      section: d("ui.configuration.foundations.title"),
      context: workspace?.key ?? ""
    }),
    !!workspace?.key
  );

  if (!getResourceLength(document?.foundations) || !workspace?.id) {
    return (
      <Box height="100" contentsHeight="fill">
        <Loading size={24} />
      </Box>
    );
  }

  return (
    <ConfigurationFoundationForm
      foundations={document?.foundations}
      docChange={docChange}
    />
  );
};
