import React, { useCallback, useMemo, useState } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Button,
  ButtonGroup,
  ColorText,
  Inline,
  PageBodyTemplate,
  Pill,
  PillVariant,
  SearchBar,
  StatusLine,
  StatusLineVariant,
  TableWithPagination,
  Text,
  TextAlignment,
  TextSize,
  Tooltip,
  useQueryParams,
  useTableSort
} from "@oneteam/onetheme";
import { useOutletContext, useSearchParams } from "react-router-dom";

import { DocumentErrorHelper } from "@helpers/DocumentErrorHelper.ts";
import { PaginatedTable, Sort } from "@helpers/PaginatedTable.ts";

import { formatDate } from "@pages/collection/flows/FlowExecutionHelper";

import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs.tsx";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import { buildShortPageTitle, usePageTitle } from "@src/hooks/usePageTitle.ts";
import { LabelConfiguration, labelColorToPill } from "@src/types/Label";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";
import { Workspace } from "@src/types/workspace.ts";

import { ConfigurationLabelModal } from "./ConfigurationLabelModal";

// import "./ConfigurationSeriesList.scss";

enum ModalState {
  CLOSED,
  CREATE,
  EDIT
}

export const ConfigurationLabelListPage = () => {
  const d = useDictionary();
  const breadcrumbs = useBreadcrumbs();
  const { document, docErrorHelper, workspace } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docErrorHelper: DocumentErrorHelper;
    workspace: Workspace;
  }>();

  usePageTitle(
    buildShortPageTitle({
      section: d("ui.configuration.labels.title"),
      context: workspace?.key ?? ""
    }),
    !!workspace?.key
  );

  const configurationLabels: LabelConfiguration[] = useMemo(() => {
    const tempLabelConfigurations = Object.values(document.labels ?? {});
    tempLabelConfigurations.forEach((label: LabelConfiguration) => {
      label.hasErrors =
        docErrorHelper.getErrorCountByPrefix(`$.labels.${label.id}`) > 0;
    });
    return tempLabelConfigurations;
  }, [document.labels, docErrorHelper]);

  const { queryParams, updateQueryParams } = useQueryParams({
    initialDefaultIfNone: { page: "1" },
    useSearchParams
  });

  const columns = getColumns(d);
  const { sort, handleSetSort } = useTableSort<LabelConfiguration>({
    columns,
    queryParams,
    updateQueryParams: newParams => {
      updateQueryParams({
        page: newParams.page,
        pageSize: pageSize,
        search: textFilter,
        asc: newParams.asc,
        dsc: newParams.dsc
      });
    }
  });
  const { textFilter, page, pageSize, asc, dsc } = useMemo(
    () => ({
      textFilter: queryParams.get("search") ?? undefined,
      page: queryParams.get("page") ?? undefined,
      pageSize: queryParams.get("pageSize") ?? undefined,
      asc: queryParams.get("asc") ?? undefined,
      dsc: queryParams.get("dsc") ?? undefined
    }),
    [queryParams]
  );

  const doUpdateQueryParams = useCallback(
    (newParams: object) => {
      const current = {
        page: page,
        pageSize: pageSize,
        search: textFilter,
        asc: asc,
        dsc: dsc
      };
      // merge new properties into current
      updateQueryParams({
        ...current,
        ...newParams
      });
    },
    [page, pageSize, textFilter, asc, dsc, updateQueryParams]
  );

  // on change handlers
  const onChangePage = useCallback(
    (newPage: number) => {
      doUpdateQueryParams({ page: String(newPage) });
    },
    [doUpdateQueryParams]
  );

  const onChangeSearchTerm = useCallback(
    (input: string) => {
      doUpdateQueryParams({
        search: input,
        page: "1", // reset page to 1 when changing filter criteria since the total count may change
        pageSize: pageSize
      });
    },
    [doUpdateQueryParams, pageSize]
  );

  const table = useMemo(() => {
    return new PaginatedTable(
      configurationLabels,
      page ? parseInt(page) : 1,
      [
        (data: LabelConfiguration) =>
          !textFilter ||
          data.name.toLowerCase().includes(textFilter.toLowerCase()) ||
          (data.description ?? "")
            ?.toLowerCase()
            .includes(textFilter.toLowerCase())
      ],
      Sort.from(asc, dsc)
    );
  }, [configurationLabels, page, asc, dsc, textFilter]);

  const [modalState, setModalState] = useState(ModalState.CLOSED);
  const [selectedLabel, setSelectedLabel] = useState<LabelConfiguration | null>(
    null
  );

  return (
    <PageBodyTemplate
      breadcrumbs={breadcrumbs}
      heading={d("ui.configuration.labels.title")}
      overlay={
        modalState !== ModalState.CLOSED ? (
          <ConfigurationLabelModal
            onOpenChange={() => {
              setModalState(ModalState.CLOSED);
              setSelectedLabel(null);
            }}
            labelConfigToEdit={selectedLabel}
          />
        ) : (
          <></>
        )
      }
      withoutScroll
    >
      <Inline gap="100" spaceBetween>
        <Inline gap="100" alignment="left">
          <SearchBar
            withDebounce
            handleChange={onChangeSearchTerm}
            placeholder={d("ui.common.search")}
            value={textFilter ?? ""}
            autoFocus
          />
        </Inline>
        <ButtonGroup alignment="right">
          <Button
            label={d("ui.configuration.labels.createButton")}
            onClick={() => {
              setSelectedLabel(null);
              setModalState(ModalState.CREATE);
            }}
          ></Button>
        </ButtonGroup>
      </Inline>

      <TableWithPagination
        isLoading={configurationLabels === undefined}
        fillContainer
        columns={columns}
        data={table.dataForPage()}
        itemsPerPage={table.pageSize}
        startingPage={table.page}
        totalCount={table.size()}
        noDataText={d("ui.common.noData")}
        isControlledOutside={true}
        onChangePage={onChangePage}
        handleSort={handleSetSort}
        sort={sort}
        rowKeyAccessor="id"
        onRowClick={(configurationSeries: LabelConfiguration) => {
          setSelectedLabel(configurationSeries);
          setModalState(ModalState.EDIT);
        }}
      />
    </PageBodyTemplate>
  );
};

function getColumns(d: Dictionary) {
  return [
    {
      header: "",
      key: "hasErrors",
      options: {
        width: "var(--spacing-000)",
        paddingLess: true
      },
      formatter: (value: LabelConfiguration) => {
        return value.hasErrors ? (
          <Inline className="series-list-status-line-container">
            <StatusLine
              variant={StatusLineVariant.DANGER}
              className="series-list-status-line"
            />
          </Inline>
        ) : (
          <></>
        );
      }
    },
    {
      header: d("ui.common.name"),
      key: "name",
      canSort: true,
      formatter: (label: LabelConfiguration) => (
        <Inline alignment="left" gap="100">
          <Pill
            label={label.name}
            {...(label.color
              ? labelColorToPill[label.color]
              : {
                  variant: PillVariant.NEUTRAL
                })}
          />
        </Inline>
      )
    },
    {
      header: d("ui.common.description"),
      key: "description",
      options: {
        color: ColorText.SECONDARY,
        size: TextSize.S
      }
    },
    // TODO: make formatted date
    {
      header: d("ui.common.lastModified"),
      key: "metadata.updatedAt",
      canSort: true,
      options: {
        alignment: TextAlignment.RIGHT,
        color: ColorText.TERTIARY,
        size: TextSize.S
      },
      formatter: (data: LabelConfiguration) => (
        <Inline alignment="left" gap="100">
          {data.metadata && (
            <Tooltip content={data.metadata.updatedAt} delay={500}>
              <Text size="s" color="text-tertiary">
                {formatDate(data.metadata.updatedAt)}
              </Text>
            </Tooltip>
          )}
        </Inline>
      )
    }
  ];
}
