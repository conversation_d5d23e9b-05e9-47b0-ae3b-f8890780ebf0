import { createContext, useContext } from "react";

import { ConfigurationFormMode } from "@src/types/FormConfiguration.ts";

export type ConfigurationFormPageSettings = {
  isRenaming?: boolean;
  isUpdating?: boolean;
  isDuplicating?: boolean;
  isDeleting?: boolean;
};

export type ConfigurationFormContextType = {
  questionPath: string;
  updateQuestionHash: (path: string) => void;
  mode: ConfigurationFormMode;
  settings?: ConfigurationFormPageSettings;
  handleUpdateSetting?: (key: string, value: boolean) => void;
};

export const ConfigurationFormContext =
  createContext<ConfigurationFormContextType>({
    questionPath: "none",
    updateQuestionHash: () => {},
    mode: ConfigurationFormMode.EDIT,
    settings: undefined,
    handleUpdateSetting: () => {}
  });

export const useConfigurationFormContext = () =>
  useContext(ConfigurationFormContext);
