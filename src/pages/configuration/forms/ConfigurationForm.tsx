import React, { useEffect, useMemo, useState } from "react";

import { updateText } from "@automerge/automerge/next";
import {
  Box,
  Floating,
  FloatingPosition,
  Form,
  Inline,
  Loading,
  Pill,
  PillVariant,
  Stack,
  useUrlHash
} from "@oneteam/onetheme";
import {
  generatePath,
  useNavigate,
  useOutletContext,
  useParams
} from "react-router-dom";

import { isMultiLevelQuestionPath } from "@helpers/configurationFormHelper.ts";

import { ConfigurationFormContent } from "@components/forms/configuration/ConfigurationFormContent.tsx";
import { ConfigurationFormContextPanel } from "@components/forms/configuration/ConfigurationFormContextPanel/ConfigurationFormContextPanel.tsx";
import { ConfigurationQuestionModal } from "@components/forms/configuration/question/ConfigurationQuestionModal/ConfigurationQuestionModal.tsx";
import { WhiteboardPageBodyTemplate } from "@components/shared/WhiteboardPageBodyTemplate/WhiteboardPageBodyTemplate.tsx";

import { ConfigurationFormContext } from "@pages/configuration/forms/ConfigurationFormContext";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { routeConstants } from "@src/constants/routeConstants";
import { useConfigurationForm } from "@src/hooks/formConfiguration/useConfigurationForm.tsx";
import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs.tsx";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  buildConfigurationEntityPageTitle,
  usePageTitle
} from "@src/hooks/usePageTitle.js";
import { useQuestionHash } from "@src/hooks/useQuestionHash.tsx";
import {
  ConfigurationFormMode,
  Section,
  formConfigSchema
} from "@src/types/FormConfiguration.ts";
import { LabelColor, labelColorToPill } from "@src/types/Label";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace.ts";

import "./ConfigurationForm.scss";

export const ConfigurationForm = () => {
  const params = useParams();
  const navigate = useNavigate();
  const d = useDictionary();
  const { urlHashDetail, updateUrlHash } = useUrlHash();
  const { questionPath, updateQuestionHash } = useQuestionHash(
    updateUrlHash,
    urlHashDetail
  );

  const { document, docChange, workspace } = useOutletContext<{
    document: WorkspaceDocument;
    docChange: DocChange;
    workspace: Workspace;
  }>();

  const { configurationForm, path, configurationFormLabels } =
    useConfigurationForm();

  const [mode, setMode] = useState<ConfigurationFormMode>(
    ConfigurationFormMode.EDIT
  );

  useEffect(() => {
    const urlHashMode = urlHashDetail?.get("mode") as ConfigurationFormMode;
    if (!urlHashMode) {
      updateUrlHash("mode", ConfigurationFormMode.EDIT);
      setMode(ConfigurationFormMode.EDIT);
      return;
    }
    setMode(urlHashMode);
  }, [updateUrlHash, urlHashDetail]);

  const breadcrumbs = useBreadcrumbs({
    formConfiguration: {
      text: configurationForm?.name ?? ""
    }
  });

  usePageTitle(
    buildConfigurationEntityPageTitle({
      section: d("ui.configuration.forms.title"),
      entityName: configurationForm?.name ?? "",
      context: workspace?.key ?? ""
    }),
    !!workspace?.key && !!configurationForm?.name
  );

  const contextProviderValue = useMemo(
    () => ({
      questionPath,
      updateQuestionHash,
      mode
    }),
    [questionPath, updateQuestionHash, mode]
  );

  useEffect(() => {
    if (document.forms && configurationForm === undefined) {
      navigate(generatePath(routeConstants.configurationFormList, params));
    }
  }, [configurationForm, document.forms, navigate, params]);

  if (!configurationForm?.id) {
    return (
      <Box height="100" contentsHeight="fill">
        <Loading size={24} />
      </Box>
    );
  }

  return (
    <ConfigurationFormContext.Provider value={contextProviderValue}>
      <Form
        schema={formConfigSchema(d)}
        handleSubmit={data => data}
        d={d}
        hideFormButtons
        defaultValues={configurationForm}
      >
        <WhiteboardPageBodyTemplate
          className="configuration-form"
          name={
            mode === ConfigurationFormMode.EDIT ? (
              <Form.Renamable
                name="name"
                onChange={name =>
                  docChange(d => {
                    updateText(d, [...path, "name"], name);
                  })
                }
                value={configurationForm?.name}
              />
            ) : (
              configurationForm?.name
            )
          }
          headingFilters={
            <Inline gap="050">
              {configurationFormLabels?.map(label => (
                <Pill
                  key={label.id}
                  label={label.name}
                  {...labelColorToPill[
                    (label.color ?? LabelColor.COLOR_1) as LabelColor
                  ]}
                />
              )) ?? []}
            </Inline>
          }
          headingPills={
            <Inline height="100" padding="050" gap="050">
              <Pill
                variant={PillVariant.NEUTRAL}
                label={configurationForm?.key ?? ""}
              />
              <Pill
                variant={PillVariant.NEUTRAL}
                label={configurationForm?.foundationName ?? ""}
              />
              {configurationForm?.seriesName && (
                <Pill
                  variant={PillVariant.NEUTRAL}
                  label={configurationForm?.seriesName}
                />
              )}
            </Inline>
          }
          isRenamable={mode === ConfigurationFormMode.EDIT}
          breadcrumbs={breadcrumbs}
          floatingLayer={({ parentRef }) => (
            <>
              <Floating
                parentRef={parentRef}
                position={FloatingPosition.BOTTOM_LEFT}
              >
                <ConfigurationFormContextPanel
                  configurationForm={configurationForm}
                  path={path}
                  disabled={true}
                />
              </Floating>
              <Floating parentRef={parentRef} position={FloatingPosition.RIGHT}>
                <ConfigurationQuestionModal
                  mode={mode}
                  questionPath={questionPath}
                  isMultiLevelQuestion={isMultiLevelQuestionPath(questionPath)}
                  onClose={() => updateQuestionHash("none")}
                  onUp={
                    isMultiLevelQuestionPath(questionPath)
                      ? () =>
                          updateQuestionHash(
                            questionPath.split(".properties").shift() ?? "none"
                          )
                      : undefined
                  }
                />
              </Floating>
            </>
          )}
        >
          <Stack className="configuration-form__content" gap="100">
            <ConfigurationFormContent
              content={configurationForm.content as Section[]}
              path={[...path, "content"]}
              level={configurationForm.level}
            />
          </Stack>
        </WhiteboardPageBodyTemplate>
      </Form>
    </ConfigurationFormContext.Provider>
  );
};

ConfigurationForm.displayName = "ConfigurationForm";
