import React from "react";

import {
  Box,
  DropdownItem,
  DropdownItemGroup,
  DropdownMenu,
  Icon,
  Text,
  WhiteboardTool,
  WhiteboardToolVariant,
  WhiteboardToolbar
} from "@oneteam/onetheme";
import { generatePath, useNavigate, useParams } from "react-router-dom";
import useOnClickOutside from "use-onclickoutside";

import { commonIcons } from "@src/constants/iconConstants";
import { routeConstants } from "@src/constants/routeConstants";
import { Dictionary } from "@src/hooks/useDictionary";

import { useConfigurationFormContext } from "../ConfigurationFormContext";

export const ConfigurationFormToolbar = ({ d }: { d: Dictionary }) => {
  const params = useParams();
  const navigate = useNavigate();
  const [openItem, setOpenItem] = React.useState<string | undefined>(undefined);
  const ref = React.useRef(null);
  const { settings, handleUpdateSetting } = useConfigurationFormContext();

  useOnClickOutside(ref, () => {
    setOpenItem(undefined);
  });
  return (
    <Box ref={ref}>
      <WhiteboardToolbar
        style={{
          padding: "var(--spacing-050, 4px)",
          gap: "var(--components-whiteboard-toolbar-gap, 8px)"
        }}
      >
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="File"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "file" ? undefined : "file"
                );
              }}
            />
          )}
          isOpen={openItem === "file"}
        >
          <DropdownItemGroup>
            <DropdownItem
              leftElement={<Icon name="arrow_back" />}
              onClick={() => {
                navigate(
                  generatePath(routeConstants.configurationFormList, params)
                );
                setOpenItem(undefined);
              }}
            >
              Back to forms
            </DropdownItem>
          </DropdownItemGroup>
          <DropdownItemGroup hasDivider>
            <DropdownItem leftElement={<Icon name="help" />} onClick={() => {}}>
              {d("ui.common.help")}
            </DropdownItem>
          </DropdownItemGroup>
        </DropdownMenu>
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="Edit"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "edit" ? undefined : "edit"
                );
              }}
            />
          )}
          isOpen={openItem === "edit"}
        >
          {handleUpdateSetting && (
            <>
              <DropdownItemGroup>
                <DropdownItem
                  leftElement={<Icon {...commonIcons.rename} />}
                  onClick={() => {
                    if (settings?.isRenaming) {
                      handleUpdateSetting("isRenaming", false);
                      setTimeout(() => {
                        handleUpdateSetting("isRenaming", true);
                      });
                    } else {
                      handleUpdateSetting("isRenaming", true);
                    }
                    setOpenItem(undefined);
                  }}
                >
                  {d("ui.common.rename")}
                </DropdownItem>
                <DropdownItem
                  leftElement={<Icon {...commonIcons.update} />}
                  onClick={() => {
                    handleUpdateSetting("isUpdating", true);
                    setOpenItem(undefined);
                  }}
                >
                  {d("ui.common.update")}
                </DropdownItem>
              </DropdownItemGroup>
              <DropdownItemGroup hasDivider>
                <DropdownItem
                  leftElement={<Icon {...commonIcons.duplicate} />}
                  onClick={() => {
                    handleUpdateSetting("isDuplicating", true);
                    setOpenItem(undefined);
                  }}
                >
                  {d("ui.common.duplicate")}
                </DropdownItem>
              </DropdownItemGroup>
              <DropdownItemGroup hasDivider>
                <DropdownItem
                  leftElement={
                    <Icon {...commonIcons.delete} color="traffic-danger" />
                  }
                  onClick={() => {
                    handleUpdateSetting("isDeleting", true);
                    setOpenItem(undefined);
                  }}
                >
                  <Text color="traffic-onDanger"> {d("ui.common.delete")}</Text>
                </DropdownItem>
              </DropdownItemGroup>
            </>
          )}
        </DropdownMenu>
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="View"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "view" ? undefined : "view"
                );
              }}
            />
          )}
          isOpen={openItem === "view"}
          width="maxContent"
        ></DropdownMenu>
      </WhiteboardToolbar>
    </Box>
  );
};
