import React from "react";

import { Doc } from "@automerge/automerge-repo";
import { ConfirmationModal, ModalDialog, Overlay } from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { useWorkspaceVersion } from "@pages/collection/home/<USER>";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";
import { Workspace } from "@src/types/workspace";

interface DeleteConfigurationFormModalProps {
  configurationFormId: string;
  handleClose: () => void;
  callbackAfterDelete?: () => void;
}

export const DeleteConfigurationFormModal = ({
  configurationFormId,
  handleClose,
  callbackAfterDelete
}: DeleteConfigurationFormModalProps) => {
  const d = useDictionary();
  const { document, docChange, workspace } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
    workspace: Workspace;
  }>();
  const { lastPublishedVersion } = useWorkspaceVersion(workspace);

  // Get published status of the form
  if (lastPublishedVersion?.configuration.forms[configurationFormId]) {
    return (
      <ModalDialog isOpen>
        <Overlay isOpen />
        <ConfirmationModal
          closeOnClickOutside
          variant="info"
          heading={d("ui.configuration.forms.deleteModal.published.title")}
          message={d("ui.configuration.forms.deleteModal.published.message", {
            name: document.forms[configurationFormId]?.name
          })}
          onConfirm={() => handleClose()}
          confirmLabel={d(
            "ui.configuration.forms.deleteModal.published.confirmLabel"
          )}
          onCancel={() => handleClose()}
          hideCancelButton
        />
      </ModalDialog>
    );
  }

  return (
    <ModalDialog isOpen>
      <Overlay isOpen />
      <ConfirmationModal
        closeOnClickOutside
        variant="danger"
        heading={d("ui.configuration.forms.deleteModal.title", {
          name: document.forms[configurationFormId]?.name
        })}
        message={d("ui.configuration.forms.deleteModal.message")}
        onConfirm={() => {
          docChange(d => {
            delete d.forms[configurationFormId];
          });
          handleClose();
          callbackAfterDelete?.();
        }}
        confirmLabel={d("ui.configuration.forms.deleteModal.confirmLabel")}
        onCancel={() => handleClose()}
      />
    </ModalDialog>
  );
};

DeleteConfigurationFormModal.displayName = "DeleteConfigurationFormModal";
