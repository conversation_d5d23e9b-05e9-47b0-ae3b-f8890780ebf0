import React, { useCallback } from "react";

import { useOutletContext } from "react-router-dom";

import { DocChange } from "@pages/workspace/WorkspaceLayout";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { ConfigurationFormType } from "@src/types/FormConfiguration";

import { ConfigurationFormModal } from "../ConfigurationFormModal";
import "../ConfigurationFormModal.scss";

interface UpdateConfigurationFormModalProps {
  configurationForm: ConfigurationFormType;
  handleClose: () => void;
  disabled?: boolean;
}

export const UpdateConfigurationFormModal = ({
  configurationForm,
  handleClose,
  disabled
}: UpdateConfigurationFormModalProps) => {
  const d = useDictionary();
  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();

  const handleUpdateForm = useCallback(
    (data: ConfigurationFormType) => {
      if (!configurationForm) {
        return;
      }
      docChange(d => {
        d.forms[configurationForm.id].name = data.name;
        d.forms[configurationForm.id].description = data.description;
        d.forms[configurationForm.id].labels = data.labels ?? [];
      });

      handleClose();
    },
    [configurationForm, docChange, handleClose]
  );

  return (
    <ConfigurationFormModal
      isOpen
      handleClose={handleClose}
      heading={d("ui.configuration.forms.updateModal.title", {
        name: configurationForm?.name || ""
      })}
      defaultValues={configurationForm}
      handleSubmit={handleUpdateForm}
      disabled={disabled}
      updatingFormId={configurationForm?.id}
    />
  );
};

UpdateConfigurationFormModal.displayName = "UpdateConfigurationFormModal";
