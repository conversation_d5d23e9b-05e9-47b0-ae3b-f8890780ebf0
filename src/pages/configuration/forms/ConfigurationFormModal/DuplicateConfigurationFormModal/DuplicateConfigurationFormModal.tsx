import React, { useCallback } from "react";

import { useOutletContext } from "react-router-dom";

import { customNanoId } from "@helpers/customNanoIdHelper";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { ConfigurationFormType } from "@src/types/FormConfiguration";

import { ConfigurationFormModal } from "../ConfigurationFormModal";
import "../ConfigurationFormModal.scss";

interface DuplicateConfigurationFormModalProps {
  configurationFormToDuplicate: ConfigurationFormType;
  handleClose: () => void;
  callbackAfterDuplicate?: (
    newFormConfiguration: ConfigurationFormType
  ) => void;
  disabled?: boolean;
}

export const DuplicateConfigurationFormModal = ({
  configurationFormToDuplicate,
  handleClose,
  callbackAfterDuplicate,
  disabled
}: DuplicateConfigurationFormModalProps) => {
  const d = useDictionary();
  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();

  const handleDuplicateForm = useCallback(
    (data: ConfigurationFormType) => {
      docChange(d => {
        const newFormId = customNanoId();
        const newForm = {
          ...JSON.parse(JSON.stringify(configurationFormToDuplicate)),
          ...data,
          id: newFormId
        };
        d.forms[newFormId] = newForm;
        callbackAfterDuplicate?.(d.forms[newFormId]);
        handleClose();
      });
    },
    [
      callbackAfterDuplicate,
      configurationFormToDuplicate,
      docChange,
      handleClose
    ]
  );

  return (
    <ConfigurationFormModal
      handleClose={handleClose}
      heading={d("ui.configuration.forms.duplicateModal.title", {
        name: configurationFormToDuplicate.name
      })}
      defaultValues={{
        ...configurationFormToDuplicate,
        name: d("ui.configuration.flows.duplicateName", {
          name: configurationFormToDuplicate.name
        }),
        key: "" // Auto-generated based on name
      }}
      handleSubmit={handleDuplicateForm}
      disabled={disabled}
      isOpen
    />
  );
};

DuplicateConfigurationFormModal.displayName = "DuplicateConfigurationFormModal";
