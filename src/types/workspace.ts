import { z } from "zod";

import {
  MAX_NAME_LENGTH,
  MIN_NAME_LENGTH,
  commonErrors
} from "@src/constants/errorMessages.ts";
import { Dictionary } from "@src/hooks/useDictionary.tsx";

import { EntityMetadata } from "./EntityMetadata";
import { keyBeforeCreateSchema } from "./Key";

export type WorkspaceForCreateOrUpdate = {
  name: string;
  key: string;
  description?: string;
};

export type Workspace = {
  id: number;
  name: string;
  key: string;
  description?: string;
  documentId?: string;
  metadata: EntityMetadata;
};

export const getWorkspaceSchema = (d: Dictionary) =>
  z.object({
    name: z
      .string()
      .trim()
      .min(2, {
        message: d(commonErrors.length, {
          name: d("ui.common.name"),
          min: MIN_NAME_LENGTH,
          max: MAX_NAME_LENGTH
        })
      })
      .max(100, {
        message: d(commonErrors.length, {
          name: d("ui.common.name"),
          min: MIN_NAME_LENGTH,
          max: MAX_NAME_LENGTH
        })
      }),
    description: z
      .string()
      .trim()
      .max(500, {
        message: d(commonErrors.maxLength, {
          name: d("ui.common.description"),
          length: 500
        })
      })
      .optional(),
    // TODO: remaining validation done on submit
    key: keyBeforeCreateSchema(d)
  });
