import { Condition } from "../Condition";
import { FlowConfiguration } from "../FlowConfiguration";
import { FlowStepTypeConfigurationPrimaryIdentifier } from "../FlowStepTypeConfiguration/FlowStepTypeConfiguration";
import { Variable, VariableIdentifier, VariableValue } from "../Variables";
import { NextFlowStepId } from "./FlowStep";

export type CommonTypedStepProperties = {
  typePrimaryIdentifier: FlowStepTypeConfigurationPrimaryIdentifier; // matches a FlowStepTypeConfiguration
  inputs: { [identifier: VariableIdentifier]: VariableValue }; // answers to the FlowStepTypeConfiguration.properties.configuration.content questions
};

export type TriggerStepProperties = CommonTypedStepProperties;

export type ActionStepProperties = CommonTypedStepProperties;

export type ConditionStepBranch = {
  name: string;
  condition: Condition;
  next: NextFlowStepId | null;
};
export type ConditionStepProperties = {
  branches: Array<ConditionStepBranch>;
};

export type SetVariablesStepProperties = {
  variables: Variable[];
};

export enum IteratorStepType {
  FOR_EACH = "iteratorForEach",
  FILTER = "iteratorFilter",
  AGGREGATE = "iteratorAggregate"
}

export const triggeredByAnotherFlowStepType = "triggerFromAnotherFlow";
const manualTriggerStepTypes = [
  "manualTriggerFromFoundation",
  "manualTriggerFromForm"
];

export const triggerStepTypesCallableFromAnotherFlow = [
  triggeredByAnotherFlowStepType,
  ...manualTriggerStepTypes
];

export type IteratorStepFlowConfiguration = Omit<
  FlowConfiguration,
  "id" | "name" | "description" | "triggers" | "metadata"
>;

export type IteratorStepProperties = CommonTypedStepProperties & {
  configuration: IteratorStepFlowConfiguration;
};

export type FlowVariantStepProperties = {
  inputs: {
    flowConfigurationId?: string;
  } & { [identifier: VariableIdentifier]: VariableValue };
};
