import { z } from "zod";

import {
  MAX_NAME_LENGTH,
  MIN_NAME_LENGTH,
  commonErrors
} from "@src/constants/errorMessages.ts";
import { Dictionary } from "@src/hooks/useDictionary.tsx";

import { EntityMetadata, entityMetadataSchema } from "./EntityMetadata.ts";
import { keyBeforeCreateSchema } from "./Key.ts";
import { Question, getQuestionSchema } from "./Question.ts";

export enum ConfigurationFormMode {
  VIEW = "view",
  EDIT = "edit",
  CONDITIONAL_LOGIC = "conditional-logic"
}

export enum FormBuilderMode {
  VIEW = "view",
  EDIT = "edit",
  CONDITIONAL_LOGIC = "conditional-logic"
}

const baseSectionSchema = (d: Dictionary) =>
  z.object({
    id: z.string(),
    name: z
      .string()
      .trim()
      .min(2, {
        message: d(commonErrors.length, {
          name: d("ui.common.name"),
          min: MIN_NAME_LENGTH,
          max: MAX_NAME_LENGTH
        })
      })
      .max(100, {
        message: d(commonErrors.length, {
          name: d("ui.common.name"),
          min: MIN_NAME_LENGTH,
          max: MAX_NAME_LENGTH
        })
      }),
    level: z.number().optional(), // Required for form collection
    description: z.string().optional()
  });

export type Section = z.infer<ReturnType<typeof baseSectionSchema>> & {
  content: Section[] | Question[];
};

export const sectionSchema: (d: Dictionary) => z.ZodType<Section> = (
  d: Dictionary
) =>
  baseSectionSchema(d).extend({
    content: z.lazy(() =>
      z.union([z.array(sectionSchema(d)), z.array(getQuestionSchema())])
    )
  });

export type ConfigurationFormType = Section & {
  key: string;
  level: 0; // is root form
  labels?: string[];
  foundationId: string;
  seriesId?: string;
  allowMultiple?: boolean;
  // logic?: FormLogic;// - TODO conditional logic
  status?: string;
  metadata?: EntityMetadata;
  hasErrors?: boolean;
  foundationName?: string;
  seriesName?: string;
};

export type ConfigurationFormUnsaved = Omit<ConfigurationFormType, "id">;

export const formConfigSchema: (
  d: Dictionary,
  existingKeys?: string[]
) => z.ZodType<ConfigurationFormUnsaved> = (
  d: Dictionary,
  existingKeys?: string[] // optional because we can't edit form keys inside FormConfig whiteboard (ConfigurationForm.tsx)
) =>
  baseSectionSchema(d).extend({
    id: z.string().optional(),
    key: keyBeforeCreateSchema(d, existingKeys),
    level: z.literal(0),
    foundationId: z
      .string({
        message: d(`errors.common.required`, {
          name: d("ui.terminology.foundationConfiguration")
        })
      })
      .trim()
      .min(
        1,
        d(`errors.common.required`, {
          name: d("ui.terminology.foundationConfiguration")
        })
      ),
    labels: z.array(z.string()).optional(),
    seriesId: z.string().optional(),
    status: z.string().optional(),
    content: z.lazy(() =>
      z.union([z.array(sectionSchema(d)), z.array(getQuestionSchema())])
    ),
    metadata: entityMetadataSchema.optional()
  });

export enum ConfigurationFormDropzoneTag {
  SECTION = "section", // For sections
  SUBSECTION = "subSection", // For subsections
  REGULAR = "regular", // For regular questions
  MULTILEVEL = "multilevel" // For table/json related questions
}
