import { z } from "zod";

import {
  MAX_KEY_LENGTH,
  MIN_KEY_LENGTH,
  commonErrors
} from "@src/constants/errorMessages.ts";
import { Dictionary } from "@src/hooks/useDictionary.tsx";

export const keyBeforeCreateSchema = (d: Dictionary, existingKeys?: string[]) =>
  z
    .string()
    .trim()
    .refine(key => !existingKeys?.includes(key), {
      message: d(commonErrors.duplicate, {
        name: d("ui.common.key")
      })
    });

export const keySchema = (d: Dictionary, existingKeys?: string[]) =>
  z
    .string()
    .trim()
    .min(MIN_KEY_LENGTH, {
      message: d(commonErrors.length, {
        name: d("ui.common.key"),
        min: MIN_KEY_LENGTH,
        max: MAX_KEY_LENGTH
      })
    })
    .max(MAX_KEY_LENGTH, {
      message: d(commonErrors.length, {
        name: d("ui.common.key"),
        min: <PERSON><PERSON>_KEY_LENGTH,
        max: MAX_KEY_LENGTH
      })
    })
    .regex(/^[a-zA-Z0-9]*$/, {
      message: d(commonErrors.alphanumeric, {
        name: d("ui.common.key"),
        constraint: d("errors.common.constraint")
      })
    })
    .refine(key => !existingKeys?.includes(key), {
      message: d(commonErrors.duplicate, {
        name: d("ui.common.key")
      })
    });
