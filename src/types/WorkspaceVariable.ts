import { z } from "zod";

import {
  MAX_NAME_LENGTH,
  MIN_NAME_LENGTH,
  commonErrors
} from "@src/constants/errorMessages";
import { Dictionary } from "@src/hooks/useDictionary";

import { EntityMetadata } from "./EntityMetadata";

// this is the model we mostly interact with - it's a merge of config model and API model
export type WorkspaceVariable = {
  id: string;
  name: string;
  description?: string;
  isSecured: boolean;
  securedRef?: WorkspaceSecuredValueFromApi; // undefined means no reference exists
  value?: string; // undefined when secured for security reasons
  hasErrors?: boolean;
  metadata: EntityMetadata;
};

export type WorkspaceVariableConfiguration = {
  id: string;
  name: string;
  description?: string;
  value?: string; // undefined when secured
  securedRef?: string; // undefined means a value has not been set yet. User needs to set a value
  isSecured: boolean;
  hasErrors?: boolean;
  metadata: EntityMetadata;
};

export type WorkspaceSecuredValueFromApi = {
  id: number;
  ref: string;
};

const commonVariableSchema = (d: Dictionary) => ({
  name: z
    .string()
    .trim()
    .toUpperCase()
    .min(2, {
      message: d(commonErrors.length, {
        name: d("ui.common.name"),
        min: MIN_NAME_LENGTH,
        max: MAX_NAME_LENGTH
      })
    })
    .max(100, {
      message: d(commonErrors.length, {
        name: d("ui.common.name"),
        min: MIN_NAME_LENGTH,
        max: MAX_NAME_LENGTH
      })
    })
    .regex(/^[A-Z][A-Z_]*$/, {
      message: d(commonErrors.alphanumeric, {
        name: d("ui.common.name"),
        constraint: d("errors.common.constraint")
      })
    }),
  description: z
    .string()
    .trim()
    .max(500, {
      message: d(commonErrors.maxLength, {
        name: d("ui.common.description"),
        length: 500
      })
    })
    .optional(),
  value: z.string(),
  isSecured: z.boolean().default(false)
});

export const getWorkspaceVariableForCreateSchema = (
  d: Dictionary,
  hasNoDuplicates: (name: string) => boolean
) => {
  const common = commonVariableSchema(d);

  return z.object({
    name: common.name.refine(hasNoDuplicates, {
      message: d(commonErrors.duplicate, {
        name: d("ui.common.name")
      })
    }),
    description: common.description,
    value: common.value.min(1, {
      message: d(commonErrors.required, {
        name: d("ui.common.value")
      })
    }),
    isSecured: common.isSecured
  });
};

export type WorkspaceVariableForCreate = z.infer<
  ReturnType<typeof getWorkspaceVariableForCreateSchema>
>;

export const getWorkspaceVariableForUpdateSchema = (
  d: Dictionary,
  hasNoDuplicates: (name: string) => boolean,
  valueRequired: boolean
) => {
  const common = commonVariableSchema(d);

  return z.object({
    name: common.name.refine(hasNoDuplicates, {
      message: d(commonErrors.duplicate, {
        name: d("ui.common.name")
      })
    }),
    description: common.description,
    value: valueRequired
      ? common.value.min(1, {
          message: d(commonErrors.required, {
            name: d("ui.common.value")
          })
        })
      : common.value.optional()
  });
};

export type WorkspaceVariableForUpdateForm = z.infer<
  ReturnType<typeof getWorkspaceVariableForUpdateSchema>
>;

export type WorkspaceVariableForApiUpdate = WorkspaceVariableForUpdateForm & {
  id: string;
  wasSecured: boolean;

  // reference XOR value: Either reference or value must be set, never both
  reference?: WorkspaceSecuredValueFromApi;
  value?: string;
};
