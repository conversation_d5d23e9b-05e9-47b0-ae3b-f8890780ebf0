import { z } from "zod";

import { Dictionary } from "@src/hooks/useDictionary.tsx";

import { entityMetadataSchema } from "./EntityMetadata";
import { keyBeforeCreateSchema } from "./Key";

export const newFoundationSchema = (d: Dictionary, existingKeys?: string[]) =>
  z.object({
    name: z.string().trim().min(1),
    key: keyBeforeCreateSchema(d, existingKeys),
    workspaceId: z.number(),
    parentId: z.number().optional().nullable(),
    foundationConfigurationId: z.string()
  });

export const foundationSchema = (d: Dictionary, existingKeys?: string[]) =>
  z.intersection(
    newFoundationSchema(d, existingKeys),
    z.object({
      id: z.number(),
      metadata: entityMetadataSchema.optional(),
      properties: z.record(z.any()).optional()
    })
  );

export type NewFoundation = z.infer<ReturnType<typeof newFoundationSchema>>;

export type Foundation = z.infer<ReturnType<typeof foundationSchema>>;

export class FoundationForUpdate implements Foundation {
  constructor(
    public id: number,
    public name: string,
    public key: string,
    public workspaceId: number,
    public foundationConfigurationId: string,
    public parentId?: number | undefined | null
  ) {}

  static fromFoundation(foundation: Foundation) {
    return new FoundationForUpdate(
      foundation.id,
      foundation.name,
      foundation.key,
      foundation.workspaceId,
      foundation.foundationConfigurationId,
      foundation.parentId
    );
  }
}

export class FoundationForCreate implements NewFoundation {
  constructor(
    public name: string,
    public key: string,
    public workspaceId: number,
    public foundationConfigurationId: string,
    public parentId?: number | undefined | null
  ) {}

  static fromFoundation(foundation: Foundation) {
    return new FoundationForCreate(
      foundation.name,
      foundation.key,
      foundation.workspaceId,
      foundation.foundationConfigurationId,
      foundation.parentId
    );
  }
}
