import { ConfigurationFormType } from "@src/types/FormConfiguration.ts";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";
import { SeriesConfig } from "@src/types/Series.ts";
import { Workspace } from "@src/types/workspace.ts";

import { Annotation } from "./Annotation";
import { FlowConfiguration } from "./FlowConfiguration/FlowConfiguration";
import { LabelConfiguration } from "./Label";
import { WorkspaceVariableConfiguration } from "./WorkspaceVariable";
import { FormAnswer } from "./collection/CollectionForm";

type Metadata = {
  createdAt: string;
  updatedAt: string;
};

export type DocError = {
  key: string;
  type: string;
  path: string;
  constraintDetail: string;
  message?: string;
};

export type Resource<T> = {
  entities: { [id: string]: T };
  order: string[];
};

export type WorkspaceDocument = Workspace & {
  metadata: Metadata;
  forms: { [formId: string]: ConfigurationFormType };
  foundations: Resource<FoundationConfiguration>;
  series: { [seriesId: string]: SeriesConfig };
  labels: { [labelId: string]: LabelConfiguration };
  variables: { [variableId: string]: WorkspaceVariableConfiguration };
  flows: Resource<FlowConfiguration>;
  errors?: DocError[];
};

export type FormAnswerDocument = {
  id: string;
  answers: { [id: string]: FormAnswer };
};

export interface FormAnnotationDocumentState {
  sectionsCollapsed: { [sectionId: string]: boolean };
}

export interface FormAnnotationDocument {
  id: string;
  annotations: { [id: Annotation["id"]]: Annotation };
  state: FormAnnotationDocumentState;
}

export type AutomergeDocument = WorkspaceDocument | FormAnswerDocument;
