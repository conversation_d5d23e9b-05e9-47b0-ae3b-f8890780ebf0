{"name": "oneteam-ai-fe", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=22.12.0 <23", "npm": ">=10.9.2"}, "scripts": {"dev": "vite --host", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest watch", "test:ci": "vitest run --coverage", "postinstall": "npm outdated || :", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test-storybook": "test-storybook", "prepare": "husky"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.4.0", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.0.3", "@automerge/automerge-repo": "^2.0.8", "@automerge/automerge-repo-network-websocket": "^2.0.8", "@automerge/automerge-repo-react-hooks": "^2.0.8", "@automerge/automerge-repo-storage-indexeddb": "^2.0.8", "@dagrejs/dagre": "^1.1.4", "@hookform/resolvers": "^3.10.0", "@oneteam/onetheme": "bitbucket:devops-martinit/onetheme#main", "@tanstack/react-query": "^5.54.1", "@tanstack/react-query-devtools": "^5.59.16", "@vitest/coverage-v8": "^3.0.0", "@xyflow/react": "^12.3.6", "lodash": "^4.17.21", "nanoid": "^5.0.8", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-router-dom": "7.6.3", "react-zoom-pan-pinch": "^3.6.1", "use-onclickoutside": "^0.4.1", "vite-plugin-wasm": "^3.3.0", "zod": "^3.23.8"}, "devDependencies": {"@chromatic-com/storybook": "^3.0.0", "@eslint/js": "^9.9.1", "@storybook/addon-coverage": "^1.0.4", "@storybook/addon-essentials": "^8.2.9", "@storybook/addon-interactions": "^8.2.9", "@storybook/addon-links": "^8.2.9", "@storybook/blocks": "^8.2.9", "@storybook/react": "^8.2.9", "@storybook/react-vite": "^8.2.9", "@storybook/test": "^8.2.9", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.1.0", "@trivago/prettier-plugin-sort-imports": "^5.0.0", "@types/eslint__js": "^9.0.0", "@types/lodash": "^4.17.13", "@types/node": "^22.8.6", "@types/react": "^18.3.5", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.0", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.4.0", "@vitejs/plugin-react-swc": "^3.5.0", "commander": "^13.1.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-storybook": "^0.12.0", "googleapis": "^148.0.0", "husky": "^9.1.1", "jsdom": "^26.0.0", "lint-staged": "^15.2.10", "msw": "^2.7.0", "msw-storybook-addon": "^2.0.4", "prettier": "^3.3.3", "sass": "^1.78.0", "storybook": "^8.4.0", "typescript": "^5.5.4", "typescript-eslint": "^8.4.0", "vite": "^6.0.0", "vite-tsconfig-paths": "^5.0.1", "vitest": "^3.0.0", "zx": "^8.3.0"}, "lint-staged": {"*.{js,ts,tsx,jsx,json,html}": ["prettier --write"], "*.{js,ts,tsx,jsx,}": ["eslint --fix --max-warnings 0"]}, "msw": {"workerDirectory": ["public"]}}