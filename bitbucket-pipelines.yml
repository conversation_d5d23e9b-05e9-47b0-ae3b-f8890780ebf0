image: node:22.17.1-bookworm
options:
  max-time: 20
definitions:
  caches:
    sonar: ~/.sonar/cache # Caching SonarCloud artifacts will speed up your build
    ot-node:
      key:
        files:
          - package-lock.json
      path: node_modules
  services:
    docker-for-sonar-pipe:
      memory: 3072
      type: docker
  steps:
    - step: &stop-previous-pipeline
        name: Stop previous Pipeline
        script:
          - npm install -g npm@10.9.2
          - rm -rf package-lock.json # Removing package.json and package-lock.json because for this step only zx and axios are needed (improves speed of the step)
          - rm -rf package.json
          - npm i zx axios --no-save
          - ./scripts/stopPreviousPipeline.mjs
    - step: &ignore-test-and-analysis-failures
        name: Ignore failures from test and code analysis
        script:
          - touch .ot-meta-ignore-failures
        artifacts:
          - .ot-meta-ignore-failures
    - step: &run-tests
        name: Run tests
        caches:
          - ot-node
        script:
          - if [ -e .ot-meta-ignore-failures ]; then
          - set +e
          - fi
          - npm install -g npm@10.9.2
          - npm install
          - npm run test:ci
        artifacts:
          - coverage/**
    - step: &code-analysis
        name: SonarCloud Scan and Analysis
        clone:
          depth: full # SonarCloud scanner needs the full history to assign issues properly
        caches:
          - sonar
        services:
          - docker-for-sonar-pipe
        script:
          - echo "sonar will run in after-script"
        after-script:
          - pipe: sonarsource/sonarcloud-scan:4.1.0
          - pipe: sonarsource/sonarcloud-quality-gate:0.2.0
    - step: &generateSASToken
        name: generate SAS Token
        image: mcr.microsoft.com/azure-cli:cbl-mariner2.0
        script:
          - tdnf install -y nodejs
          - npm install -g zx
          - npm install -g npm@10.9.2
          - zx ./scripts/getSASToken.mjs > sas.txt
          - mkdir secret
          - mv sas.txt secret
        artifacts:
          - secret/**
    - step: &deploy
        name: deploy
        script:
          - export VERSION=$(cat version.txt)
          - mv dist $VERSION
          - export SAS_TOKEN=$(cat secret/sas.txt)
          - pipe: atlassian/azure-storage-deploy:2.5.1
            variables:
              SOURCE: "./$VERSION"
              DESTINATION_STORAGE_ACCOUNT_NAME: "https://otaishareddevfe.blob.core.windows.net"
              DESTINATION_CONTAINER_NAME: "static-assets"
              DESTINATION_BLOB_RELATIVE_PATH: "otai"
              DESTINATION_SAS_TOKEN: "${SAS_TOKEN}"
    - step: &updateFrontDoor
        name: Update FrontDoor config for version
        image: mcr.microsoft.com/azure-cli:cbl-mariner2.0
        script:
          - export VERSION=$(cat version.txt)
          - az login --service-principal
            --tenant ${AZURE_TENANT_ID}
            --username ${AZURE_APP_ID}
            --password ${AZURE_PASSWORD}
            --output table
          - az afd route update
            --subscription   oneteam_dev
            --resource-group oneteam_dev
            --profile-name   oneteam-dev-shared-frontdoor
            --endpoint-name  oneteam-dev-shared-frontdoor
            --route-name     otai-dev-demo-innovation-fe
            --origin-path    /static-assets/otai/${VERSION}
            --output table
          - az afd route update
            --subscription   oneteam_dev
            --resource-group oneteam_dev
            --profile-name   oneteam-dev-shared-frontdoor
            --endpoint-name  oneteam-dev-shared-frontdoor
            --route-name     otai-dev-ot-innovation-fe
            --origin-path    /static-assets/otai/${VERSION}
            --output table
    - step: &build
        name: build
        clone:
          depth: full
        caches:
          - ot-node
        script:
          - npm install -g npm@10.9.2
          - npm install
          - ./scripts/makeBuildJson.sh
          - npm run build
        artifacts:
          - dist/**
          - version.txt
pipelines:
  tags:
    "*.*.*":
      - step: *run-tests
      - step: *code-analysis
      - step: *build
      - step: *generateSASToken
      - step: *deploy
  branches:
    "main": # deploy to dev
      - parallel:
          - step: *build
          - step: *generateSASToken
      - step: *deploy
      - step: *updateFrontDoor
      - step: *ignore-test-and-analysis-failures
      - step: *run-tests
      - step: *code-analysis
  pull-requests:
    "**":
      - parallel:
          - step: *stop-previous-pipeline
          - step: *build
          - step: *run-tests
      - step: *code-analysis
  custom:
    owasp-dependency-check:
      - step:
          name: Run OWASP Dependency Check
          max-time: 40 # Allow more time for the dependency check
          image: node:22.17.1-bullseye
          script:
            - npm install -g npm@10.9.2
            - apt-get update && apt-get install -y openjdk-17-jdk # owasp-dependency-check requires java
            - export JAVA_HOME="/usr/lib/jvm/java-17-openjdk-amd64"
            - npm install -g zx owasp-dependency-check
            - npm install --include=dev # googleapis library is installed as dev dependency
            - mkdir dependency-check-bin
            - owasp-dependency-check --scan package-lock.json --nvdApiKey $NVD_API_KEY --suppression owaspSuppression.xml
            - ./scripts/uploadOwaspResults.mjs
