package services.oneteam.ai.flow.event

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.CreateFlowExecutionRequestBody.TriggerProperties
import services.oneteam.ai.flow.execution.payload.filter.JsonSchemaQuestionFilter
import services.oneteam.ai.flow.execution.step.ContextToJsonObjectBuilder
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.flow.expression.conditional.Condition
import services.oneteam.ai.flow.pubsub.MessageType
import services.oneteam.ai.flow.pubsub.PubSubService
import services.oneteam.ai.flow.pubsub.WebSocketMessage
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.EventKey
import services.oneteam.ai.shared.domains.event.EventListener
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfigurationStatusType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType.Properties.Configuration.TriggerEventSubscription.VariableMapping
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService

@Serializable
data class FlowsUsingASubscribedTriggerFlowStepType(
    val flowConfiguration: FlowConfiguration.ForJson, val triggerStepType: FlowStepTypeConfiguration
)

class TriggerFlowEventListener(
    private val workspaceVersionService: WorkspaceVersionService,
    private val flowStepTypeConfigurationService: FlowStepTypeConfigurationService,
    private val flowExecutionService: FlowExecutionService,
    private val runExecutionImmediately: Boolean,
    private val timeoutMins: Long
) : EventListener {

    private val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun onEvent(event: Event.ForJson) {
        val activeWorkspaceVersion = workspaceVersionService.getActiveWorkspaceVersion(event.workspaceId)
        if (activeWorkspaceVersion == null) {
            logger.info("No active workspace version found for workspace {}", event.workspaceId)
            return
        }

        // Find the StepTypes matching the TriggerEvent
        val flowConfigurations = getFlowConfigurations(activeWorkspaceVersion)
        val subscribedTriggerFlowStepTypeConfigurations = getSubscribedTriggerFlowStepTypeConfigurations(event)
        val flowsUsingSubscribedTriggerFlowStepType =
            getFlowsUsingSubscribedTriggerFlowStepType(flowConfigurations, subscribedTriggerFlowStepTypeConfigurations)

        flowsUsingSubscribedTriggerFlowStepType.forEachIndexed { index, (flowConfiguration, triggerStepType) ->
            val event = event.copy(shouldNotifyUser = index == 0) // only notify user for the first flow
            checkTriggerMatch(activeWorkspaceVersion, event, flowConfiguration, triggerStepType)
        }
    }

    internal suspend fun getSubscribedTriggerFlowStepTypeConfigurations(event: Event.ForJson): List<FlowStepTypeConfiguration> {
        val query = mapOf("type" to listOf("trigger"))
        val triggerFlowStepTypes = flowStepTypeConfigurationService.getAllByQuery(query)

        val subscribedTriggerFlowStepTypeConfigurations = triggerFlowStepTypes.filter { flowStepType ->
            val subscribedEvents: Set<EventKey>? = flowStepType.properties?.configuration?.subscribeTo?.keys
            subscribedEvents?.contains(event.eventProperties.key) == true
        }
        if (subscribedTriggerFlowStepTypeConfigurations.isEmpty()) {
            return emptyList()
        }
        return subscribedTriggerFlowStepTypeConfigurations
    }

    internal fun getFlowsUsingSubscribedTriggerFlowStepType(
        flowConfigurations: List<FlowConfiguration.ForJson>,
        subscribedTriggerFlowStepTypeConfigurations: List<FlowStepTypeConfiguration>
    ): List<FlowsUsingASubscribedTriggerFlowStepType> {
        return flowConfigurations.mapNotNull { flowConfiguration ->
            // Check all triggers in flow configuration
            flowConfiguration.triggers?.values?.forEach { trigger ->
                val typePrimaryIdentifier = trigger.properties.typePrimaryIdentifier
                val triggerStepType = subscribedTriggerFlowStepTypeConfigurations.find { flowStepType ->
                    flowStepType.primaryIdentifier == typePrimaryIdentifier
                }

                if (triggerStepType != null) {
                    return@mapNotNull FlowsUsingASubscribedTriggerFlowStepType(
                        flowConfiguration, triggerStepType
                    )
                }
            }
            null
        }
    }

    private fun getFlowConfigurations(workspaceVersion: WorkspaceVersion): List<FlowConfiguration.ForJson> {
        return workspaceVersion.configuration.flows.entities.values.toList()
            .filter { !it.start?.value.isNullOrEmpty() && it.status != FlowConfigurationStatusType.INACTIVE }
    }

    internal suspend fun checkTriggerMatch(
        activeWorkspaceVersion: WorkspaceVersion.ForApi,
        event: Event.ForJson,
        flowConfiguration: FlowConfiguration.ForJson,
        triggerStepType: FlowStepTypeConfiguration,
    ) {
        logger.info(
            "Checking Matched Trigger for event: {} in flow: {}",
            event.eventProperties.key,
            flowConfiguration.name
        )
        val matchingTriggerSteps = flowConfiguration.triggers?.values?.filter { step ->
            step.properties.typePrimaryIdentifier == triggerStepType.primaryIdentifier
        } ?: emptyList()

        for (trigger in matchingTriggerSteps) {

            val subscribeTo = triggerStepType.properties?.configuration?.subscribeTo

            if (subscribeTo != null) {

                val globalVariables = GlobalVariables(
                    workspaceId = activeWorkspaceVersion.workspaceId,
                    workspaceVersionId = activeWorkspaceVersion.id,
                    tenantId = event.tenantId,
                    flowConfigurationId = flowConfiguration.id,
                    flowConfigurationName = flowConfiguration.name
                )
                val flowContextLocalStep = FlowContextWithLocalStep(
                    flowContext = FlowContext(
                        globalVariables, event = Event.ForApi(
                            event.workspaceId,
                            event.eventProperties,
                            event.id,
                            event.tenantId,
                        )
                    )
                )

                val triggerEventSubscription = subscribeTo[event.eventProperties.key]

                if (triggerEventSubscription != null) {

                    val executionTrigger = trigger.toExecution()
                    executionTrigger.condition =
                        Json.decodeFromJsonElement(Condition.serializer(), triggerEventSubscription.condition)
                    executionTrigger.variableMappings = triggerEventSubscription.variableMappings

                    val contextToJsonObjectBuilder = ContextToJsonObjectBuilder(emptyList(), trigger.id.value)

                    TriggerTemplatePopulator(flowContextLocalStep, contextToJsonObjectBuilder).populateTrigger(
                        executionTrigger,
                        triggerStepType.properties?.configuration!!
                    )

                    val isPutOnQueue = isMatchingCondition(executionTrigger.condition!!.toExpression())

                    if (isPutOnQueue) {

                        val filteredVariables = filterIncomingPayload(
                            executionTrigger.variableMappings!!
                        )

                        val flowExecutionId = triggerFlow(
                            trigger,
                            activeWorkspaceVersion.workspaceId,
                            activeWorkspaceVersion.id,
                            flowConfiguration,
                            event,
                            filteredVariables
                        )
                        PubSubService.sendToWorkspace(
                            activeWorkspaceVersion.workspaceId,
                            WebSocketMessage(MessageType.FlowRunnerUpdate)
                        )

                        if (runExecutionImmediately) {
                            runFlow(activeWorkspaceVersion.workspaceId, flowExecutionId)
                        }

                        return // Only need to run the first matching trigger for each flow configuration
                    }
                } else {
                    logger.info("Trigger event subscription not found for event key: {}", event.eventProperties.key)
                }
            } else {
                logger.info(
                    "No trigger event subscription found for trigger step type: {}",
                    triggerStepType.primaryIdentifier
                )
            }
        }
    }

    internal suspend fun isMatchingCondition(
        condition: String,
    ): Boolean {
        val result: Boolean = JsonataExpressionEvaluator().evaluate(condition, emptyMap<Any, Any>()) as Boolean
        return result
    }


    private suspend fun triggerFlow(
        step: FlowConfiguration.Trigger,
        workspaceId: Workspace.Id,
        workflowVersionId: WorkspaceVersion.Id,
        flowConfiguration: FlowConfiguration.ForJson,
        event: Event.ForJson,
        variables: List<VariableMapping>,
    ): FlowExecution.Id {
        logger.info("Triggering flow: {} / {}", flowConfiguration.id, flowConfiguration.name)

        val requestBodyPayload = CreateFlowExecutionRequestBody(
            flowConfigurationId = flowConfiguration.id,
            workspaceVersionId = workflowVersionId,
            event = Event.ForApi(
                event.workspaceId,
                event.eventProperties,
                event.id,
                event.tenantId,
                event.eventGroupId,
                event.shouldNotifyUser
            ),
            variables = variables,
            CreateFlowExecutionRequestBody.CreationMode.AVOID_DUPLICATION,
            settings = FlowExecution.Properties.Settings(timeoutMins),
            trigger = TriggerProperties(
                id = step.id,
                primaryIdentifier = step.properties.typePrimaryIdentifier!!
            ),
        )

        val flowResponse = flowExecutionService.createIfNoDuplicates(workspaceId, requestBodyPayload)
        if (flowResponse == null) {
            throw Exception("Failed to trigger flow")
        }

        logger.info("Flow triggered: {}", flowResponse)
        PubSubService.sendToWorkspace(workspaceId, WebSocketMessage(MessageType.FlowRunnerUpdate))

        return flowResponse.id
    }

    private suspend fun runFlow(
        workspaceId: Workspace.Id,
        flowExecutionId: FlowExecution.Id,
    ) {
        flowExecutionService.runFlowByExecutionId(
            flowExecutionId,
            workspaceId,
        )
        logger.trace("Flow execution started: {}", flowExecutionId)
    }

    private fun filterIncomingPayload(
        variables: List<VariableMapping>
    ): List<VariableMapping> {
        return variables.map { variable ->
            filterVariableWithExpectedSchema(variable) ?: variable
        }
    }

    private fun filterVariableWithExpectedSchema(
        variable: VariableMapping
    ): VariableMapping? {
        val expectedSchema = variable.properties?.get("schema") ?: return null

        val filteredPayload = JsonSchemaQuestionFilter.tryFilterPayload(
            expectedSchema = expectedSchema,
            incomingPayload = variable.value
        )
        return filteredPayload?.let { variable.copy(value = it) }
    }
}
