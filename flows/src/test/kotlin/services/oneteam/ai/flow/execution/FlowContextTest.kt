package services.oneteam.ai.flow.execution

import io.kotest.matchers.equals.shouldBeEqual
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion

class FlowContextTest {
    val global = GlobalVariables(
        workspaceId = Workspace.Id(1),
        workspaceVersionId = WorkspaceVersion.Id(1),
        tenantId = 1,
        flowConfigurationId = FlowConfiguration.Id("1"),
        flowConfigurationName = FlowConfiguration.Name("flow")
    )

    val workspace = WorkspaceContext(
        documentId = Workspace.DocumentId("1"),
        id = Workspace.Id(1),
        key = Workspace.Key("WORKSPACE1"),
        name = Workspace.Name("workspace1"),
        workspaceFoundationId = Foundation.Id(1),
        variables = emptyMap()
    )

    val event = Event.ForApi(
        Workspace.Id(1),
        StartFlowManuallyFromFormProperties(
            buttonLabel = "string",
            form = null,
            userId = 1,
        ),
        Event.Id("1"),
        1
    )

    @Test
    fun `should serialize context`() {
        val context = FlowContext(
            global = global,
            variables = mutableMapOf(
                "key" to Variable(
                    JsonPrimitive("value"),
                    "string",
                    "key",
                    null
                )
            ),
            workspace = workspace,
            event = event
        )
        val serialized = Json.encodeToString(context)
        assertEquals(
            """
                {"global":{"workspaceId":1,"workspaceVersionId":1,"tenantId":1,"flowConfigurationId":"1","flowConfigurationName":"flow"},"workspace":{"documentId":"1","id":1,"key":"WORKSPACE1","name":"workspace1","workspaceFoundationId":1},"variables":{"key":{"value":"value","type":"string","identifier":"key"}},"event":{"workspaceId":1,"eventProperties":{"buttonLabel":"string","form":null,"userId":1,"key":"START_flow_manually_from_form"},"id":"1","tenantId":1}}
                """.trimIndent(),
            serialized
        )
    }

    @Test
    fun `should serialize context with list`() {
        val context = FlowContext(
            global = global,
            variables = mutableMapOf(
                "key" to Variable(
                    JsonArray(listOf(JsonPrimitive("value"))),
                    "string",
                    "key",
                    null
                )
            ),
            workspace = workspace,
            event = event

        )

        val serialized = Json.encodeToString(context)
        println(serialized)
        assertEquals(
            """
                {"global":{"workspaceId":1,"workspaceVersionId":1,"tenantId":1,"flowConfigurationId":"1","flowConfigurationName":"flow"},"workspace":{"documentId":"1","id":1,"key":"WORKSPACE1","name":"workspace1","workspaceFoundationId":1},"variables":{"key":{"value":["value"],"type":"string","identifier":"key"}},"event":{"workspaceId":1,"eventProperties":{"buttonLabel":"string","form":null,"userId":1,"key":"START_flow_manually_from_form"},"id":"1","tenantId":1}}
            """.trimIndent(),
            serialized
        )
    }

    @Test
    fun `should deserialize context`() {
        val serialized =
            """{"global":{"workspaceId":1,"workspaceVersionId":1,"tenantId":1,"flowConfigurationId":"1","flowConfigurationName":"flow"},"workspace":{"documentId":"1", "id":1, "key":"WORKSPACE1","name":"workspace1","workspaceFoundationId":1,"variables": {}},"variables":{"key":{"value":"value","type":"string","identifier":"key","properties":null}},"event":{"workspaceId":1,"eventProperties":{"key":"START_flow_manually_from_form","buttonLabel":"string","form":null,"userId":1},"id":"1","tenantId":1}}"""
        val context = Json.decodeFromString(FlowContext.serializer(), serialized)
        assertEquals(
            FlowContext(
                global = global,
                variables = mutableMapOf(
                    "key" to Variable(
                        JsonPrimitive("value"),
                        "string",
                        "key",
                        null
                    )
                ),
                workspace = workspace,
                event = event

            ),
            context
        )
    }

    @Test
    fun `should deserialize context with list`() {
        val serialized =
            """{"global":{"workspaceId":1,"workspaceVersionId":1,"tenantId":1,"flowConfigurationId":"1","flowConfigurationName":"flow"},"workspace":{"documentId":"1", "id":1, "key":"WORKSPACE1","name":"workspace1","workspaceFoundationId":1,"variables": {}},"variables":{"key":{"value":["value"],"type":"string","identifier":"key","properties":null}},"event":{"workspaceId":1,"eventProperties":{"key":"START_flow_manually_from_form","buttonLabel":"string","form":null,"userId":1},"id":"1","tenantId":1}}"""
        val context = Json.decodeFromString(FlowContext.serializer(), serialized)
        assertEquals(
            FlowContext(
                global = global,
                variables = mutableMapOf(
                    "key" to Variable(
                        JsonArray(listOf(JsonPrimitive("value"))),
                        "string",
                        "key",
                        null
                    )
                ),
                workspace = workspace,
                event = event

            ),
            context
        )
    }

    @Test
    fun `should be able to set value`() = runTest {
        val context = FlowContext(
            global = global,
            variables = mutableMapOf(
                "1" to Variable(
                    JsonPrimitive("z"),
                    "string",
                    "key",
                    null
                ),
                "2" to Variable(
                    JsonPrimitive(9),
                    "number",
                    "key",
                    null
                )

            ),
            workspace = workspace,
            event = event

        )

        context.set(Variable(JsonPrimitive("a"), "string", "1", null))
        context.set(Variable(JsonPrimitive(2), "number", "2", null))
        assertEquals(context.variables["1"]?.value, JsonPrimitive("a"))
        assertEquals(context.variables["2"]?.value, JsonPrimitive(2))
    }

    @Test
    fun `should map to object values`() = runTest {
        val context = FlowContext(
            global = global,
            variables = mutableMapOf(
                "form" to Variable(
                    value = JsonObject(mapOf("id" to JsonPrimitive(400))),
                    identifier = "form",
                    type = "object",
                )
            ),
            workspace = workspace,
            event = event
        )

        assertEquals(JsonObject(mapOf("id" to JsonPrimitive(400))), context.variables["form"]?.value)
    }

    @Test
    fun `should deserialize json variable declaration`() = runTest {
        val serialized =
            """
              {
                "type": "json",
                "identifier": "document",
                "value": {
                  "id": "y5WOPTzFLI",
                  "url": "myUrl",
                  "path": "/upload/1/29/tmp/rsvZJocVvk-a2nJ5zCDagR4jqH9A8yRp94hD/y5WOPTzFLI",
                  "name": "outputdoc.docx",
                  "extension": "docx"
                },
                "properties": {
                  "items": [
                    { "type": "text", "identifier": "url" },
                    { "type": "text", "identifier": "id" },
                    { "type": "text", "identifier": "name" },
                    { "type": "text", "identifier": "path" },
                    { "type": "text", "identifier": "extension" }
                  ]
                }
              }
            """.trimIndent()
        val vm: Variable = Json.decodeFromString(serialized)
        vm.type shouldBeEqual "json"
        vm.identifier shouldBeEqual "document"
        vm.value shouldBeEqual JsonObject(
            mapOf(
                "id" to JsonPrimitive("y5WOPTzFLI"),
                "url" to JsonPrimitive("myUrl"),
                "path" to JsonPrimitive("/upload/1/29/tmp/rsvZJocVvk-a2nJ5zCDagR4jqH9A8yRp94hD/y5WOPTzFLI"),
                "name" to JsonPrimitive("outputdoc.docx"),
                "extension" to JsonPrimitive("docx")
            )
        )
        vm.properties?.shouldBeEqual(
            VariableDefinition.JsonVariableProperties(
                items = listOf(
                    VariableDefinition.VariableConfiguration(
                        type = "text",
                        identifier = "url"
                    ),
                    VariableDefinition.VariableConfiguration(
                        type = "text",
                        identifier = "id"
                    ),
                    VariableDefinition.VariableConfiguration(
                        type = "text",
                        identifier = "name"
                    ),
                    VariableDefinition.VariableConfiguration(
                        type = "text",
                        identifier = "path"
                    ),
                    VariableDefinition.VariableConfiguration(
                        type = "text",
                        identifier = "extension"
                    )
                )
            )
        )
    }

}
