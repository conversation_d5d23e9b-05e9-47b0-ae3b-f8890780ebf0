package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.DefaultMapBuilder
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.VariableDefinition.Variable
import services.oneteam.ai.flow.execution.variables.DefaultVariableOperation
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SetVariablesExecutionStepTestV2 {

    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val contextToJsonObjectBuilder = ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "1")

    fun specProviderMany(): Stream<Spec> {
        return Stream.of(
            Spec(
                listOf(Variable.of(JsonPrimitive("{{numberVar}}"), "number", "output")),
                listOf(Variable.of(JsonPrimitive(Fixtures.Vars.numberVar.value.jsonPrimitive.int), "number", "output"))
            ), Spec(
                listOf(Variable.of(JsonPrimitive("{{numberVar}} * 2"), "number", "output")), listOf(
                    Variable.of(
                        JsonPrimitive(Fixtures.Vars.numberVar.value.jsonPrimitive.int * 2), "number", "output"
                    )
                )
            ), Spec(
                listOf(Variable.of(JsonPrimitive("\$sum([{{numberVar}}, {{numberVar}}])"), "number", "output")), listOf(
                    Variable.of(
                        JsonPrimitive(Fixtures.Vars.numberVar.value.jsonPrimitive.int + Fixtures.Vars.numberVar.value.jsonPrimitive.int),
                        "number",
                        "output"
                    )
                )
            ),
            // strings
            Spec(
                listOf(Variable.of(JsonPrimitive("{{stringVar}}"), "text", "output")), listOf(
                    Variable.of(
                        JsonPrimitive(Fixtures.Vars.stringVar.value.jsonPrimitive.content), "text", "output"
                    )
                )
            ), Spec(
                listOf(Variable.of(JsonPrimitive("\$join([{{stringVar}}, ' ', {{stringVar}}])"), "text", "output")),
                listOf(
                    Variable.of(
                        JsonPrimitive("${Fixtures.Vars.stringVar.value.jsonPrimitive.content} ${Fixtures.Vars.stringVar.value.jsonPrimitive.content}"),
                        "text",
                        "output"
                    )
                )
            ),
            // boolean
            Spec(
                listOf(Variable.of(JsonPrimitive("{{booleanVar}}"), "boolean", "output")), listOf(
                    Variable.of(
                        JsonPrimitive(Fixtures.Vars.booleanVar.value.jsonPrimitive.boolean), "boolean", "output"
                    )
                )
            ), Spec(
                listOf(Variable.of(JsonPrimitive("{{booleanVar}} = true"), "boolean", "output")), listOf(
                    Variable.of(
                        JsonPrimitive(Fixtures.Vars.booleanVar.value.jsonPrimitive.boolean == true), "boolean", "output"
                    )
                )
            ), Spec(
                listOf(Variable.of(JsonPrimitive("{{booleanVar}} = false"), "boolean", "output")), listOf(
                    Variable.of(
                        JsonPrimitive(Fixtures.Vars.booleanVar.value.jsonPrimitive.boolean == false),
                        "boolean",
                        "output"
                    )
                )
            ),
            // list
            Spec(
                listOf(Variable.of(JsonPrimitive("{{listVar}}"), "list", "output")),
                listOf(Variable.of(Fixtures.Vars.listVar.value, "list", "output"))
            ), Spec(
                listOf(Variable.of(JsonPrimitive("\$count({{listVar}})"), "number", "output")),
                listOf(Variable.of((Fixtures.Vars.listVar.value as JsonArray).size, "number", "output"))
            ), Spec(
                listOf(Variable.of(JsonPrimitive("\$count({{listVar}}) + 10"), "number", "output")),
                listOf(Variable.of(Fixtures.Vars.listVar.value.size + 10, "number", "output"))
            ), Spec(
                listOf(Variable.of(JsonPrimitive("\$count({{listVar}}) + {{numberVar}}"), "number", "output")), listOf(
                    Variable.of(
                        Fixtures.Vars.listVar.value.size + Fixtures.Vars.numberVar.value.jsonPrimitive.int,
                        "number",
                        "output"
                    )
                )
            )
        )
    }

    data class Spec(
        val rawVariables: List<Variable>,
        val expectedVariables: List<Variable>,
        val contextVariables: List<Variable> = emptyList(),

        )

    @Test
    fun `should resolve type and identifier`() {
        `should resolve properly`(
            Spec(
                listOf(Variable.of(JsonPrimitive("{{stringVar}}"), "{{type}}", "{{identifier}}")), listOf(
                    Variable.of(
                        JsonPrimitive(Fixtures.Vars.stringVar.value.jsonPrimitive.content),
                        Fixtures.Vars.type.value.jsonPrimitive.content,
                        Fixtures.Vars.identifier.value.jsonPrimitive.content,
                    )
                )
            ),
        )
    }

    @Test
    fun `should resolve type and identifier with prefix`() {
        `should resolve properly`(
            Spec(
                listOf(Variable.of(JsonPrimitive("{{stringVar}}"), "t{{ext}}", "out.{{identifier}}")), listOf(
                    Variable.of(
                        JsonPrimitive(Fixtures.Vars.stringVar.value.jsonPrimitive.content),
                        "text",
                        "out.${Fixtures.Vars.identifier.value.jsonPrimitive.content}",
                    )
                ), listOf(
                    Variable.of(
                        JsonPrimitive("ext"),
                        "text",
                        "ext",
                    )
                )
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("specProviderMany")
    fun `should resolve properly`(spec: Spec) = runTest {
        val setVariablesStep = SetVariablesExecutionStepV2(
            Step(
                Step.Id("1"), Step.Variant.SET_VARIABLES, "name", Step.Properties(
                    typePrimaryIdentifier = null, variables = spec.rawVariables.toMutableList()
                )
            ),
            ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "1"),
            variableOperations = listOf(DefaultVariableOperation(false))
        )

        val context = Fixtures.context()

        context.flowContext.set(Fixtures.Vars.numberVar)
        context.flowContext.set(Fixtures.Vars.stringVar)
        context.flowContext.set(Fixtures.Vars.booleanVar)
        context.flowContext.set(Fixtures.Vars.listVar)
        context.flowContext.set(Fixtures.Vars.objectVar)
        context.flowContext.set(Fixtures.Vars.type)
        context.flowContext.set(Fixtures.Vars.identifier)
        spec.contextVariables.forEach { context.flowContext.set(it) }

        setVariablesStep.execute(context)

        logger.debug("Flow context: {}", Json.encodeToString(context))
        logger.debug("Actual. : {}", context.flowContext.variables["output"])
        logger.debug("Expected: {}", spec.expectedVariables[0])

        assertThat(context.flowContext.variables.values).containsAll(spec.expectedVariables)
    }

    @Test
    fun `should throw exception`() {
        `should resolve properly`(
            Spec(
                listOf(Variable.of(JsonPrimitive("System.exit()"), "boolean", "output")),
                listOf(Variable.of(JsonNull, "boolean", "output"))
            )
        )
    }


    @Test
    fun `should set variable`() = runTest {

        val setVariablesStep = SetVariablesExecutionStepV2(
            Step(
                Step.Id("1"), Step.Variant.SET_VARIABLES, "name", Step.Properties(
                    typePrimaryIdentifier = null, variables = mutableListOf(
                        Variable(
                            JsonPrimitive("1 + 1"), "number", "var1"
                        ), Variable(
                            JsonPrimitive("'string literal'"), "text", "var2"
                        ), Variable(
                            JsonPrimitive("{{var1}}"), "number", "var3"
                        ), Variable(
                            // we won't support joining strings with expressions like `{{var2}} {{var2}}` because jsonata won't handle it
                            // for now they'll have to use a concatenation function or join
                            // when we replace the tokens we'll have to know to quote the strings for jsonata - based on either the JsonPrimitive.isString or the Variable.type
                            JsonPrimitive("\$join([{{var2}},'/',{{var2}}])"), // expect jsonata to receive quoted strings here
                            "text", "var4"
                        )
                    )
                )
            ),
            ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "1"),
            variableOperations = listOf(DefaultVariableOperation(false))
        )

        val context = FlowContextWithLocalStep(
            Step.Id("1"), FlowContext(
                global = Fixtures.global,
                workspace = Fixtures.workspaceContext,
                variables = mutableMapOf(),
                event = Fixtures.event,
            )
        )

        setVariablesStep.execute(context)

        logger.debug("Flow context: {}", Json.encodeToString(context))

        // check that the FED is updated with the resolved values
//        assertThat(setVariablesStep.step.properties.variables!![0].value).isEqualTo(JsonPrimitive("1 + 1"))
//        assertThat(setVariablesStep.step.properties.variables[1].value).isEqualTo(JsonPrimitive("'string literal'"))
//        assertThat(setVariablesStep.step.properties.variables[2].value).isEqualTo(JsonPrimitive("2"))
//        assertThat(setVariablesStep.step.properties.variables[3].value).isEqualTo(JsonPrimitive("\$join([\"string literal\",'/',\"string literal\"])"))

        // check the context variables are correct
        assertThat(context.flowContext.variables["var1"]?.value).isEqualTo(JsonPrimitive(2))
        assertThat(context.flowContext.variables["var2"]?.value).isEqualTo(JsonPrimitive("string literal"))
        assertThat(context.flowContext.variables["var3"]?.value).isEqualTo(JsonPrimitive(2))
        assertThat(context.flowContext.variables["var4"]?.value).isEqualTo(JsonPrimitive("string literal/string literal"))

    }

    @Test
    fun `given step configuration when running step then should populate placeholders`() = runTest {
        val stepConfiguration = Json.decodeFromString<Step>(
            """
                {
                    "id": "LxUM7X-Hw1ND9oTMzc8TF",
                    "name": "Set variable(s)",
                    "next": "l4LcyIEnZaTTJM-KBPAbx",
                    "properties": {
                      "typePrimaryIdentifier": null,
                      "variables": [
                        {
                          "identifier": "CYPayroll",
                          "properties": {
                            "columnIdentifier": "{{someQuestionId}}",
                            "operation": "setCell",
                            "rowIndex": "{{PayrollItems_index}}"
                          },
                          "type": "table",
                          "value": "{{StandardMapping}}"
                        }
                      ]
                    },
                    "variant": "setVariables"
                  }
            """.trimIndent()
        )

        val executionStep = SetVariablesExecutionStepV2(
            stepConfiguration,
            ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "1"),
            variableOperations = listOf(DefaultVariableOperation(false))
        )

        val context = Fixtures.context()
        context.flowContext.set(
            Variable.of(JsonPrimitive("1"), "number", "PayrollItems_index")
        )
        context.flowContext.set(
            Variable.of(JsonPrimitive("myStandardMapping"), "text", "StandardMapping")
        )
        context.flowContext.set(
            Variable.of(JsonPrimitive("testQuestionId"), "text", "someQuestionId")
        )

        // Populated variable: Variable(value="\"myStandardMapping\"", type=table, identifier=CYPayroll, properties=TableVariableProperties(operation=SET_CELL, rowIdentifier=null, rowIndex=1, columnIdentifier=testQuestionId, columns=[]))
        stepConfiguration.properties.variables?.forEachIndexed { idx, variable ->
            val populatedVariable = executionStep.populate(variable, context, "$idx")
            logger.debug("Populated variable: {}", populatedVariable)
        }
    }

}