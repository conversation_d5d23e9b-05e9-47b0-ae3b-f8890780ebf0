{"ui": {"terminology": {"appName": "OneTeam Internal", "oneteam": "OneTeam", "workspace": "Workspace", "configuration": "Configuration", "workspaceConfiguration": "$d(ui.terminology.workspace) {{$d(ui.terminology.configuration), lowercase}}", "collection": "Collection", "form": "Form", "formConfiguration": "$d(ui.terminology.form) {{$d(ui.terminology.configuration), lowercase}}", "question": "Question", "answer": "Answer", "section": "Section", "flow": "Flow", "flowConfiguration": "$d(ui.terminology.flow) {{$d(ui.terminology.configuration), lowercase}}", "series": "Series", "interval": "Interval", "seriesInterval": "$d(ui.terminology.series) $d(ui.terminology.interval)", "label": "Label", "foundation": "Foundation", "foundationConfiguration": "$d(ui.terminology.foundation) Level", "variable": "Variable", "step": "Step", "stepType": "Step type", "trigger": "<PERSON><PERSON>", "action": "Action", "condition": "Condition", "setVariables": "Set {{$d(ui.terminology.variable), lowercase}}(s)", "iterator": "Iterator", "annotation": "Annotation", "alert": "<PERSON><PERSON>", "user": "User", "apiKey": "API Key"}, "common": {"crud": {"create": "Create", "update": "Update", "delete": "Delete"}, "search": "Search", "loading": "Loading...", "lastModified": "Last Modified", "noData": "No data available", "name": "Name", "identifier": "Identifier", "value": "Value", "description": "Description", "save": "Save", "cancel": "Cancel", "create": "New", "update": "Edit", "duplicate": "Duplicate", "copy": "Copy", "paste": "Paste", "delete": "Delete", "rename": "<PERSON><PERSON>", "add": "Add", "key": "Key", "run": "Run", "or": "or", "viewAsDocument": "View document", "preview": "Preview", "view": "View", "row": "Row", "column": "Column", "hide": "<PERSON>de", "show": "Show", "help": "Help", "connected": "Connected", "disconnected": "Disconnected", "status": "Status", "highlight": "Highlight"}, "auth": {"redirectToLogin": "Redirecting to login..."}, "configuration": {"title": "$d(ui.terminology.configuration)", "dashboard": {"title": "Dashboard", "publish": "Publish", "import": "Import", "export": "Export", "importModal": {"heading": "$d(ui.configuration.dashboard.import) $d(ui.terminology.workspaceConfiguration)", "description": "Provide a $d(ui.terminology.workspaceConfiguration) file to import, or paste the JSON content below", "publishedWorkspace": "Cannot import a $d(ui.terminology.workspaceConfiguration) as this {{$d(ui.terminology.workspace), lowercase}} has already been published", "overwrite": "Importing a $d(ui.terminology.workspaceConfiguration) will overwrite the current {{$d(ui.terminology.configuration), lowercase}}, except for your {{$d(ui.terminology.workspace), lowercase}} {{$d(ui.common.key), lowercase}} and {{$d(ui.common.name), lowercase}}", "tabs": {"file": {"label": "Import from file"}, "json": {"label": "JSON content"}}}, "alert": {"publish": {"success": "$d(ui.terminology.workspaceConfiguration) published successfully", "error": "Failed to publish configuration:"}, "import": {"success": "$d(ui.terminology.workspaceConfiguration) imported successfully", "error": "Failed to import configuration:"}}}, "forms": {"title": "$d(ui.forms.title)", "key": "$d(ui.common.key)", "status": "Status", "section": {"title": "$d(ui.terminology.section)", "add": "$d(ui.common.add) {{$d(ui.terminology.section), lowercase}}"}, "tooltip": {"formKey": "{{$d(ui.terminology.form)}} {{$d(ui.common.key), lowercase}}", "interval": "{{seriesName}}"}, "question": {"title": "$d(ui.terminology.question)", "preview": "$d(ui.common.preview)", "type": {"title": "$d(ui.terminology.question) Type", "text": {"label": "Text", "description": ""}, "number": {"label": "Number", "description": ""}, "date": {"label": "Date", "description": ""}, "select": {"label": "Select option(s)", "description": ""}, "multiSelect": {"label": "Select option(s)", "description": ""}, "boolean": {"label": "Yes or No", "description": ""}, "files": {"label": "File(s)", "description": ""}, "table": {"label": "Table", "description": ""}, "json": {"label": "JSON", "description": ""}, "list": {"label": "List", "description": ""}, "schema": {"label": "JSON schema", "description": "", "tabHeaders": {"sample": "<PERSON><PERSON>", "schema": "<PERSON><PERSON><PERSON>", "preview": "Preview"}}, "fillInTemplate": {"label": "Fill in template", "description": ""}, "generateDocument": {"label": "Generate document", "description": ""}, "reuseQuestion": {"label": "Reuse {{$d(ui.terminology.question), lowercase}}", "description": ""}}, "text": {"label": "$d(ui.terminology.question)", "isTextArea": {"label": "Multiple lines", "description": "Whether multiple lines are visible in the answer"}}, "number": {"type": "Type", "decimalPlaces": "Decimal places", "min": "Min", "max": "Max", "number": "Number", "accounting": "Accounting", "percentage": "Percentage"}, "boolean": {"trueDefault": "Yes", "falseDefault": "No", "trueText": "Yes label", "falseText": "No label"}, "date": {"type": "Type", "min": "Minimum", "max": "Maximum"}, "configuration": {"title": "Question configuration"}, "select": {"allowSelectMultiple": {"label": "Allow select multiple", "description": "Whether multiple options can be selected"}, "options": {"title": "Options", "label": {"label": "Display name"}, "value": {"label": "Value to save"}, "add": "$d(ui.common.add) Answer"}}, "files": {"minFiles": {"label": "Min number of files"}, "maxFiles": {"label": "Max number of files"}, "maxFileSizeMB": {"label": "Maximum file size (MB)"}, "restrictedFileTypes": {"label": "Restrict to file types", "formats": {"image": "Image", "video": "Video", "audio": "Audio", "pdf": "PDF", "document": "Document", "spreadsheet": "Spreadsheet", "presentation": "Presentation"}}, "uploadInstructions": {"placeholder": "Drag and drop a file here", "orText": "or", "browseButtonLabel": "browse"}}, "table": {"title": "Columns", "add": "$d(ui.common.add) $d(ui.common.column)"}, "json": {"title": "Items", "add": "$d(ui.common.add) item", "isUnstructured": {"label": "Unstructured JSON", "description": "Data that does not conform to a predefined schema"}, "toggle": "JSON Mode"}, "list": {"title": "List of"}, "description": {"label": "Description", "description": ""}, "options": {"label": "Options", "description": "", "add": "$d(ui.common.add) Option", "remove": "Remove Option"}, "placeholder": {"label": "Placeholder", "description": ""}, "defaultRequiredHiddenDisabled": {"title": "De<PERSON>ults"}, "required": {"label": "Required", "description": ""}, "disabled": {"label": "Disabled for {{$d(ui.terminology.user), lowercase}}", "description": ""}, "defaultValue": {"label": "Default Value", "description": ""}, "identifier": {"label": "Unique identifier", "description": ""}, "allowReuseAcrossForms": {"label": "Allow reuse across other forms", "description": "Whether this {{$d(ui.terminology.question), lowercase}} can be used in other forms and the data will flow to and from"}, "hidden": {"label": "Hidden", "tooltip": "This field is hidden from the form", "description": ""}, "minLength": {"label": "Min Length"}, "maxLength": {"label": "Max Length"}}, "createModal": {"title": "$d(ui.common.create) {{$d(ui.terminology.form), lowercase}}"}, "updateModal": {"title": "$d(ui.common.update) '{{name}}'"}, "duplicateModal": {"title": "$d(ui.common.duplicate) '{{name}}'"}, "deleteModal": {"title": "$d(ui.common.delete) '{{name}}'", "message": "Are you sure you want to delete this {{$d(ui.terminology.form), lowercase}}? This action is irreversible", "confirmLabel": "$d(ui.common.delete)", "published": {"title": "This {{$d(ui.terminology.form), lowercase}} cannot be deleted", "message": "'{{name}}' cannot be deleted as it has already been published", "confirmLabel": "Okay"}}, "fields": {"name": "$d(ui.common.name)", "key": {"label": "$d(ui.terminology.form) $d(ui.common.key)", "tooltip": "A unique key to identify the {{$d(ui.terminology.form), lowercase}}"}, "description": "Description", "foundation": {"description": "The data entity to which data will be collected", "globalLabel": "(Global)"}, "allowMultiple": {"label": "Multiple per {{$d(ui.terminology.foundation), lowercase}}", "description": "Whether multiple of this {{$d(ui.terminology.form), lowercase}} can be answered by the same {{$d(ui.terminology.foundation), lowercase}} within the same {{$d(ui.terminology.seriesInterval), lowercase}}"}}}, "flows": {"createButton": "$d(ui.common.create) {{$d(ui.terminology.flow), lowercase}}", "title": "$d(ui.terminology.flow)s", "creation": {"title": "$d(ui.common.create) {{$d(ui.terminology.flow), lowercase}}"}, "edit": {"title": "$d(ui.common.update) {{$d(ui.terminology.flow), lowercase}}"}, "step": {"add": "$d(ui.common.add) {{$d(ui.terminology.trigger), lowercase}} {{$d(ui.terminology.step), lowercase}}", "delete": {"cannotDeleteTrigger": "Cannot delete {{$d(ui.terminology.trigger), lowercase}} {{$d(ui.terminology.step), lowercase}}", "cannotDeleteVariant": "Deletion of {{variant}} {{$d(ui.terminology.step), lowercase}}s with multiple subsequent {{$d(ui.terminology.step), lowercase}}s is not supported yet. Delete the subsequent {{$d(ui.terminology.step), lowercase}}s first."}, "variants": {"common": {"propertiesModal": {"expandAll": "Expand All", "collapseAll": "Collapse All", "inputs": {"label": "Inputs"}, "flowStepType": {"label": "Type", "select": "Select a type"}}}, "trigger": {"label": "$d(ui.terminology.trigger)", "description": "", "addModal": {"heading": "Choose a {{$d(ui.terminology.trigger), lowercase}} type that begins the flow"}, "updateModal": {"heading": "Update the {{$d(ui.terminology.trigger), lowercase}} type that begins the flow"}}, "action": {"label": "$d(ui.terminology.action)", "description": "", "addModal": {"heading": "Choose an {{$d(ui.terminology.action), lowercase}} type"}, "updateModal": {"heading": "Update the {{$d(ui.terminology.action), lowercase}} type"}, "noActionWarning": "This {{$d(ui.terminology.flow), lowercase}} has no {{$d(ui.terminology.step), lowercase}}s and will not be queued if triggered", "propertiesModal": {"addButton": "Add", "delete": {"lastAnswer": "Must have at least one answer. Remove the {{$d(ui.terminology.step), lowercase}} instead."}}}, "condition": {"label": "$d(ui.terminology.condition)", "description": "", "propertiesModal": {"branch": "Branch", "nestedCondition": "Nested $d(ui.terminology.condition)", "nestedConditionBlocks": "$d(ui.configuration.flows.step.variants.condition.propertiesModal.nestedCondition) Blocks", "delete": {"lastBranch": "$d(ui.configuration.flows.step.variants.condition.label) must have at least one branch. Remove the {{$d(ui.terminology.step), lowercase}} instead.", "hasNext": "Deletion of branches with subsequent {{$d(ui.terminology.step), lowercase}}s is not supported yet. Delete the subsequent {{$d(ui.terminology.step), lowercase}}s first."}}, "operator": {">": {"label": "greater than", "description": ""}, ">=": {"label": "greater than or equal", "description": ""}, "<": {"label": "less than", "description": ""}, "<=": {"label": "less than or equal", "description": ""}, "contains": {"label": "contains", "description": ""}, "doesNotContain": {"label": "does not contain", "description": ""}, "isEmpty": {"label": "is empty", "description": ""}, "isNotEmpty": {"label": "is not empty", "description": ""}, "=": {"label": "equals", "description": ""}, "!=": {"label": "does not equal", "description": ""}}, "logicalOperator": {"AND": {"label": "AND", "description": ""}, "OR": {"label": "OR", "description": ""}}, "elseBranch": "Else"}, "setVariables": {"label": "$d(ui.terminology.setVariables)", "description": "", "propertiesModal": {"addButton": "Add {{$d(ui.terminology.variable), lowercase}}", "expandAll": "Expand All", "collapseAll": "Collapse All", "delete": {"lastVariable": "$d(ui.configuration.flows.step.variants.setVariables.label) must have at least one {{$d(ui.terminology.variable), lowercase}} . Remove the {{$d(ui.terminology.step), lowercase}} instead."}}}, "iterator": {"label": "$d(ui.terminology.iterator)", "description": "", "type": {"forEach": {"label": "For Each", "description": ""}, "filter": {"label": "Filter", "description": ""}, "aggregate": {"label": "Aggregate", "description": ""}}, "addModal": {"heading": "Choose an {{$d(ui.terminology.iterator), lowercase}} type"}, "updateModal": {"heading": "Update the {{$d(ui.terminology.iterator), lowercase}} type"}}, "flow": {"label": "$d(ui.terminology.flow)", "description": "", "addModal": {"heading": "Choose a {{$d(ui.terminology.flow), lowercase}} type"}, "propertiesModal": {"flowConfiguration": {"label": "$d(ui.terminology.flowConfiguration) to run", "description": "", "tooltip": "$d(ui.terminology.flow)s with manual or 'Triggered from another {{$d(ui.terminology.flow), lowercase}}' triggers can be used inside a {{$d(ui.terminology.flow), lowercase}}", "openButton": "Open {{$d(ui.terminology.flow), lowercase}} {{$d(ui.terminology.configuration), lowercase}}"}}}}}, "stepType": {"category": {"label": "Category", "placeholder": "Select category"}, "deprecatedTypeWarning": {"summary": "$d(ui.terminology.stepType) deprecated", "message": "Deprecated {{$d(ui.terminology.stepType), lowercase}}", "suggestion": "Replace with '{{replacementStepTypeName}}'"}}, "duplicateName": "Copy of {{name}}", "deleteModal": {"title": "$d(ui.common.delete) {{$d(ui.terminology.flow), lowercase}}", "message": "Are you sure you want to delete this {{$d(ui.terminology.flow), lowercase}}? This is action is irreversible", "confirmLabel": "$d(ui.common.delete)"}, "status": {"label": "$d(ui.common.status)", "filter": {"title": "$d(ui.configuration.flows.status.label)", "placeholder": "Select {{$d(ui.common.status), lowercase}}"}, "active": {"label": "Active", "action": {"label": "Activate", "description": ""}}, "inactive": {"label": "Inactive", "action": {"label": "Deactivate", "description": ""}}}, "showHidden": {"label": "Show hidden {{$d(ui.configuration.flows.title), lowercase}}", "description": ""}, "hiddenTooltip": "Hidden from {{$d(ui.terminology.flow), lowercase}} list", "variables": {"title": "$d(ui.terminology.variable)s", "identifier": {"label": "$d(ui.terminology.variable) $d(ui.common.name)", "description": ""}, "value": {"label": "Value", "description": ""}, "unknown": "Unknown {{$d(ui.terminology.variable), lowercase}}", "reused": "{{Reused, uppercase}}", "new": "{{$d(ui.common.create), uppercase}}", "panel": {"title": "$d(ui.configuration.flows.variables.title)", "variablesTab": "$d(ui.configuration.flows.variables.title)", "copyVariableTip": "Copy a {{$d(ui.terminology.variable), lowercase}} to begin using it in values"}, "output": {"label": "Output {{$d(ui.terminology.variable), lowercase}}(s)", "empty": "No output {{$d(ui.terminology.variable), lowercase}}s", "cannotSet": "Cannot set an output {{$d(ui.terminology.variable), lowercase}}, choose another name"}, "startingVariable": {"title": "Starting {{$d(ui.terminology.variable), lowercase}}"}, "endingVariable": {"title": "Ending {{$d(ui.terminology.variable), lowercase}}"}, "openSourceStep": "Open source {{$d(ui.terminology.step), lowercase}}", "type": {"label": "Type", "unknown": {"label": "Unknown"}, "files": {"operation": {"label": "File operation", "description": "", "options": {"setFiles": {"label": "Set files", "description": "Setup or replace the entire file list"}, "addFiles": {"label": "Add files", "description": "Add files to the existing list"}, "removeFiles": {"label": "Remove files", "description": "Remove files from the existing list", "parameters": {"itemIndex": {"label": "Item index", "description": "Keep blank to add a new item"}}}}}}, "table": {"operation": {"label": "Table operation", "description": "", "options": {"setTable": {"label": "Set table", "description": "Setup or replace the entire table"}, "setCell": {"label": "Set cell", "description": "Add or update a specific cell"}, "setRow": {"label": "Set {{$d(ui.common.row), lowercase}}", "description": "Add or update an entire {{$d(ui.common.row), lowercase}}"}, "setColumn": {"label": "Set {{$d(ui.common.column), lowercase}}", "description": "Add or update an entire {{$d(ui.common.column), lowercase}}"}}}, "columnIdentifier": {"label": "$d(ui.common.column) identifier", "description": ""}, "row": {"label": "$d(ui.common.row)", "description": "Keep blank to add a new {{$d(ui.common.row), lowercase}}", "index": {"label": "$d(ui.common.row) index", "description": ""}, "identifier": {"label": "$d(ui.common.row) identifier", "description": ""}}}, "list": {"operation": {"label": "List operation", "description": "", "options": {"setList": {"label": "Set list", "description": "Setup or replace the entire list"}, "setItem": {"label": "Set item", "description": "Add or update a specific item"}, "removeItem": {"label": "Remove item", "description": "Remove a specific item"}}}, "item": {"index": {"label": "Item index", "description": "Keep blank to add a new item"}}}}, "reservedName": {"error": "Cannot use a reserved name, choose another name"}, "notAvailableTooltip": "This variable is not available to the current open step", "errors": {"tooManyParts": "Value must only contain 1 variable", "noDirectValues": "Value must be a variable (type '{{type}}')", "invalidNumber": "Value must be a valid number", "invalidDate": "Value must be a valid date (YYYY-MM-DD)", "invalidBoolean": "Value must be a valid boolean (true/false/yes/no/1/0)", "cannotBeExpression": "Value cannot be an expression", "invalidVariableType": "Expected variable type '{{expectedType}}', but got '{{actualType}}'"}}, "help": {"functions": {"title": "Functions", "documentation": {"description": "Description", "arguments": "Arguments", "examples": "Examples"}}}}, "foundations": {"title": "$d(ui.terminology.foundationConfiguration)s", "relationship": {"OneToOne": {"label": "one-to-one", "description": "A {{prevName}} has one {{currName}} and a {{currName}} has one {{prevName}}."}, "OneToMany": {"label": "one-to-many", "description": "A {{prevName}} can have many {{currName}}(s) and a {{currName}} has one {{prevName}}."}, "ManyToOne": {"label": "many-to-one", "description": "A {{prevName}} can have only one {{currName}} and a {{currName}} can have multiple {{prevName}}(s)."}, "ManyToMany": {"label": "many-to-many", "description": "A {{prevName}} can have multiple {{currName}}(s) and a {{currName}} can have multiple {{prevName}}(s)."}}, "cancelButton": "Cancel", "applyButton": "Apply", "placeholderName": "Untitled", "dashboardPanel": {"title": "$d(ui.configuration.foundations.title)", "empty": "No {{$d(ui.configuration.foundations.title), lowercase}} have been configured", "emptyCTA": "Configure {{$d(ui.configuration.foundations.title), lowercase}}"}, "deleteModal": {"title": "{{FoundationLevelName}} cannot be deleted", "message": "This {{$d(ui.terminology.foundationConfiguration), lowercase}} level cannot be deleted as there are already foundations in collection for this level", "confirmLabel": "Okay"}, "warningModal": {"title": "$d(ui.common.delete) {{FoundationLevelName}} {{$d(ui.configuration.foundations.title), lowercase}}", "message": "Are you sure you want to $d(ui.common.delete) this {{$d(ui.configuration.foundations.title), lowercase}}? There are forms setup in the configuration that are associated with this {{$d(ui.configuration.foundations.title), lowercase}} that will also be deleted as a result. This action is irreversible"}}, "series": {"title": "$d(ui.terminology.series)", "manageButton": "Configure $d(ui.configuration.series.title)", "creation": {"title": "$d(ui.common.create) {{$d(ui.configuration.series.title), lowercase}}"}, "edit": {"title": "$d(ui.common.update) {{$d(ui.configuration.series.title), lowercase}}"}, "interval": {"title": "$d(ui.terminology.interval)s", "addButton": "$d(ui.common.add) {{$d(ui.terminology.interval), lowercase}}", "example": {"title": "Current setup example", "empty": "$d(ui.common.add) first {{$d(ui.terminology.interval), lowercase}} to see example"}, "name": "$d(ui.terminology.interval) {{$d(ui.common.name), lowercase}}", "placeholder": "$d(ui.terminology.interval) {{$d(ui.common.name), lowercase}}", "intervalRequired": "At least one interval is required."}}, "labels": {"title": "$d(ui.terminology.label)s", "createButton": "$d(ui.common.create) {{$d(ui.terminology.label), lowercase}}", "creation": {"title": "$d(ui.common.create) {{$d(ui.terminology.label), lowercase}}"}, "edit": {"title": "$d(ui.common.update) {{$d(ui.terminology.label), lowercase}}"}, "filter": {"placeholder": "$d(ui.terminology.label)s"}, "color": {"label": "Colour", "options": {"1": "Grey", "2": "Purple", "3": "Blue", "4": "Green", "5": "Orange", "6": "Red"}}, "availableTo": {"label": "Available to", "description": "", "options": {"formConfiguration": "$d(ui.configuration.forms.title)", "flowConfiguration": "$d(ui.configuration.flows.title)"}}}, "navigation": {"extrasMenuName": "Extras"}, "variables": {"title": "Variables", "terminology": {"variable": "Variable", "workspaceVariable": "$d(ui.terminology.workspace) {{$d(ui.configuration.variables.terminology.variable), lowercase}}"}, "create": {"actionButton": "$d(ui.common.create) {{$d(ui.configuration.variables.terminology.variable), lowercase}}", "title": "$d(ui.common.create) {{$d(ui.configuration.variables.terminology.variable), lowercase}}", "success": "{{$d(ui.configuration.variables.terminology.variable)}} created"}, "update": {"title": "$d(ui.common.update) {{$d(ui.configuration.variables.terminology.variable), lowercase}}", "success": "{{$d(ui.configuration.variables.terminology.variable)}} updated"}, "delete": {"actionButton": "$d(ui.common.delete) {{$d(ui.configuration.variables.terminology.variable), lowercase}}", "confirmation": {"title": "$d(ui.common.delete) {{$d(ui.configuration.variables.terminology.variable), lowercase}}", "secured": {"message": "{{$d(ui.configuration.variables.terminology.workspaceVariable)}} '{{variableName}}' will be deleted.\n\nAre you sure you want to continue?"}, "unsecured": {"message": "{{$d(ui.configuration.variables.terminology.workspaceVariable)}} '{{variableName}}' will be deleted.\n\nAre you sure you want to continue?"}}, "success": "{{variableName}} - {{$d(ui.configuration.variables.terminology.variable), lowercase}} was deleted"}, "columns": {"name": "$d(ui.common.name)", "description": "$d(ui.common.description)", "value": "$d(ui.common.value)", "isSecured": "Secured", "createdAt": "Created at", "updatedAt": "$d(ui.common.lastModified)"}, "fields": {"name": {"label": "$d(ui.common.name)"}, "description": {"label": "$d(ui.common.description)"}, "value": {"label": "$d(ui.common.value)"}, "isSecured": {"label": "Secured", "description": "Once secured, you can no longer see this value", "state": {"secured": "Secured", "unsecured": "Not Secured"}}}}}, "help": {"title": "Help"}, "notifications": {"unread": "{{count}} unread messages", "warning": "Warning", "success": "Success", "notification": "Notification", "actionLabels": {"viewInFlowRunner": "View in $d(ui.flow.title)", "viewExecutionDocument": "View execution document"}}, "navigation": {"workspaceMenuLabel": "Switch {{$d(ui.terminology.workspace), lowercase}}s or apps", "workspaceMenuTitle": "$d(ui.terminology.workspace)s", "createWorkspace": "$d(ui.common.create) $d(ui.terminology.workspace)", "switchToOneTeam": "Switch To $d(ui.terminology.oneteam)", "altOneTeamLogo": "$d(ui.terminology.oneteam) Logo", "altOneTeamAILogo": "$d(ui.terminology.oneteam) Internal Logo", "appSettingsLabel": "$d(ui.appSettings.title)"}, "collection": {"title": "$d(ui.terminology.collection)", "browse": {"title": "Browse all", "sideNavTitle": "Browse all", "tooltip": {"foundationKey": "{{$d(ui.terminology.foundation)}} {{$d(ui.common.key), lowercase}}", "foundationConfiguration": "{{$d(ui.terminology.foundationConfiguration)}}"}, "search": {"breadcrumb": "$d(ui.common.search) results", "title": "$d(ui.common.search) results for '{{searchTerm}}'", "input": {"placeholder": "$d(ui.common.search) all"}}, "createButton": {"label": "$d(ui.common.create)", "form": "{{foundationConfigurationName}} {{$d(ui.terminology.form), lowercase}}", "foundation": "{{foundationConfigurationName}}"}, "tabs": {"foundations": "{{foundationConfigurationName}}(s)", "forms": "{{foundationConfigurationName}} {{$d(ui.terminology.form), lowercase}}s"}}, "formModal": {"createTitle": "$d(ui.common.create) {{foundation}} {{$d(ui.terminology.form), lowercase}}"}, "foundationModal": {"createTitle": "$d(ui.common.create) {{foundation}}", "updateTitle": "$d(ui.common.update) {{foundation}}"}}, "workspace": {"create": {"title": "$d(ui.common.create) $d(ui.terminology.workspace)", "createButton": "$d(ui.common.save)"}, "fields": {"name": {"label": "$d(ui.common.name)"}, "key": {"label": "$d(ui.terminology.workspace) $d(ui.common.key)", "tooltip": "A unique {{$d(ui.common.key), lowercase}} to identify the {{$d(ui.terminology.workspace), lowercase}}"}, "description": {"label": "$d(ui.common.description)", "description": ""}}, "update": {"success": "$d(ui.terminology.workspace) saved successfully", "fail": "Unable to save {{$d(ui.terminology.workspace), lowercase}}, please try again or contact support"}, "delete": {"button": "$d(ui.common.delete) {{$d(ui.terminology.workspace), lowercase}}", "modal": {"title": "$d(ui.common.delete) {{$d(ui.terminology.workspace), lowercase}}", "message": "Are you sure you want to delete '{{workspaceName}}'? This action is irreversible", "confirmLabel": "$d(ui.common.delete)"}}, "revertToPublishedVersion": {"title": "Revert to published version", "message": "Are you sure you want to revert the {{$d(ui.terminology.workspaceConfiguration), lowercase}} to the published version? This action is irreversible and any changes made since the last publish will be lost", "confirmLabel": "Continue"}, "annotations": {"title": "$d(ui.terminology.annotation)s", "deleteModal": {"title": "$d(ui.common.delete) {{$d(ui.terminology.annotation), lowercase}}", "message": "Are you sure you want to delete this {{$d(ui.terminology.annotation), lowercase}}?", "confirmLabel": "$d(ui.common.delete)"}, "resolved": {"label": "Resolved"}, "actions": {"markAsResolved": "<PERSON> as resolved", "markAsUnresolved": "<PERSON> as unresolved", "reply": "Reply", "delete": "$d(ui.common.delete)", "update": "$d(ui.common.update)"}, "edited": "Edited", "replies": {"one": "1 reply", "many": "{{count}} replies"}, "alerts": {"title": "$d(ui.terminology.alert)s", "type": {"title": "$d(ui.terminology.alert) type", "info": {"label": "Information", "description": ""}, "success": {"label": "Success", "description": ""}, "warning": {"label": "Warning", "description": ""}, "blocker": {"label": "Blocker", "description": ""}}}}}, "foundations": {"title": "$d(ui.terminology.foundation)s", "fields": {"name": {"label": "$d(ui.common.name)", "placeholder": "Provide your {{$d(ui.terminology.foundation), lowercase}} with a {{$d(ui.common.name), lowercase}}", "description": "Your {{$d(ui.terminology.foundation), lowercase}} {{$d(ui.common.name), lowercase}} can be changed later on"}, "key": {"label": "{{foundationConfigurationName}} $d(ui.common.key)", "tooltip": "A unique key to identify the {{foundationConfigurationName}}", "changeWarning": "Changing the $d(ui.terminology.foundation) key may result in some unexpected issues (including unlinking integrations with other systems). Please consider if you need to make this update."}, "details": {"label": "Details"}, "forms": {"label": "$d(ui.configuration.forms.title)"}, "status": {"label": "Status"}, "users": {"label": "Users"}}}, "forms": {"title": "$d(ui.terminology.form)s", "completed": "Completed", "progress": "Progress", "view": {"title": "$d(ui.terminology.form) documents", "answersDocument": "Answers", "annotationsDocument": "Annotations", "collaboratorsCursors": "User cursors"}, "cancel": {"label": "$d(ui.common.cancel)"}, "create": {"title": "$d(ui.common.create) {{foundationName}} {{$d(ui.terminology.form), lowercase}}", "createButton": "$d(ui.common.save)"}, "fields": {"name": {"label": "$d(ui.common.name)", "placeholder": "Provide your {{$d(ui.terminology.form), lowercase}} with a {{$d(ui.common.name), lowercase}}", "description": "Your {{$d(ui.terminology.form), lowercase}} {{$d(ui.common.name), lowercase}} can be changed later on"}, "type": {"label": "Type"}, "status": {"label": "Status"}, "users": {"label": "Users"}}, "question": {"table": {"deleteRow": "$d(ui.common.delete) {{$d(ui.common.row), lowercase}} {{rowIndex}}", "addRow": "$d(ui.common.add) {{$d(ui.common.row), lowercase}}", "data": "data", "upload": "Upload $d(ui.forms.question.table.data)", "download": "Download $d(ui.forms.question.table.data)", "notifications": {"upload": {"started": {"heading": "Upload in progress", "description": "The file is being processed. You will be notified when processing has completed"}, "success": {"heading": "Upload successful", "description": ""}, "failed": {"heading": "Upload failed", "description": "The file provided could not be processed.\nPlease check the file contents, try again or contact support"}}, "download": {"started": {"heading": "Preparing file for download", "description": "The file is being prepared. Please remain on the page and your browser will download the file when ready"}, "failed": {"heading": "Download failed", "description": "The file could not be downloaded.\nPlease try again or contact support"}}}}}, "collaboration": {"users": {"anonymous": {"displayName": "Anonymous OneTeam User", "firstName": "O", "lastName": "T"}, "system": {"displayName": "OneTeam System", "firstName": "O", "lastName": "T"}}}, "summaryModal": {"tabs": {"all": {"label": "All"}, "alerts": {"label": "$d(ui.terminology.alert)s"}, "comments": {"label": "Comments"}, "highlights": {"label": "$d(ui.common.highlight)s"}}, "filters": {"showResolved": {"label": "Show resolved", "description": ""}}}}, "flow": {"title": "$d(ui.terminology.flow) Runner", "noData": "No {{$d(ui.configuration.flows.title), lowercase}} have ran", "runButton": "$d(ui.common.run) $d(ui.terminology.flow)", "cancelButton": "Cancel", "startExecutionButton": "Start now", "filters": {"status": "Status", "result": "Result", "tag": "Tag", "tag_plural": "Tags", "startTime": "Start time"}, "columns": {"name": "$d(ui.common.name)", "tags": "$d(ui.flow.filters.tag_plural)", "status": "$d(ui.flow.filters.status)", "result": "$d(ui.flow.filters.result)", "runTime": "Run time", "startTime": "Start time", "endTime": "End time", "createdAt": "Created at"}, "status": {"PENDING": "Pending", "RUNNING": "Running", "COMPLETED": "Complete", "CANCELLING": "Cancelling"}, "result": {"PENDING": "Pending", "SUCCESS": "Success", "FAILED": "Fail", "CANCELLED": "Cancelled"}, "alreadyQueued": "{{<PERSON><PERSON><PERSON><PERSON>}} is already in the queue", "queuedSuccessfully": "{{button<PERSON><PERSON><PERSON>}} has been queued to begin", "failedInQueue": "\"{{button<PERSON><PERSON><PERSON>}}\" failed at \"{{failedAt}}\" with message: \"{{error}}\""}, "settings": {"title": "Settings", "details": {"title": "$d(ui.terminology.workspace) details"}, "permissions": {"title": "Permissions", "description": "Manage who can access and perform actions in this {{$d(ui.terminology.workspace), lowercase}}", "level": {"COLLECTION": {"label": "$d(ui.collection.title)", "description": "Gather data from {{$d(ui.configuration.foundations.title), lowercase}} (access to {{$d(ui.terminology.foundation), lowercase}}s or {{$d(ui.terminology.form), lowercase}}s can be managed in $d(ui.collection.title))", "addToGlobalWorkspace": {"label": "Add to global {{$d(ui.terminology.workspace), lowercase}}", "tooltip": "Allow users to see {{$d(ui.terminology.workspace), lowercase}} level {{$d(ui.terminology.form), lowercase}}s and publicly visible {{$d(ui.terminology.foundation), lowercase}}s"}}, "CONFIGURATION": {"label": "$d(ui.configuration.title)", "description": "Manage the configuration for {{$d(ui.configuration.foundations.title), lowercase}}, {{$d(ui.configuration.forms.title), lowercase}} and {{$d(ui.configuration.flows.title), lowercase}}"}, "SETTINGS": {"label": "$d(ui.settings.title)", "description": "Manage settings"}}, "updateStatusModal": {"INACTIVE": {"heading": "Deactivate {{$d(ui.terminology.user), lowercase}} from {{$d(ui.terminology.workspace), lowercase}}", "message": "The {{$d(ui.terminology.user), lowercase}} {{name}} will be deactivated and will no longer be able to access Workspace {{value}}.\n\nAre you sure you want to proceed?", "confirmLabel": "Deactivate"}, "ACTIVE": {"heading": "Reactivate {{$d(ui.terminology.user), lowercase}} in {{$d(ui.terminology.workspace), lowercase}}", "message": "The {{$d(ui.terminology.user), lowercase}} {{name}} will be reactivated and will be able to access Workspace {{value}}.\n\nAre you sure you want to proceed?", "confirmLabel": "Reactivate"}}, "status": {"label": "$d(ui.common.status)", "ACTIVE": {"label": "Active", "action": {"label": "Reactivate in {{$d(ui.terminology.workspace), lowercase}}", "description": ""}}, "INACTIVE": {"label": "Deactivated", "action": {"label": "Deactivate from {{$d(ui.terminology.workspace), lowercase}}", "description": ""}}}, "showDeactivated": {"label": "Show deactivated {{$d(ui.terminology.user), lowercase}}s", "description": ""}, "updateUser": {"title": "$d(ui.common.update)", "alert": "Failed to update {{$d(ui.terminology.user), lowercase}}."}, "addUsers": {"title": "Add {{$d(ui.terminology.user), lowercase}}(s) to {{$d(ui.terminology.workspace), lowercase}}", "button": "Add {{$d(ui.terminology.user), lowercase}}(s)", "noAccessSelected": "At least one access level must be provided", "noUserSelected": "At least one {{$d(ui.terminology.user), lowercase}} must be selected", "alert": "Failed to add {{$d(ui.terminology.user), lowercase}}s.", "usersSelect": {"label": "$d(ui.terminology.user)(s)", "placeholder": "Select {{$d(ui.terminology.user), lowercase}}(s) to add"}, "permissionLevelSelect": {"label": "Permissions", "description": "You can also edit permissions for individuals after inviting."}}}, "navigation": {"generalMenuName": "General", "detailsMenuLabel": "$d(ui.settings.details.title)", "permissionsMenuLabel": "$d(ui.settings.permissions.title)"}}, "appSettings": {"title": "Application settings", "generalMenuName": "General", "apiKeys": {"title": "API Keys", "fields": {"name": {"label": "$d(ui.common.name)"}, "description": {"label": "$d(ui.common.description)"}, "apiKey": {"label": "{{$d(ui.terminology.apiKey)}}"}, "createdAt": {"label": "Created at"}}, "create": {"actionButton": "$d(ui.common.create) {{$d(ui.terminology.apiKey)}}", "title": "$d(ui.common.create) {{$d(ui.terminology.apiKey)}}", "success": "{{$d(ui.terminology.apiKey)}} created", "failed": "Unable to create {{$d(ui.terminology.apiKey)}}, please try again or contact support"}, "postCreate": {"title": "{{$d(ui.terminology.apiKey)}} created", "viewOnceOnlyWarning": "This {{$d(ui.terminology.apiKey)}} will not be shown again.\nCopy and keep it secure.", "copyButton": "Copy", "copied": "Copied!", "closeButton": "Close"}, "delete": {"confirmation": {"title": "$d(ui.common.delete) {{$d(ui.terminology.apiKey)}}", "message": "The {{$d(ui.terminology.apiKey)}} '{{apiKeyName}}' will be permanently deleted and cannot be restored.\n\nAre you sure you want to continue?", "confirmButton": "$d(ui.common.delete) {{$d(ui.terminology.apiKey)}}"}, "success": "{{api<PERSON>ey<PERSON>ame}} - {{$d(ui.terminology.apiKey)}} was deleted"}}}, "websocket": {"connected": {"heading": "$d(ui.common.connected) to real-time server", "description": ""}}, "errorPage": {"notFound": {"title": "Oops! Something went wrong", "description": "This page is unable to load due to an error, or due to the page no longer being available.", "refresh": "Refresh page", "home": "Back to home"}, "accessDenied": {"title": "Access Denied", "description": "You do not have permission to access this page.", "home": "Back to home"}}}, "errors": {"common": {"duplicate": "{{name, capitalize}} already in use. Choose another {{name, lowercase}}", "required": "{{name, capitalize}} is required", "minLength": "Min length is {{length}} characters", "maxLength": "Max length is {{length}} characters", "length": "{{name, capitalize}} must be between {{min}} and {{max}} characters", "greaterThan": "{{name, capitalize}} must be greater than {{value}}", "lessThan": "{{name, capitalize}} must be less than {{value}}", "greaterThanOrEqual": "{{name, capitalize}} must be greater than or equal to {{value}}", "lessThanOrEqual": "{{name, capitalize}} must be less than or equal to {{value}}", "alphanumeric": "{{name, capitalize}} must be alphanumeric {{constraint}}", "alphabetSnakeCase": "{{name, capitalize}} must only contain alphabet characters or underscores {{constraint}}", "constraint": "with no spaces", "notFound": "Not found", "unexpected": "Unexpected error occurred", "badRequest": "Bad request", "inactiveFlow": "{{name, capitalize}} must reference an active flow", "key": {"duplicate": "$d(ui.common.key) already in use. Choose another {{$d(ui.common.key), lowercase}}", "uppercase": "$d(ui.common.key) must be uppercase", "length": "$d(ui.common.key) must be between 2 and 20 characters"}, "name": {"required": "$d(ui.common.name) is required", "duplicate": "$d(ui.common.name) already in use. Choose another {{$d(ui.common.name), lowercase}}", "length": "$d(ui.common.name) must be between 2 and 100 characters"}, "creationFailed": "Creation failed", "updateFailed": "$d(ui.common.crud.update) failed", "deletionFailed": "Deletion failed", "pageNotFound": "Page Not Found"}, "configurationForm": {"question": {"min": {"minMax": "Min value must be less than Max value"}, "defaultValue": {"lessThanMinLength": "Default value must be greater than or equal to Min Length", "greaterThanMaxLength": "Default value must be less than or equal to Max Length", "lessThanMin": "Default value must be greater than or equal to Min value", "greaterThanMax": "Default value must be less than or equal to Max value"}, "maxLength": {"lessThanMinLength": "Max Length must be greater than or equal to Min Length", "negative": "Max Length must be greater than or equal to 0"}, "minLength": {"greaterThanMaxLength": "Min Length must be less than or equal to Max Length", "negative": "Max Length must be greater than or equal to 0"}, "identifier": {"required": "Identifier is required", "constraint": "and _"}, "options": {"label": {"required": "Display name is required"}, "value": {"duplicate": "Value already in use. Choose another value"}}, "files": {"min": {"negative": "Minimum number of files must be greater than or equal to 0", "greaterThanMax": "Minimum number of files must be less than or equal to Maximum", "notEnoughFiles": "Must have at least {{min}} files", "noFileSelected": "No file selected"}, "max": {"negative": "Maximum number of files must be greater than or equal to 0", "lessThanMin": "Maximum number of files must be greater than or equal to Minimum number of files", "tooManyFiles": "Number of files provided has been exceeded. You can only upload {{max}} files for this question", "upperLimit": "Maximum number of files must be less than or equal to {{max}}"}, "maxFileSize": {"negative": "Maximum file size must be greater than or equal to 0", "greaterThanMaximum": "Maximum file size must be less than or equal to 100MB", "fileTooLarge": "File is too large. Maximum file size is {{maxFileSize}}MB"}, "types": {"required": "Allowed file types is required"}, "type": {"invalid": "Invalid file type"}}, "list": {"minLength": "List should contain at least {{min}} element(s)", "maxLength": "List should contain at most {{max}} element(s)"}, "tooltip": "Error(s) on {{$d(ui.terminology.question), lowercase}} properties, click on {{$d(ui.terminology.question), lowercase}} to view the error details"}}, "foundation": {"create": "$d(ui.terminology.foundation) not created, error occurred!", "rename": "$d(ui.terminology.foundation) not renamed, error occurred!", "tooltip": "Error(s) on {{$d(ui.terminology.foundation), lowercase}} properties, click on {{$d(ui.terminology.foundation), lowercase}} to view the error details"}, "configurationFlow": {"clipboard": {"invalidStep": {"heading": "Invalid {{$d(ui.terminology.step), lowercase}} in clipboard", "description": "The {{$d(ui.terminology.step), lowercase}} in the clipboard is invalid. Please copy a valid {{$d(ui.terminology.step), lowercase}}."}}, "unsupportedStep": {"heading": "Unsupported {{$d(ui.terminology.step), lowercase}}", "description": "{{variant, capitalize}}s in {{variant, lowercase}}s coming soon"}, "tooltip": "Error(s) on {{$d(ui.terminology.flow), lowercase}} {{$d(ui.terminology.step), lowercase}} properties, click on {{$d(ui.terminology.flow), lowercase}} {{$d(ui.terminology.step), lowercase}} to view the error details"}, "websocket": {"disconnected": "Disconnected from real-time server", "token": "Failed to get pub sub token"}, "configurationVariables": {"create": "Unable to {{$d(ui.common.crud.create), lowercase}} new {{$d(ui.configuration.variables.terminology.workspaceVariable), lowercase}}. Please try again later.", "update": "Unable to {{$d(ui.common.crud.update), lowercase}} {{$d(ui.configuration.variables.terminology.workspaceVariable), lowercase}} {{variableName}}. Please try again later.", "delete": "Unable to {{$d(ui.common.crud.delete), lowercase}} {{$d(ui.configuration.variables.terminology.workspaceVariable), lowercase}} {{variableName}}. Please try again later.", "fields": {"value": {"notSet": "No value set"}}}, "appSettings": {"apiKeys": {"create": "Unable to {{$d(ui.common.crud.create), lowercase}} {{$d(ui.terminology.apiKey)}}.\nPlease try again later.", "delete": "Unable to {{$d(ui.common.crud.delete), lowercase}} {{$d(ui.terminology.apiKey)}} {{apiKeyName}}.\nPlease try again later."}}}}